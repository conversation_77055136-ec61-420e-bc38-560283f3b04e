from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.owners import owners_bp
from app.forms import OwnerForm, EditOwnerForm
from app.db import query_db, insert_db, update_db, delete_db
from app.decorators import data_entry_required

@owners_bp.route('/')
@login_required
def index():
    """قائمة الملاك مع دعم البحث والفلترة"""
    # الحصول على معلمات البحث والفلترة
    search_query = request.args.get('search', '')
    has_debt = request.args.get('has_debt', '')
    has_bank_info = request.args.get('has_bank_info', '')
    sort_by = request.args.get('sort_by', 'name')
    display_mode = request.args.get('display_mode', 'table')

    # بناء استعلام SQL الأساسي
    base_query = '''
        SELECT o.*,
               (SELECT SUM(c.contract_fee - COALESCE(c.contract_fee_paid, 0))
                FROM contracts c
                JOIN units u ON c.unit_id = u.id
                JOIN buildings b ON u.building_id = b.id
                WHERE b.owner_id = o.id
                  AND c.contract_fee_status != 'paid'
                  AND c.contract_fee > COALESCE(c.contract_fee_paid, 0)) as total_debt
        FROM owners o
        WHERE 1=1
    '''

    # إعداد قائمة المعلمات
    params = []

    # إضافة شروط البحث
    if search_query:
        base_query += " AND (o.name LIKE ? OR o.id_number LIKE ? OR o.phone LIKE ? OR o.email LIKE ?)"
        search_param = f'%{search_query}%'
        params.extend([search_param, search_param, search_param, search_param])

    # إضافة فلتر المديونية
    if has_debt == 'yes':
        base_query += '''
            AND EXISTS (
                SELECT 1
                FROM contracts c
                JOIN units u ON c.unit_id = u.id
                JOIN buildings b ON u.building_id = b.id
                WHERE b.owner_id = o.id
                  AND c.contract_fee_status != 'paid'
                  AND c.contract_fee > COALESCE(c.contract_fee_paid, 0)
            )
        '''
    elif has_debt == 'no':
        base_query += '''
            AND NOT EXISTS (
                SELECT 1
                FROM contracts c
                JOIN units u ON c.unit_id = u.id
                JOIN buildings b ON u.building_id = b.id
                WHERE b.owner_id = o.id
                  AND c.contract_fee_status != 'paid'
                  AND c.contract_fee > COALESCE(c.contract_fee_paid, 0)
            )
        '''

    # إضافة فلتر معلومات البنك
    if has_bank_info == 'yes':
        base_query += " AND (o.bank_name IS NOT NULL AND o.bank_name != '' AND o.bank_iban IS NOT NULL AND o.bank_iban != '')"
    elif has_bank_info == 'no':
        base_query += " AND (o.bank_name IS NULL OR o.bank_name = '' OR o.bank_iban IS NULL OR o.bank_iban = '')"

    # إضافة الترتيب
    if sort_by == 'name':
        base_query += " ORDER BY o.name"
    elif sort_by == 'id_number':
        base_query += " ORDER BY o.id_number"
    elif sort_by == 'debt_desc':
        base_query += " ORDER BY COALESCE(total_debt, 0) DESC"
    elif sort_by == 'debt_asc':
        base_query += " ORDER BY COALESCE(total_debt, 0) ASC"
    else:
        base_query += " ORDER BY o.name"  # الترتيب الافتراضي

    # تنفيذ الاستعلام
    owners = query_db(base_query, params)

    return render_template('owners/index.html',
                          owners=owners,
                          search_query=search_query,
                          has_debt=has_debt,
                          has_bank_info=has_bank_info,
                          sort_by=sort_by,
                          display_mode=display_mode,
                          title='إدارة الملاك')

@owners_bp.route('/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add():
    """إضافة مالك جديد"""
    form = OwnerForm()

    if form.validate_on_submit():
        owner_id = insert_db('owners', {
            'name': form.name.data,
            'id_number': form.id_number.data,
            'phone': form.phone.data,
            'email': form.email.data,
            'birth_date': form.birth_date.data,
            'address': form.address.data,
            'bank_name': form.bank_name.data,
            'bank_iban': form.bank_iban.data,
            'notes': form.notes.data
        })

        flash('تم إضافة المالك بنجاح!', 'success')
        return redirect(url_for('owners.view', owner_id=owner_id))

    return render_template('owners/form.html', form=form, title='إضافة مالك جديد')

@owners_bp.route('/edit/<int:owner_id>', methods=['GET', 'POST'])
@login_required
@data_entry_required
def edit(owner_id):
    """تعديل مالك"""
    owner = query_db('SELECT * FROM owners WHERE id = ?', (owner_id,), one=True)

    if not owner:
        flash('المالك غير موجود.', 'danger')
        return redirect(url_for('owners.index'))

    form = EditOwnerForm()

    if request.method == 'GET':
        form.id.data = owner['id']
        form.name.data = owner['name']
        form.id_number.data = owner['id_number']
        form.phone.data = owner['phone']
        form.email.data = owner['email']
        form.birth_date.data = owner['birth_date']
        form.address.data = owner['address']
        form.bank_name.data = owner['bank_name']
        form.bank_iban.data = owner['bank_iban']
        form.notes.data = owner['notes']

    if form.validate_on_submit():
        update_db('owners', owner_id, {
            'name': form.name.data,
            'id_number': form.id_number.data,
            'phone': form.phone.data,
            'email': form.email.data,
            'birth_date': form.birth_date.data,
            'address': form.address.data,
            'bank_name': form.bank_name.data,
            'bank_iban': form.bank_iban.data,
            'notes': form.notes.data
        })

        flash('تم تحديث بيانات المالك بنجاح!', 'success')
        return redirect(url_for('owners.view', owner_id=owner_id))

    return render_template('owners/form.html', form=form, title='تعديل بيانات المالك', owner=owner)

@owners_bp.route('/view/<int:owner_id>')
@login_required
def view(owner_id):
    """عرض تفاصيل المالك"""
    owner = query_db('SELECT * FROM owners WHERE id = ?', (owner_id,), one=True)

    if not owner:
        flash('المالك غير موجود.', 'danger')
        return redirect(url_for('owners.index'))

    # الحصول على المباني التابعة للمالك
    buildings = query_db('SELECT * FROM buildings WHERE owner_id = ? ORDER BY name', (owner_id,))

    # الحصول على المستندات المرتبطة بالمالك
    documents = query_db('''
        SELECT d.*, u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.related_to = 'owner' AND d.related_id = ?
        ORDER BY d.upload_date DESC
    ''', (owner_id,))

    # الحصول على المعاملات المالية المرتبطة بالمالك
    transactions = query_db('''
        SELECT t.*, u.name as created_by_name
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        WHERE t.related_to = 'owner' AND t.related_id = ?
        ORDER BY t.transaction_date DESC
    ''', (owner_id,))

    # الحصول على معلومات المديونية الخاصة برسوم العقود
    contract_debts = query_db('''
        SELECT c.id, c.contract_number, c.contract_fee, c.contract_fee_paid, c.contract_fee_status,
               c.start_date, c.end_date, t.name as tenant_name, u.unit_number, b.name as building_name,
               (c.contract_fee - COALESCE(c.contract_fee_paid, 0)) as remaining_fee
        FROM contracts c
        JOIN units u ON c.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        JOIN tenants t ON c.tenant_id = t.id
        WHERE b.owner_id = ? AND c.contract_fee_status != 'paid' AND c.contract_fee > COALESCE(c.contract_fee_paid, 0)
        ORDER BY c.start_date DESC
    ''', (owner_id,))

    # حساب إجمالي المديونية
    total_debt = 0
    for debt in contract_debts:
        total_debt += debt['remaining_fee']

    return render_template('owners/view.html',
                           owner=owner,
                           buildings=buildings,
                           documents=documents,
                           transactions=transactions,
                           contract_debts=contract_debts,
                           total_debt=total_debt,
                           title=f'تفاصيل المالك: {owner["name"]}')

@owners_bp.route('/delete/<int:owner_id>', methods=['POST'])
@login_required
@data_entry_required
def delete(owner_id):
    """حذف مالك"""
    owner = query_db('SELECT * FROM owners WHERE id = ?', (owner_id,), one=True)

    if not owner:
        flash('المالك غير موجود.', 'danger')
        return redirect(url_for('owners.index'))

    # التحقق من وجود مباني مرتبطة بالمالك
    buildings = query_db('SELECT COUNT(*) as count FROM buildings WHERE owner_id = ?', (owner_id,), one=True)

    if buildings['count'] > 0:
        flash('لا يمكن حذف المالك لأنه مرتبط بمباني. يرجى حذف المباني أولاً.', 'danger')
        return redirect(url_for('owners.view', owner_id=owner_id))

    # حذف المستندات المرتبطة بالمالك
    delete_db('documents', owner_id)

    # حذف المالك
    delete_db('owners', owner_id)

    flash('تم حذف المالك بنجاح!', 'success')
    return redirect(url_for('owners.index'))

@owners_bp.route('/api/get_owner_details/<int:owner_id>')
@login_required
def get_owner_details(owner_id):
    """الحصول على بيانات المالك"""
    owner = query_db('SELECT * FROM owners WHERE id = ?', (owner_id,), one=True)

    if not owner:
        return jsonify({'error': 'المالك غير موجود'}), 404

    return jsonify({
        'id': owner['id'],
        'name': owner['name'],
        'id_number': owner['id_number'],
        'phone': owner['phone'],
        'email': owner['email'],
        'birth_date': owner['birth_date'],
        'address': owner['address'],
        'bank_name': owner['bank_name'],
        'bank_iban': owner['bank_iban']
    })
