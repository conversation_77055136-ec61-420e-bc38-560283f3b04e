#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إنشاء النسخة المحمولة لمكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path

def create_portable_version():
    """إنشاء نسخة محمولة من التطبيق"""

    print("=" * 60)
    print("إنشاء النسخة المحمولة لمكتب عصام الفت")
    print("=" * 60)

    # تحديد المجلد الحالي
    current_dir = Path(__file__).parent

    # تحديد مجلد الإخراج
    output_dir = current_dir / "portable_version"

    # إنشاء مجلد الإخراج
    if output_dir.exists():
        print("حذف النسخة السابقة...")
        shutil.rmtree(output_dir)

    output_dir.mkdir(exist_ok=True)
    print(f"إنشاء مجلد الإخراج: {output_dir}")

    # قائمة الملفات والمجلدات المطلوبة
    required_items = [
        "app",
        "config.py",
        "requirements.txt",
        "run.py",
        "schema.sql",
        "server_manager.py",
        "fix_paths.py",
        "monkey_patch.py",
        "portable_config.py",
        "run_portable.py",
        "pyinstaller_config.py",
        "file.ico",
        "file.jpeg",
        "start_server.bat",
        "start_server_gui.bat",
        "start_server_network.bat",
        "quick_start.bat",
        "تشغيل_النظام.bat",
        "دليل_المستخدم.md",
        "README.md",
        "project_info.json"
    ]

    # نسخ الملفات والمجلدات
    print("نسخ ملفات التطبيق...")
    for item in required_items:
        source = current_dir / item
        dest = output_dir / item

        if source.exists():
            if source.is_file():
                shutil.copy2(source, dest)
                print(f"  ✓ نسخ ملف: {item}")
            elif source.is_dir():
                shutil.copytree(source, dest)
                print(f"  ✓ نسخ مجلد: {item}")
        else:
            print(f"  ⚠ لم يتم العثور على: {item}")

    # إنشاء مجلدات إضافية
    additional_dirs = [
        "instance",
        "uploads",
        "uploads/buildings",
        "uploads/contracts",
        "uploads/documents",
        "uploads/owners",
        "uploads/tenants",
        "uploads/transactions"
    ]

    print("إنشاء المجلدات الإضافية...")
    for dir_name in additional_dirs:
        dir_path = output_dir / dir_name
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"  ✓ إنشاء مجلد: {dir_name}")

    # إنشاء ملف README
    readme_content = """
# مكتب عصام الفت لإدارة الأملاك - النسخة المحمولة

## تم التطوير والبرمجة بواسطة شركة المبرمج المصري
- فيسبوك: https://www.facebook.com/almbarmg
- واتساب: 0201032540807
- جميع الحقوق محفوظة © 2025

## متطلبات التشغيل
- Python 3.8 أو أحدث
- نظام التشغيل Windows

## طريقة التشغيل

### 1. تشغيل الخادم المحلي (وحدة التحكم)
انقر نقراً مزدوجاً على: `start_server.bat`

### 2. تشغيل الواجهة الرسومية
انقر نقراً مزدوجاً على: `start_server_gui.bat`

### 3. تشغيل خادم الشبكة
انقر نقراً مزدوجاً على: `start_server_network.bat`

## بيانات الدخول الافتراضية
- اسم المستخدم: admin
- كلمة المرور: admin123

## ملاحظات مهمة
- تأكد من تثبيت Python على النظام
- في حالة عدم وجود Python، يمكن تحميله من: https://python.org
- يتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
- جميع البيانات محفوظة في مجلد instance
- الملفات المرفوعة محفوظة في مجلد uploads

## الدعم الفني
للحصول على الدعم الفني، يرجى التواصل معنا:
- واتساب: 0201032540807
- فيسبوك: https://www.facebook.com/almbarmg
"""

    readme_path = output_dir / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("  ✓ إنشاء ملف README.md")

    # إنشاء ملف تثبيت المتطلبات
    install_requirements_content = """@echo off
chcp 65001 >nul
title تثبيت متطلبات مكتب عصام الفت

echo ================================================================
echo                    تثبيت متطلبات التطبيق
echo ================================================================
echo.

REM تحديد مجلد العمل الحالي
cd /d "%~dp0"

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo تم تثبيت المتطلبات بنجاح!
echo يمكنك الآن تشغيل التطبيق باستخدام أحد ملفات التشغيل
pause
"""

    # إنشاء ملف تشغيل محسن للنسخة المحمولة
    run_portable_content = """@echo off
chcp 65001 >nul
title مكتب عصام الفت - النسخة المحمولة

echo ================================================================
echo                    مكتب عصام الفت لإدارة الأملاك
echo                           النسخة المحمولة
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

REM تحديد مجلد العمل الحالي
cd /d "%~dp0"

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    echo أو استخدام النسخة التنفيذية من التطبيق
    pause
    exit /b 1
)

REM تثبيت المتطلبات تلقائياً
echo التحقق من المتطلبات...
pip install -r requirements.txt --quiet

REM تشغيل النسخة المحمولة
echo بدء تشغيل النسخة المحمولة...
echo.
python run_portable.py

pause
"""

    install_path = output_dir / "install_requirements.bat"
    with open(install_path, 'w', encoding='utf-8') as f:
        f.write(install_requirements_content)
    print("  ✓ إنشاء ملف install_requirements.bat")

    # إنشاء ملف التشغيل المحسن
    run_portable_path = output_dir / "run_portable.bat"
    with open(run_portable_path, 'w', encoding='utf-8') as f:
        f.write(run_portable_content)
    print("  ✓ إنشاء ملف run_portable.bat")

    # إنشاء ملف zip للنسخة المحمولة
    print("إنشاء ملف ZIP...")
    zip_path = current_dir / "مكتب_عصام_الفت_النسخة_المحمولة.zip"

    if zip_path.exists():
        zip_path.unlink()

    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(output_dir)
                zipf.write(file_path, arc_name)

    print(f"  ✓ تم إنشاء ملف ZIP: {zip_path}")

    print("=" * 60)
    print("تم إنشاء النسخة المحمولة بنجاح!")
    print(f"المجلد: {output_dir}")
    print(f"ملف ZIP: {zip_path}")
    print("=" * 60)

    return True

if __name__ == "__main__":
    try:
        create_portable_version()
        print("العملية مكتملة بنجاح!")
    except Exception as e:
        print(f"خطأ في إنشاء النسخة المحمولة: {str(e)}")
        import traceback
        traceback.print_exc()

    input("اضغط Enter للخروج...")
