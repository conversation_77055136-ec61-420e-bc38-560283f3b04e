#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إنشاء الملف التنفيذي لمكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ تم تثبيت PyInstaller بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("✗ فشل في تثبيت PyInstaller")
        return False

def build_executable():
    """إنشاء الملف التنفيذي"""

    print("=" * 60)
    print("إنشاء الملف التنفيذي لمكتب عصام الفت")
    print("=" * 60)

    # تحديد المجلد الحالي
    current_dir = Path(__file__).parent

    # التحقق من وجود PyInstaller
    try:
        import PyInstaller
        print("✓ PyInstaller متوفر")
    except ImportError:
        print("PyInstaller غير مثبت. جاري التثبيت...")
        if not install_pyinstaller():
            return False

    # تحديد مجلد الإخراج
    dist_dir = current_dir / "dist"
    build_dir = current_dir / "build"

    # حذف المجلدات السابقة
    if dist_dir.exists():
        print("حذف مجلد dist السابق...")
        shutil.rmtree(dist_dir)

    if build_dir.exists():
        print("حذف مجلد build السابق...")
        shutil.rmtree(build_dir)

    # استيراد إعدادات PyInstaller المحسنة
    try:
        from pyinstaller_config import get_spec_content
        spec_content = get_spec_content()
        print("✓ تم تحميل إعدادات PyInstaller المحسنة")
    except ImportError:
        print("⚠ لم يتم العثور على pyinstaller_config.py، استخدام الإعدادات الافتراضية")
        # إعدادات افتراضية مبسطة
        spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['server_manager.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('app', 'app'),
        ('config.py', '.'),
        ('schema.sql', '.'),
        ('fix_paths.py', '.'),
        ('monkey_patch.py', '.'),
        ('file.ico', '.'),
        ('file.jpeg', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'flask', 'flask_login', 'flask_bcrypt', 'flask_wtf', 'flask_mail',
        'werkzeug', 'jinja2', 'itsdangerous', 'email_validator',
        'qrcode', 'PyQt5', 'bcrypt', 'sqlite3', 'PIL'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'pandas'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='مكتب_عصام_الفت',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='file.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=['vcruntime140.dll', 'msvcp140.dll'],
    name='مكتب_عصام_الفت',
)
'''

    spec_path = current_dir / "server_manager.spec"
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ تم إنشاء ملف spec")

    # تشغيل PyInstaller
    print("بدء إنشاء الملف التنفيذي...")
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            str(spec_path)
        ]

        subprocess.check_call(cmd)
        print("✓ تم إنشاء الملف التنفيذي بنجاح")

    except subprocess.CalledProcessError as e:
        print(f"✗ فشل في إنشاء الملف التنفيذي: {e}")
        return False

    # نسخ الملفات الإضافية
    exe_dir = dist_dir / "مكتب_عصام_الفت"
    if exe_dir.exists():
        print("نسخ الملفات الإضافية...")

        # إنشاء المجلدات المطلوبة
        dirs_to_create = [
            "instance",
            "uploads",
            "uploads/buildings",
            "uploads/contracts",
            "uploads/documents",
            "uploads/owners",
            "uploads/tenants",
            "uploads/transactions"
        ]

        for dir_name in dirs_to_create:
            dir_path = exe_dir / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)

        # نسخ ملفات التشغيل
        batch_files = [
            "start_server.bat",
            "start_server_gui.bat",
            "start_server_network.bat"
        ]

        for batch_file in batch_files:
            source = current_dir / batch_file
            if source.exists():
                # تعديل محتوى ملف batch للعمل مع الملف التنفيذي
                with open(source, 'r', encoding='utf-8') as f:
                    content = f.read()

                # استبدال python server_manager.py بالملف التنفيذي
                content = content.replace(
                    'python server_manager.py',
                    'مكتب_عصام_الفت.exe'
                )

                # إزالة التحقق من Python والمتطلبات
                lines = content.split('\n')
                new_lines = []
                skip_section = False

                for line in lines:
                    if 'التحقق من وجود Python' in line:
                        skip_section = True
                    elif 'تشغيل' in line and ('الخادم' in line or 'الواجهة' in line):
                        skip_section = False
                        new_lines.append(line)
                    elif not skip_section:
                        new_lines.append(line)

                content = '\n'.join(new_lines)

                dest = exe_dir / batch_file
                with open(dest, 'w', encoding='utf-8') as f:
                    f.write(content)

                print(f"  ✓ نسخ وتعديل: {batch_file}")

        # إنشاء ملف README للنسخة التنفيذية
        readme_content = """
# مكتب عصام الفت لإدارة الأملاك - النسخة التنفيذية

## تم التطوير والبرمجة بواسطة شركة المبرمج المصري
- فيسبوك: https://www.facebook.com/almbarmg
- واتساب: 0201032540807
- جميع الحقوق محفوظة © 2025

## طريقة التشغيل

### 1. تشغيل الخادم المحلي (وحدة التحكم)
انقر نقراً مزدوجاً على: `start_server.bat`

### 2. تشغيل الواجهة الرسومية
انقر نقراً مزدوجاً على: `start_server_gui.bat`

### 3. تشغيل خادم الشبكة
انقر نقراً مزدوجاً على: `start_server_network.bat`

### 4. تشغيل مباشر
انقر نقراً مزدوجاً على: `مكتب_عصام_الفت.exe`

## بيانات الدخول الافتراضية
- اسم المستخدم: admin
- كلمة المرور: admin123

## ملاحظات مهمة
- لا يتطلب تثبيت Python
- يعمل على أي نظام Windows
- يتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول
- جميع البيانات محفوظة في مجلد instance
- الملفات المرفوعة محفوظة في مجلد uploads

## الدعم الفني
للحصول على الدعم الفني، يرجى التواصل معنا:
- واتساب: 0201032540807
- فيسبوك: https://www.facebook.com/almbarmg
"""

        readme_path = exe_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("  ✓ إنشاء ملف README.md")

    # تنظيف الملفات المؤقتة
    if spec_path.exists():
        spec_path.unlink()

    print("=" * 60)
    print("تم إنشاء الملف التنفيذي بنجاح!")
    print(f"المجلد: {exe_dir}")
    print("=" * 60)

    return True

if __name__ == "__main__":
    try:
        build_executable()
        print("العملية مكتملة بنجاح!")
    except Exception as e:
        print(f"خطأ في إنشاء الملف التنفيذي: {str(e)}")
        import traceback
        traceback.print_exc()

    input("اضغط Enter للخروج...")
