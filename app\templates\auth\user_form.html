{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {% if form.username.errors %}
                            {{ form.username(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.username(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {% if form.name.errors %}
                            {{ form.name(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.name(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {% if form.email.errors %}
                            {{ form.email(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {% if form.password.errors %}
                            {{ form.password(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.password(class="form-control") }}
                        {% endif %}
                        {% if user %}
                        <small class="form-text text-muted">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير كلمة المرور.</small>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.confirm_password.label(class="form-label") }}
                        {% if form.confirm_password.errors %}
                            {{ form.confirm_password(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.confirm_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.confirm_password(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.role.label(class="form-label") }}
                        {% if form.role.errors %}
                            {{ form.role(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.role.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.role(class="form-select") }}
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.users') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
