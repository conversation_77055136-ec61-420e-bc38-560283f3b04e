{% extends "base.html" %}

{% block title %}تفاصيل العقد - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">عقد إيجار #{{ contract.contract_number or contract.id }}</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('tenants.edit_contract', contract_id=contract.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('tenants.contracts') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع
        </a>

    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-contract me-2"></i>بيانات العقد
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>رقم العقد</th>
                            <td>{{ contract.contract_number or contract.id }}</td>
                        </tr>
                        <tr>
                            <th>المستأجر</th>
                            <td>
                                <a href="{{ url_for('tenants.view', tenant_id=contract.tenant_id) }}">
                                    {{ contract.tenant_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>الوحدة</th>
                            <td>
                                <a href="{{ url_for('properties.view_unit', unit_id=contract.unit_id) }}">
                                    {{ contract.unit_number }} - {{ contract.building_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ البداية</th>
                            <td>{{ contract.start_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ النهاية</th>
                            <td>{{ contract.end_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>المدة</th>
                            <td>{{ contract_duration }} شهر</td>
                        </tr>
                        <tr>
                            <th>قيمة الإيجار</th>
                            <td>{{ contract.rent_amount|format_currency }}</td>
                        </tr>
                        <tr>
                            <th>دورة الدفع</th>
                            <td>
                                {% if contract.payment_frequency == 'monthly' %}
                                شهري
                                {% elif contract.payment_frequency == 'quarterly' %}
                                ربع سنوي
                                {% elif contract.payment_frequency == 'biannual' %}
                                نصف سنوي
                                {% elif contract.payment_frequency == 'annual' %}
                                سنوي
                                {% elif contract.payment_frequency == 'one_payment' %}
                                دفعة واحدة
                                {% elif contract.payment_frequency == 'two_payments' %}
                                دفعتين
                                {% elif contract.payment_frequency == 'three_payments' %}
                                ثلاث دفعات
                                {% elif contract.payment_frequency == 'four_payments' %}
                                أربع دفعات
                                {% else %}
                                {{ contract.payment_frequency }}
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>قيمة التأمين</th>
                            <td>{{ contract.deposit_amount|format_currency if contract.deposit_amount else 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <th>رسوم العقد (على المالك)</th>
                            <td>{{ contract.contract_fee|format_currency if contract.contract_fee else 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <th>المدفوع من رسوم العقد</th>
                            <td>{{ contract.contract_fee_paid|format_currency if contract.contract_fee_paid else '0' }}</td>
                        </tr>
                        <tr>
                            <th>المتبقي من رسوم العقد</th>
                            <td>{{ (contract.contract_fee - (contract.contract_fee_paid or 0))|format_currency if contract.contract_fee else '0' }}</td>
                        </tr>
                        <tr>
                            <th>حالة دفع الرسوم</th>
                            <td>
                                {% if contract.contract_fee_status == 'paid' %}
                                <span class="badge bg-success">مدفوعة بالكامل</span>
                                {% elif contract.contract_fee_status == 'partially_paid' %}
                                <span class="badge bg-warning">مدفوعة جزئياً</span>
                                {% elif contract.contract_fee_status == 'unpaid' %}
                                <span class="badge bg-danger">غير مدفوعة</span>
                                {% else %}
                                <span class="badge bg-secondary">غير محدد</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                {% if contract.status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                                {% elif contract.status == 'expired' %}
                                <span class="badge bg-danger">منتهي</span>
                                {% elif contract.status == 'terminated' %}
                                <span class="badge bg-warning">ملغي</span>
                                {% elif contract.status == 'renewed' %}
                                <span class="badge bg-info">مجدد</span>
                                {% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        {% if contract.status == 'active' %}
        <div class="card mt-3">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>إجراءات العقد
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('tenants.edit_contract', contract_id=contract.id) }}" class="btn btn-success">
                        <i class="fas fa-sync-alt me-1"></i>تجديد العقد
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>شروط وأحكام العقد
                </h5>
            </div>
            <div class="card-body">
                <div class="terms-content">
                    {% if contract.terms %}
                    <p>{{ contract.terms|nl2br }}</p>
                    {% else %}
                    <p>لا توجد شروط محددة لهذا العقد.</p>
                    {% endif %}
                </div>

                {% if contract.notes %}
                <div class="mt-4">
                    <h6>ملاحظات إضافية:</h6>
                    <p>{{ contract.notes|nl2br }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>سجل المدفوعات
                </h5>
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الوصف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.transaction_date|format_date }}</td>
                                <td>{{ payment.amount|format_currency }}</td>
                                <td>
                                    {% if payment.payment_method == 'cash' %}
                                    نقدي
                                    {% elif payment.payment_method == 'bank_transfer' %}
                                    تحويل بنكي
                                    {% elif payment.payment_method == 'check' %}
                                    شيك
                                    {% elif payment.payment_method == 'credit_card' %}
                                    بطاقة ائتمان
                                    {% else %}
                                    {{ payment.payment_method }}
                                    {% endif %}
                                </td>
                                <td>{{ payment.description or 'غير محدد' }}</td>
                                <td>
                                    <a href="{{ url_for('finance.view_transaction', transaction_id=payment.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مدفوعات مسجلة لهذا العقد.
                </div>
                {% endif %}

                {% if contract.status == 'active' %}
                <div class="mt-3">
                    <a href="{{ url_for('finance.add_transaction') }}?related_to=contract&related_id={{ contract.id }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>تسجيل دفعة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>المستندات
                </h5>
            </div>
            <div class="card-body">
                {% if documents %}
                <div class="row">
                    {% for document in documents %}
                    <div class="col-md-3 mb-3">
                        <div class="card document-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ document.title }}</h5>
                                <p class="card-text text-muted">
                                    <small>
                                        <i class="fas fa-calendar-alt me-1"></i>{{ document.upload_date|format_date }}
                                    </small>
                                </p>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>تنزيل
                                    </a>
                                    <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مستندات مرفقة لهذا العقد.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('documents.add') }}?related_to=contract&related_id={{ contract.id }}" class="btn btn-info">
                        <i class="fas fa-plus-circle me-1"></i>إضافة مستند جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
