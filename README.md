# مكتب عصام الفت لإدارة الأملاك

<div align="center">

![Logo](file.jpeg)

**نظام شامل لإدارة العقارات والمستأجرين والمعاملات المالية**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](#)

</div>

---

## 🏢 نظرة عامة

**مكتب عصام الفت لإدارة الأملاك** هو نظام متكامل ومتطور لإدارة العقارات، تم تطويره خصيصاً لمكاتب إدارة الأملاك في المملكة العربية السعودية. يوفر النظام حلولاً شاملة لإدارة الملاك والمباني والمستأجرين والمعاملات المالية.

### ✨ المميزات الرئيسية

- 🏠 **إدارة العقارات**: إدارة شاملة للمباني والوحدات
- 👥 **إدارة المستأجرين**: متابعة المستأجرين والعقود
- 💰 **النظام المالي**: إدارة الإيرادات والمصروفات
- 📄 **إدارة المستندات**: رفع وتنظيم الملفات
- 📊 **التقارير**: تقارير مالية وإحصائية شاملة
- 🔔 **التنبيهات**: تنبيهات انتهاء العقود والمدفوعات
- 🛠️ **طلبات الصيانة**: إدارة طلبات الصيانة والمتابعة
- 📱 **واجهة عربية**: واجهة مستخدم باللغة العربية بالكامل

---

## 🚀 التشغيل السريع

### 1. التشغيل الفوري
```bash
# انقر نقراً مزدوجاً على:
quick_start.bat
```

### 2. تشغيل الخادم المحلي
```bash
start_server.bat
```

### 3. تشغيل الواجهة الرسومية
```bash
start_server_gui.bat
```

### 4. تشغيل خادم الشبكة
```bash
start_server_network.bat
```

---

## 📋 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 7/8/10/11
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 2 جيجابايت RAM
- **مساحة القرص**: 500 ميجابايت

### المُوصى به
- **نظام التشغيل**: Windows 10/11
- **Python**: 3.11 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM
- **مساحة القرص**: 1 جيجابايت

---

## 🔧 التثبيت والإعداد

### 1. تثبيت Python
قم بتحميل وتثبيت Python من [python.org](https://python.org)

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python server_manager.py
```

---

## 📦 إنشاء النسخ المختلفة

### إنشاء جميع النسخ تلقائياً
```bash
create_all_versions.bat
```

### إنشاء النسخة المحمولة
```bash
python create_portable_version.py
```

### إنشاء النسخة التنفيذية
```bash
python build_executable.py
```

---

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

> ⚠️ **تنبيه**: يُنصح بشدة بتغيير كلمة المرور بعد التشغيل الأول

---

## 🏗️ هيكل المشروع

```
مكتب_عصام_الفت/
├── app/                    # كود التطبيق الرئيسي
│   ├── auth/              # نظام المصادقة
│   ├── dashboard/         # لوحة التحكم
│   ├── owners/            # إدارة الملاك
│   ├── properties/        # إدارة العقارات
│   ├── tenants/           # إدارة المستأجرين
│   ├── finance/           # النظام المالي
│   ├── documents/         # إدارة المستندات
│   ├── reports/           # التقارير
│   ├── static/            # الملفات الثابتة
│   └── templates/         # قوالب HTML
├── instance/              # قاعدة البيانات
├── uploads/               # الملفات المرفوعة
├── config.py              # إعدادات التطبيق
├── schema.sql             # مخطط قاعدة البيانات
├── server_manager.py      # مدير الخادم
└── requirements.txt       # المتطلبات
```

---

## 🌐 خيارات التشغيل

### وضع وحدة التحكم
```bash
python server_manager.py --console
```

### وضع الشبكة
```bash
python server_manager.py --network
```

### منفذ مخصص
```bash
python server_manager.py --port 8080
```

### وضع التطوير
```bash
python server_manager.py --debug
```

---

## 📊 الوحدات الرئيسية

### 1. لوحة التحكم
- إحصائيات شاملة
- رسوم بيانية للإيرادات والمصروفات
- العقود المنتهية الصلاحية قريباً

### 2. إدارة الملاك
- إضافة وتعديل بيانات الملاك
- ربط الملاك بالمباني
- تتبع معلومات الاتصال

### 3. إدارة العقارات
- إدارة المباني والوحدات
- تتبع حالة الوحدات
- قراءات العدادات
- طلبات الصيانة

### 4. إدارة المستأجرين
- بيانات المستأجرين الشاملة
- إدارة العقود
- تجديد وإنهاء العقود
- تتبع المدفوعات

### 5. النظام المالي
- تسجيل المعاملات المالية
- تقارير الإيرادات والمصروفات
- متابعة المدفوعات المستحقة
- إيصالات الدفع

### 6. إدارة المستندات
- رفع وتنظيم الملفات
- ربط المستندات بالعقود والوحدات
- عارض PDF مدمج
- نظام بحث متقدم

---

## 🔒 الأمان والصلاحيات

### أنواع المستخدمين
1. **مدير** (admin): صلاحيات كاملة
2. **محاسب** (accountant): إدارة مالية ومحاسبية
3. **مدخل بيانات** (data_entry): إدخال وتعديل البيانات

### ميزات الأمان
- تشفير كلمات المرور باستخدام bcrypt
- حماية CSRF
- جلسات آمنة
- تسجيل العمليات

---

## 💾 النسخ الاحتياطي

### المجلدات المهمة
- `instance/` - قاعدة البيانات والإعدادات
- `uploads/` - الملفات المرفوعة

### التوصيات
- عمل نسخة احتياطية يومية
- حفظ النسخ في مكان آمن
- اختبار استعادة النسخ دورياً

---

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### المنفذ مشغول
```bash
python server_manager.py --port 8080
```

#### مشاكل قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm instance/realestate.db
python server_manager.py
```

#### إعادة تعيين كلمة مرور المدير
```bash
python server_manager.py --reset-admin-password
```

---

## 📞 الدعم الفني

### تم التطوير والبرمجة بواسطة شركة المبرمج المصري

- 📱 **واتساب**: [0201032540807](https://wa.me/201032540807)
- 📘 **فيسبوك**: [almbarmg](https://www.facebook.com/almbarmg)
- 🕒 **ساعات الدعم**: الأحد إلى الخميس (9:00 ص - 6:00 م)

### عند طلب الدعم يرجى تقديم:
1. وصف المشكلة بالتفصيل
2. رسائل الخطأ (إن وجدت)
3. نظام التشغيل المستخدم
4. إصدار Python
5. خطوات إعادة إنتاج المشكلة

---

## 📄 الترخيص

**جميع الحقوق محفوظة © 2025 - شركة المبرمج المصري**

هذا البرنامج محمي بحقوق الطبع والنشر. يُمنع نسخه أو توزيعه أو تعديله بدون إذن كتابي من المطور.

---

## 🔄 التحديثات

للحصول على آخر التحديثات والميزات الجديدة، يرجى التواصل معنا عبر:
- واتساب: 0201032540807
- فيسبوك: https://www.facebook.com/almbarmg

---

<div align="center">

**شكراً لاستخدام مكتب عصام الفت لإدارة الأملاك**

*تم تطويره بـ ❤️ بواسطة شركة المبرمج المصري*

</div>
