{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">{{ document.title }}</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('documents.display_file', document_id=document.id) }}" class="btn btn-primary" target="_blank">
            <i class="fas fa-eye me-1"></i>عرض
        </a>
        <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-success">
            <i class="fas fa-download me-1"></i>تنزيل
        </a>
        <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <button type="submit" class="btn btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                <i class="fas fa-trash-alt me-1"></i>حذف
            </button>
        </form>
        <a href="{{ url_for('documents.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>تفاصيل المستند
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>العنوان</th>
                            <td>{{ document.title }}</td>
                        </tr>
                        <tr>
                            <th>نوع الملف</th>
                            <td>{{ document.file_path.split('.')[-1].upper() }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الرفع</th>
                            <td>{{ document.upload_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>تم الرفع بواسطة</th>
                            <td>{{ document.uploaded_by_name }}</td>
                        </tr>
                        <tr>
                            <th>متعلق بـ</th>
                            <td>
                                {% if document.related_to == 'owner' %}
                                <a href="{{ url_for('owners.view', owner_id=document.related_id) }}">
                                    مالك: {{ document.related_name }}
                                </a>
                                {% elif document.related_to == 'building' %}
                                <a href="{{ url_for('properties.view_building', building_id=document.related_id) }}">
                                    مبنى: {{ document.related_name }}
                                </a>
                                {% elif document.related_to == 'unit' %}
                                <a href="{{ url_for('properties.view_unit', unit_id=document.related_id) }}">
                                    وحدة: {{ document.related_name }}
                                </a>
                                {% elif document.related_to == 'tenant' %}
                                <a href="{{ url_for('tenants.view', tenant_id=document.related_id) }}">
                                    مستأجر: {{ document.related_name }}
                                </a>
                                {% elif document.related_to == 'contract' %}
                                <a href="{{ url_for('tenants.view_contract', contract_id=document.related_id) }}">
                                    عقد: {{ document.related_name }}
                                </a>
                                {% elif document.related_to == 'transaction' %}
                                <a href="{{ url_for('finance.view_transaction', transaction_id=document.related_id) }}">
                                    معاملة مالية: {{ document.related_name }}
                                </a>
                                {% else %}
                                غير مرتبط
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ document.notes or 'لا توجد ملاحظات' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>معاينة
                </h5>
            </div>
            <div class="card-body text-center">
                {% set file_ext = document.file_path.split('.')[-1].lower() %}

                {% if file_ext in ['jpg', 'jpeg', 'png', 'gif'] %}
                <div class="mb-3">
                    <img src="{{ url_for('documents.serve_file', document_id=document.id) }}" class="img-fluid" alt="{{ document.title }}">
                </div>
                <div class="mt-2">
                    <a href="{{ url_for('documents.display_file', document_id=document.id) }}" class="btn btn-sm btn-success" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>فتح الصورة في صفحة منفصلة
                    </a>
                </div>
                {% elif file_ext == 'pdf' %}
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>لا يمكن معاينة ملفات PDF هنا. يرجى استخدام زر "عرض" أدناه.
                </div>
                <div class="mt-2">
                    <a href="{{ url_for('documents.display_file', document_id=document.id) }}" class="btn btn-sm btn-success" target="_blank">
                        <i class="fas fa-file-pdf me-1"></i>عرض ملف PDF
                    </a>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>لا يمكن معاينة هذا النوع من الملفات. يرجى تنزيل الملف لعرضه.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function openFileDirectly(filename) {
        // الحصول على المسار المطلق للملف
        var uploadFolder = "{{ config['UPLOAD_FOLDER'] }}";
        var filePath = uploadFolder + "/" + filename;

        // إنشاء رابط مباشر للملف باستخدام بروتوكول file://
        // استخدام المسار المطلق للملف
        var absolutePath = "H:/مشاريع 2025/mktb-akret-3sam/" + uploadFolder + "/" + filename;
        var fileUrl = "file:///" + absolutePath.replace(/\\/g, '/');

        console.log("Opening file: " + fileUrl);

        // إنشاء عنصر a وتعيين خصائصه
        var link = document.createElement('a');
        link.href = fileUrl;
        link.target = '_blank';

        // إضافة العنصر إلى الصفحة وتنفيذ النقر عليه
        document.body.appendChild(link);
        link.click();

        // إزالة العنصر من الصفحة
        document.body.removeChild(link);
    }
</script>
{% endblock %}