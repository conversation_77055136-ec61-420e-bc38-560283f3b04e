from flask import render_template
from flask_login import login_required, current_user
from app.dashboard import dashboard_bp
from app.db import query_db
from app.utils import get_expiring_contracts, get_monthly_income, get_monthly_expense, get_yearly_income_by_month, get_yearly_expense_by_month
from datetime import datetime

@dashboard_bp.route('/')
@login_required
def index():
    """الصفحة الرئيسية للوحة التحكم"""
    # الحصول على الإحصائيات
    owners_count = query_db('SELECT COUNT(*) as count FROM owners', one=True)['count']
    buildings_count = query_db('SELECT COUNT(*) as count FROM buildings', one=True)['count']
    units_count = query_db('SELECT COUNT(*) as count FROM units', one=True)['count']
    tenants_count = query_db('SELECT COUNT(*) as count FROM tenants', one=True)['count']
    
    # الحصول على عدد العقود النشطة
    active_contracts_count = query_db('SELECT COUNT(*) as count FROM contracts WHERE status = "active"', one=True)['count']
    
    # الحصول على الإيرادات والمصروفات للشهر الحالي
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_income = get_monthly_income(current_year, current_month)
    monthly_expense = get_monthly_expense(current_year, current_month)
    net_profit = monthly_income - monthly_expense
    
    # الحصول على العقود التي ستنتهي قريبًا
    expiring_contracts = get_expiring_contracts()
    
    # الحصول على بيانات الرسم البياني للإيرادات والمصروفات السنوية
    yearly_income = get_yearly_income_by_month(current_year)
    yearly_expense = get_yearly_expense_by_month(current_year)
    
    # الحصول على آخر المعاملات المالية
    latest_transactions = query_db('''
        SELECT t.*, u.name as created_by_name 
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        ORDER BY t.transaction_date DESC
        LIMIT 5
    ''')
    
    # الحصول على آخر طلبات الصيانة
    latest_maintenance = query_db('''
        SELECT m.*, u.unit_number, b.name as building_name
        FROM maintenance_requests m
        JOIN units u ON m.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        ORDER BY m.request_date DESC
        LIMIT 5
    ''')
    
    return render_template('dashboard/index.html', 
                           title='لوحة التحكم',
                           owners_count=owners_count,
                           buildings_count=buildings_count,
                           units_count=units_count,
                           tenants_count=tenants_count,
                           active_contracts_count=active_contracts_count,
                           monthly_income=monthly_income,
                           monthly_expense=monthly_expense,
                           net_profit=net_profit,
                           expiring_contracts=expiring_contracts,
                           yearly_income=yearly_income,
                           yearly_expense=yearly_expense,
                           latest_transactions=latest_transactions,
                           latest_maintenance=latest_maintenance)
