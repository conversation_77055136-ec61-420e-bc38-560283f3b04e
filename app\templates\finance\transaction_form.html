{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" enctype="multipart/form-data" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.type.label(class="form-label") }}
                            {% if form.type.errors %}
                                {{ form.type(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.type(class="form-select") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.category.label(class="form-label") }}
                            {% if form.category.errors %}
                                {{ form.category(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.category.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.category(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.amount.label(class="form-label") }}
                            {% if form.amount.errors %}
                                {{ form.amount(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.amount(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.transaction_date.label(class="form-label") }}
                            {% if form.transaction_date.errors %}
                                {{ form.transaction_date(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.transaction_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.transaction_date(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {% if form.description.errors %}
                            {{ form.description(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                    {% endfor %}
                            </div>
                        {% else %}
                            {{ form.description(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.payment_method.label(class="form-label") }}
                            {% if form.payment_method.errors %}
                                {{ form.payment_method(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.payment_method.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.payment_method(class="form-select") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.reference_number.label(class="form-label") }}
                            {% if form.reference_number.errors %}
                                {{ form.reference_number(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.reference_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.reference_number(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.related_to.label(class="form-label") }}
                            {% if form.related_to.errors %}
                                {{ form.related_to(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.related_to.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.related_to(class="form-select") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.related_id.label(class="form-label") }}
                            {% if form.related_id.errors %}
                                {{ form.related_id(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.related_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.related_id(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.attachment.label(class="form-label") }}
                        {% if form.attachment.errors %}
                            {{ form.attachment(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.attachment.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.attachment(class="form-control") }}
                        {% endif %}
                        <small class="form-text text-muted">يمكنك تحميل إيصال الدفع أو أي مستند آخر متعلق بالمعاملة.</small>
                    </div>


                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('finance.transactions') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const typeSelect = document.getElementById('type');
        const categorySelect = document.getElementById('category');
        const relatedToSelect = document.getElementById('related_to');
        const relatedIdInput = document.getElementById('related_id');

        // تحديث خيارات الفئة بناءً على نوع المعاملة
        function updateCategoryOptions() {
            const type = typeSelect.value;

            // حذف جميع الخيارات الحالية
            while (categorySelect.options.length > 0) {
                categorySelect.remove(0);
            }

            // إضافة الخيارات المناسبة بناءً على النوع
            if (type === 'income') {
                addOption(categorySelect, 'rent', 'إيجار');
                addOption(categorySelect, 'deposit', 'تأمين');
                addOption(categorySelect, 'contract_fee', 'رسوم عقد');
                addOption(categorySelect, 'other', 'أخرى');
            } else if (type === 'expense') {
                addOption(categorySelect, 'maintenance', 'صيانة');
                addOption(categorySelect, 'utilities', 'مرافق');
                addOption(categorySelect, 'taxes', 'ضرائب');
                addOption(categorySelect, 'insurance', 'تأمين');
                addOption(categorySelect, 'salary', 'رواتب');
                addOption(categorySelect, 'commission', 'عمولة');
                addOption(categorySelect, 'other', 'أخرى');
            }
        }

        // تحديث حقل related_id بناءً على related_to
        function updateRelatedIdField() {
            const relatedTo = relatedToSelect.value;

            if (relatedTo === 'other' || relatedTo === '') {
                relatedIdInput.disabled = true;

                // حذف جميع الخيارات الحالية
                while (relatedIdInput.options.length > 0) {
                    relatedIdInput.remove(0);
                }

                // إضافة خيار "لا يوجد" فقط
                addOption(relatedIdInput, 0, 'لا يوجد');
                relatedIdInput.value = 0;
            } else {
                relatedIdInput.disabled = false;

                // جلب الخيارات من الخادم
                fetch(`/finance/api/related-options?related_to=${relatedTo}`)
                    .then(response => response.json())
                    .then(data => {
                        // حذف جميع الخيارات الحالية
                        while (relatedIdInput.options.length > 0) {
                            relatedIdInput.remove(0);
                        }

                        // إضافة الخيارات الجديدة
                        data.forEach(option => {
                            addOption(relatedIdInput, option.id, option.text);
                        });

                        // تحديد الخيار الأول افتراضيًا
                        if (data.length > 0) {
                            relatedIdInput.value = data[0].id;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching related options:', error);
                    });
            }
        }

        // دالة مساعدة لإضافة خيار إلى قائمة منسدلة
        function addOption(selectElement, value, text) {
            const option = document.createElement('option');
            option.value = value;
            option.text = text;
            selectElement.add(option);
        }

        // تسجيل مستمعي الأحداث
        typeSelect.addEventListener('change', updateCategoryOptions);
        relatedToSelect.addEventListener('change', updateRelatedIdField);

        // تهيئة الحقول عند تحميل الصفحة
        updateCategoryOptions();
        updateRelatedIdField();
    });
</script>
{% endblock %}
