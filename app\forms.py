from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON><PERSON>ield, PasswordField, SubmitField, SelectField, TextAreaField, FloatField, IntegerField, DateField, HiddenField, BooleanField
from wtforms.validators import DataRequired, Email, Length, EqualTo, Optional, ValidationError
from app.db import query_db

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    submit = SubmitField('تسجيل الدخول')

class UserForm(FlaskForm):
    """نموذج المستخدم"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=50)])
    name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=3, max=100)])
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[DataRequired(), EqualTo('password')])
    role = SelectField('الدور', choices=[('admin', 'مدير'), ('accountant', 'محاسب'), ('data_entry', 'مدخل بيانات')])
    submit = SubmitField('حفظ')

    def validate_username(self, username):
        """التحقق من فريدية اسم المستخدم"""
        user = query_db('SELECT * FROM users WHERE username = ? AND id != ?',
                       (username.data, self.id.data if hasattr(self, 'id') and self.id.data else 0),
                       one=True)
        if user:
            raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر.')

    def validate_email(self, email):
        """التحقق من فريدية البريد الإلكتروني"""
        user = query_db('SELECT * FROM users WHERE email = ? AND id != ?',
                       (email.data, self.id.data if hasattr(self, 'id') and self.id.data else 0),
                       one=True)
        if user:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر.')

class EditUserForm(UserForm):
    """نموذج تعديل المستخدم"""
    id = HiddenField('المعرف')
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=6)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[EqualTo('password')])
    submit = SubmitField('تحديث')

class OwnerForm(FlaskForm):
    """نموذج المالك"""
    name = StringField('الاسم', validators=[DataRequired(), Length(min=3, max=100)])
    id_number = StringField('رقم الهوية', validators=[Optional(), Length(max=20)])
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email()])
    birth_date = DateField('تاريخ الميلاد', validators=[Optional()])
    address = TextAreaField('العنوان', validators=[Optional()])
    bank_iban = StringField('رقم الآيبان (IBAN)', validators=[Optional(), Length(max=50)])
    bank_name = StringField('اسم البنك', validators=[Optional(), Length(max=100)])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

    def validate_id_number(self, id_number):
        """التحقق من فريدية رقم الهوية"""
        if id_number.data:
            owner = query_db('SELECT * FROM owners WHERE id_number = ?', (id_number.data,), one=True)
            if owner:
                raise ValidationError('رقم الهوية مستخدم بالفعل. يرجى التحقق من الرقم.')

class EditOwnerForm(OwnerForm):
    """نموذج تعديل المالك"""
    id = HiddenField('المعرف')
    submit = SubmitField('تحديث')

    def validate_id_number(self, id_number):
        """التحقق من فريدية رقم الهوية مع استثناء المالك الحالي"""
        if id_number.data:
            owner = query_db('SELECT * FROM owners WHERE id_number = ? AND id != ?',
                           (id_number.data, self.id.data),
                           one=True)
            if owner:
                raise ValidationError('رقم الهوية مستخدم بالفعل. يرجى التحقق من الرقم.')

class BuildingForm(FlaskForm):
    """نموذج المبنى"""
    name = StringField('اسم المبنى', validators=[DataRequired(), Length(min=3, max=100)])
    building_code = StringField('رقم العقار', validators=[Optional(), Length(max=20)])
    address = StringField('العنوان', validators=[DataRequired(), Length(min=3, max=200)])
    district = StringField('الحي', validators=[Optional(), Length(max=100)])
    city = StringField('المدينة', validators=[DataRequired(), Length(min=2, max=100)])
    owner_id = SelectField('المالك', coerce=int, validators=[DataRequired()])
    agent = StringField('الوكيل', validators=[Optional(), Length(max=100)])
    type = StringField('النوع', validators=[Optional(), Length(max=50)])
    usage = StringField('الاستخدام', validators=[Optional(), Length(max=50)])
    deed_number = StringField('رقم الصك', validators=[Optional(), Length(max=50)])
    deed_date = DateField('تاريخ الصك', validators=[Optional()])
    deed_type = StringField('نوع الصك', validators=[Optional(), Length(max=50)])
    deed_issuer = StringField('جهة إصدار الصك', validators=[Optional(), Length(max=100)])
    building_number = StringField('رقم المبنى', validators=[Optional(), Length(max=20)])
    additional_number = StringField('الرقم الإضافي', validators=[Optional(), Length(max=20)])
    postal_code = StringField('الرمز البريدي', validators=[Optional(), Length(max=10)])
    old_file = StringField('الملف القديم', validators=[Optional(), Length(max=100)])
    build_year = IntegerField('سنة البناء', validators=[Optional()])
    floors_count = IntegerField('عدد الطوابق', validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

    def validate_building_code(self, building_code):
        """التحقق من فريدية رقم العقار"""
        if building_code.data:
            building = query_db('SELECT * FROM buildings WHERE building_code = ? AND id != ?',
                           (building_code.data, self.id.data if hasattr(self, 'id') and self.id.data else 0),
                           one=True)
            if building:
                raise ValidationError('رقم العقار مستخدم بالفعل. يرجى استخدام رقم آخر.')

class UnitForm(FlaskForm):
    """نموذج الوحدة"""
    unit_number = StringField('رقم الوحدة', validators=[DataRequired(), Length(min=1, max=20)])
    building_id = SelectField('المبنى', coerce=int, validators=[DataRequired()])
    floor_number = IntegerField('رقم الطابق', validators=[Optional()])
    type = StringField('نوع الوحدة', validators=[DataRequired(), Length(min=1, max=50)])
    area = FloatField('المساحة (متر مربع)', validators=[Optional()])
    rooms_count = IntegerField('عدد الغرف', validators=[Optional()])
    bathrooms_count = IntegerField('عدد الحمامات', validators=[Optional()])
    rent_amount = FloatField('قيمة الإيجار', validators=[DataRequired()])
    status = SelectField('الحالة', choices=[
        ('vacant', 'شاغرة'),
        ('occupied', 'مشغولة'),
        ('maintenance', 'تحت الصيانة'),
        ('reserved', 'محجوزة')
    ], validators=[DataRequired()])
    site_number_electricity = StringField('رقم الموقع - الكهرباء', validators=[Optional(), Length(max=50)])
    meter_number_electricity = StringField('رقم العداد - الكهرباء', validators=[Optional(), Length(max=50)])
    water_meter_number = StringField('رقم عداد المياه', validators=[Optional(), Length(max=50)])
    water_meter_account = StringField('رقم حساب عداد المياه', validators=[Optional(), Length(max=50)])
    electricity_meter_type = SelectField('نوع عداد الكهرباء', choices=[
        ('shared', 'مشترك'),
        ('separate', 'منفصل'),
        ('other', 'أخرى')
    ], validators=[Optional()])
    water_meter_type = SelectField('نوع عداد المياه', choices=[
        ('shared', 'مشترك'),
        ('separate', 'منفصل'),
        ('other', 'أخرى')
    ], validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

    def validate_type(self, type):
        """التحقق من صحة نوع الوحدة"""
        allowed_types = [
            'apartment', 'villa', 'shop', 'office', 'warehouse',
            'studio', 'floor', 'penthouse', 'duplex', 'chalet',
            'farm', 'land', 'other'
        ]
        # تحويل القيمة إلى حروف صغيرة للمقارنة
        type_lower = type.data.lower()

        # إذا كانت القيمة غير موجودة في القائمة المسموح بها، نقوم بتعيينها إلى 'other'
        if type_lower not in allowed_types:
            type.data = 'other'

class TenantForm(FlaskForm):
    """نموذج المستأجر"""
    name = StringField('الاسم', validators=[DataRequired(), Length(min=3, max=100)])
    id_number = StringField('رقم الهوية', validators=[Optional(), Length(max=20)])
    phone = StringField('رقم الهاتف', validators=[DataRequired(), Length(max=20)])
    email = StringField('البريد الإلكتروني', validators=[Optional(), Email()])
    nationality = StringField('الجنسية', validators=[Optional(), Length(max=50)])
    birth_date = DateField('تاريخ الميلاد', validators=[Optional()])
    occupation = StringField('المهنة', validators=[Optional(), Length(max=100)])
    employer = StringField('جهة العمل', validators=[Optional(), Length(max=100)])
    emergency_contact = StringField('رقم الطوارئ', validators=[Optional(), Length(max=100)])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

    def validate_id_number(self, id_number):
        """التحقق من فريدية رقم الهوية"""
        if id_number.data:
            tenant = query_db('SELECT * FROM tenants WHERE id_number = ? AND id != ?',
                            (id_number.data, self.id.data if hasattr(self, 'id') and self.id.data else 0),
                            one=True)
            if tenant:
                raise ValidationError('رقم الهوية مستخدم بالفعل. يرجى التحقق من الرقم.')

class ContractForm(FlaskForm):
    """نموذج العقد"""
    unit_id = SelectField('الوحدة', coerce=int, validators=[DataRequired()])
    tenant_id = SelectField('المستأجر', coerce=int, validators=[DataRequired()])
    start_date = DateField('تاريخ البداية', validators=[DataRequired()])
    end_date = DateField('تاريخ النهاية', validators=[DataRequired()])
    rent_amount = FloatField('قيمة الإيجار', validators=[DataRequired()])
    payment_frequency = StringField('دورة الدفع', validators=[DataRequired(), Length(max=50)])
    deposit_amount = FloatField('قيمة التأمين', validators=[Optional()])
    contract_fee = FloatField('رسوم العقد (على المالك)', validators=[Optional()])
    contract_fee_paid = FloatField('المدفوع من رسوم العقد', validators=[Optional()])
    contract_fee_status = SelectField('حالة دفع الرسوم', choices=[
        ('unpaid', 'غير مدفوعة'),
        ('partially_paid', 'مدفوعة جزئياً'),
        ('paid', 'مدفوعة بالكامل')
    ], validators=[DataRequired()])
    contract_number = StringField('رقم العقد', validators=[Optional(), Length(max=50)])
    status = StringField('حالة العقد', validators=[DataRequired(), Length(max=50)])
    terms = TextAreaField('شروط العقد', validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

    def validate_contract_number(self, contract_number):
        """التحقق من فريدية رقم العقد"""
        if contract_number.data:
            contract = query_db('SELECT * FROM contracts WHERE contract_number = ?', (contract_number.data,), one=True)
            if contract:
                raise ValidationError('رقم العقد مستخدم بالفعل. يرجى استخدام رقم آخر.')

    def validate_payment_frequency(self, payment_frequency):
        """التحقق من صحة دورة الدفع وتحويل القيم العربية إلى الإنجليزية"""
        # قاموس لتحويل القيم العربية إلى الإنجليزية
        arabic_to_english = {
            'شهري': 'monthly',
            'ربع سنوي': 'quarterly',
            'نصف سنوي': 'biannual',
            'سنوي': 'annual',
            'دفعة واحدة': 'one_payment',
            'دفعتين': 'two_payments',
            'ثلاث دفعات': 'three_payments',
            'أربع دفعات': 'four_payments'
        }

        # إذا كانت القيمة بالعربية، نقوم بتحويلها إلى الإنجليزية
        if payment_frequency.data in arabic_to_english:
            payment_frequency.data = arabic_to_english[payment_frequency.data]

    def validate_status(self, status):
        """التحقق من صحة حالة العقد وتحويل القيم العربية إلى الإنجليزية"""
        # قاموس لتحويل القيم العربية إلى الإنجليزية
        arabic_to_english = {
            'نشط': 'active',
            'منتهي': 'expired',
            'ملغي': 'terminated',
            'مجدد': 'renewed',
            'معلق': 'pending',
            'تحت المراجعة': 'review'
        }

        # إذا كانت القيمة بالعربية، نقوم بتحويلها إلى الإنجليزية
        if status.data in arabic_to_english:
            status.data = arabic_to_english[status.data]

class EditContractForm(ContractForm):
    """نموذج تعديل العقد"""
    id = HiddenField('المعرف')
    submit = SubmitField('تحديث')

    def validate_contract_number(self, contract_number):
        """التحقق من فريدية رقم العقد مع استثناء العقد الحالي"""
        if contract_number.data:
            contract = query_db('SELECT * FROM contracts WHERE contract_number = ? AND id != ?',
                             (contract_number.data, self.id.data),
                             one=True)
            if contract:
                raise ValidationError('رقم العقد مستخدم بالفعل. يرجى استخدام رقم آخر.')

class DocumentForm(FlaskForm):
    """نموذج المستند"""
    title = StringField('عنوان المستند', validators=[DataRequired(), Length(min=3, max=100)])
    file = FileField('الملف', validators=[
        DataRequired(),
        FileAllowed(['pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx', 'xls', 'xlsx'], 'يرجى تحميل ملف بتنسيق صالح.')
    ])
    related_to = SelectField('متعلق بـ', choices=[
        ('owner', 'مالك'),
        ('building', 'مبنى'),
        ('unit', 'وحدة'),
        ('tenant', 'مستأجر'),
        ('contract', 'عقد'),
        ('transaction', 'معاملة مالية')
    ], validators=[DataRequired()])
    related_id = SelectField('المعرف المرتبط', coerce=int, validators=[DataRequired()])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

class TransactionForm(FlaskForm):
    """نموذج المعاملة المالية"""
    transaction_date = DateField('تاريخ المعاملة', validators=[DataRequired()])
    amount = FloatField('المبلغ', validators=[DataRequired()])
    type = SelectField('نوع المعاملة', choices=[
        ('income', 'إيراد'),
        ('expense', 'مصروف')
    ], validators=[DataRequired()])
    category = SelectField('الفئة', choices=[
        ('rent', 'إيجار'),
        ('deposit', 'تأمين'),
        ('contract_fee', 'رسوم عقد'),
        ('maintenance', 'صيانة'),
        ('utilities', 'مرافق'),
        ('taxes', 'ضرائب'),
        ('insurance', 'تأمين'),
        ('salary', 'رواتب'),
        ('commission', 'عمولة'),
        ('other', 'أخرى')
    ], validators=[DataRequired()])
    description = TextAreaField('الوصف', validators=[Optional()])
    related_to = SelectField('متعلق بـ', choices=[
        ('contract', 'عقد'),
        ('building', 'مبنى'),
        ('unit', 'وحدة'),
        ('owner', 'مالك'),
        ('other', 'أخرى')
    ], validators=[Optional()])
    related_id = SelectField('المعرف المرتبط', coerce=int, validators=[Optional()])
    payment_method = SelectField('طريقة الدفع', choices=[
        ('cash', 'نقدي'),
        ('bank_transfer', 'تحويل بنكي'),
        ('check', 'شيك'),
        ('credit_card', 'بطاقة ائتمان'),
        ('other', 'أخرى')
    ], validators=[DataRequired()])
    reference_number = StringField('رقم المرجع', validators=[Optional(), Length(max=50)])
    attachment = FileField('مرفقات (إيصال)', validators=[
        Optional(),
        FileAllowed(['pdf', 'png', 'jpg', 'jpeg'], 'يرجى تحميل ملف بتنسيق صالح.')
    ])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

class MeterReadingForm(FlaskForm):
    """نموذج قراءة العداد"""
    unit_id = SelectField('الوحدة', coerce=int, validators=[DataRequired()])
    reading_date = DateField('تاريخ القراءة', validators=[DataRequired()])
    electricity_reading = FloatField('قراءة الكهرباء', validators=[Optional()])
    water_reading = FloatField('قراءة المياه', validators=[Optional()])
    gas_reading = FloatField('قراءة الغاز', validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

class MaintenanceRequestForm(FlaskForm):
    """نموذج طلب الصيانة"""
    unit_id = SelectField('الوحدة', coerce=int, validators=[DataRequired()])
    tenant_id = SelectField('المستأجر', coerce=int, validators=[Optional()])
    request_date = DateField('تاريخ الطلب', validators=[DataRequired()])
    description = TextAreaField('وصف المشكلة', validators=[DataRequired()])
    priority = SelectField('الأولوية', choices=[
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة')
    ], validators=[DataRequired()])
    status = SelectField('الحالة', choices=[
        ('pending', 'قيد الانتظار'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي')
    ], validators=[DataRequired()])
    completion_date = DateField('تاريخ الإكمال', validators=[Optional()])
    cost = FloatField('التكلفة', validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('حفظ')

class ReportForm(FlaskForm):
    """نموذج التقرير"""
    report_type = SelectField('نوع التقرير', choices=[
        ('income', 'تقرير الإيرادات'),
        ('expense', 'تقرير المصروفات'),
        ('profit_loss', 'تقرير الأرباح والخسائر'),
        ('occupancy', 'تقرير الإشغال'),
        ('contracts', 'تقرير العقود'),
        ('maintenance', 'تقرير الصيانة')
    ], validators=[DataRequired()])
    start_date = DateField('تاريخ البداية', validators=[DataRequired()])
    end_date = DateField('تاريخ النهاية', validators=[DataRequired()])
    building_id = SelectField('المبنى', coerce=int, validators=[Optional()])
    format = SelectField('تنسيق التقرير', choices=[
        ('html', 'عرض على الشاشة'),
        ('pdf', 'PDF'),
        ('excel', 'Excel')
    ], validators=[DataRequired()])
    submit = SubmitField('إنشاء التقرير')
