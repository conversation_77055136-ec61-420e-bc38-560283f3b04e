{% extends "base.html" %}

{% block title %}إدارة المستأجرين - نظام إدارة العقارات{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* تحسينات عامة */
    .card {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .card-header {
        border-top-left-radius: 0.5rem !important;
        border-top-right-radius: 0.5rem !important;
    }

    /* تنسيق الجدول */
    .table th {
        font-weight: 600;
        white-space: nowrap;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    /* تنسيق البطاقات */
    .bg-opacity-10 {
        --bs-bg-opacity: 0.1;
    }

    .rounded-circle {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 767.98px) {
        .card-title {
            font-size: 1.1rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .mobile-action-buttons .dropdown-menu {
            min-width: 200px;
        }

        .list-group-item {
            padding: 0.75rem;
        }
    }

    /* تنسيق للشاشات المتوسطة والكبيرة */
    @media (min-width: 768px) {
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table th, .table td {
            vertical-align: middle;
        }
    }

    /* تأثيرات متحركة */
    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.1);
    }

    /* تنسيق الأيقونات */
    .fa-eye, .fa-info {
        color: #17a2b8;
    }

    .fa-edit, .fa-pencil-alt {
        color: #007bff;
    }

    .fa-trash-alt {
        color: #dc3545;
    }

    /* تنسيق الروابط */
    a {
        text-decoration: none;
    }

    /* تنسيق الفورم */
    .form-control:focus, .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تنسيق الأزرار المجمعة */
    .btn-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
    }

    /* تنسيق خاص بالمستأجرين */
    .tenant-card {
        transition: all 0.3s ease;
    }

    .tenant-card:hover {
        transform: translateY(-5px);
    }

    .tenant-icon {
        font-size: 2rem;
        color: #007bff;
    }

    .tenant-with-contract {
        color: #28a745;
    }

    .tenant-without-contract {
        color: #dc3545;
    }

    .contact-icon {
        width: 32px;
        height: 32px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 0.5rem;
        transition: all 0.3s ease;
    }

    .contact-icon:hover {
        transform: scale(1.2);
    }

    .contact-phone {
        background-color: #28a745;
        color: white;
    }

    .contact-email {
        background-color: #007bff;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة المستأجرين</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('tenants.add') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة مستأجر جديد
        </a>
    </div>
</div>

<div class="card mb-4 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>بحث وفلترة
        </h5>
        <button class="btn btn-sm btn-outline-primary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
            <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
        </button>
    </div>
    <div class="card-body collapse show" id="searchCollapse">
        <form method="GET" action="{{ url_for('tenants.index') }}" id="searchForm">
            <div class="row g-3">
                <!-- حقل البحث - يظهر دائماً بعرض كامل -->
                <div class="col-12">
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="ابحث في الاسم، رقم الهوية، رقم الهاتف..."
                               value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            بحث
                        </button>
                        <a href="{{ url_for('tenants.index') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i>
                        </a>
                    </div>
                </div>

                <!-- الفلاتر الرئيسية -->
                <div class="col-12 col-md-6 col-lg-3">
                    <label for="nationality" class="form-label">الجنسية</label>
                    <select class="form-select form-select-sm" id="nationality" name="nationality">
                        <option value="">الكل</option>
                        {% for nat in nationalities %}
                        <option value="{{ nat }}" {% if nationality == nat %}selected{% endif %}>{{ nat }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="has_active_contract" class="form-label">حالة العقد</label>
                    <select class="form-select form-select-sm" id="has_active_contract" name="has_active_contract">
                        <option value="">الكل</option>
                        <option value="yes" {% if has_active_contract == 'yes' %}selected{% endif %}>لديهم عقود نشطة</option>
                        <option value="no" {% if has_active_contract == 'no' %}selected{% endif %}>بدون عقود نشطة</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="occupation" class="form-label">المهنة</label>
                    <select class="form-select form-select-sm" id="occupation" name="occupation">
                        <option value="">الكل</option>
                        {% for occ in occupations %}
                        <option value="{{ occ }}" {% if occupation == occ %}selected{% endif %}>{{ occ }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select form-select-sm" id="sort_by" name="sort_by">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم</option>
                        <option value="id_number" {% if sort_by == 'id_number' %}selected{% endif %}>رقم الهوية</option>
                        <option value="nationality" {% if sort_by == 'nationality' %}selected{% endif %}>الجنسية</option>
                        <option value="newest" {% if sort_by == 'newest' %}selected{% endif %}>الأحدث</option>
                    </select>
                </div>
            </div>

            <!-- أزرار إضافية للشاشات المتوسطة والكبيرة -->
            <div class="d-none d-md-flex justify-content-between mt-3">
                <div>
                    <label for="display_mode" class="form-label">طريقة العرض</label>
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="display_mode" id="table_mode" value="table" {% if display_mode == 'table' or not display_mode %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="table_mode">
                            <i class="fas fa-table me-1"></i>جدول
                        </label>

                        <input type="radio" class="btn-check" name="display_mode" id="cards_mode" value="cards" {% if display_mode == 'cards' %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="cards_mode">
                            <i class="fas fa-th-large me-1"></i>بطاقات
                        </label>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>تطبيق الفلاتر
                    </button>
                    <a href="{{ url_for('tenants.index') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين
                    </a>
                    <a href="{{ url_for('tenants.add') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة مستأجر جديد
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>قائمة المستأجرين
        </h5>
        <span class="badge bg-primary">{{ tenants|length }} مستأجر</span>
    </div>
    <div class="card-body">
        {% if tenants %}
            {% if display_mode == 'table' or not display_mode %}
            <!-- عرض الجدول -->
            <!-- جدول للشاشات المتوسطة والكبيرة -->
            <div class="table-responsive d-none d-md-block">
                <table class="table table-striped table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">#</th>
                            <th>الاسم</th>
                            <th>رقم الهوية</th>
                            <th>معلومات الاتصال</th>
                            <th>الجنسية</th>
                            <th>حالة العقد</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tenant in tenants %}
                        <tr>
                            <td class="text-center">{{ tenant.id }}</td>
                            <td><strong>{{ tenant.name }}</strong></td>
                            <td>
                                {% if tenant.id_number %}
                                <span class="badge bg-secondary">{{ tenant.id_number }}</span>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if tenant.phone %}
                                    <a href="tel:{{ tenant.phone }}" class="me-2" title="اتصال" data-bs-toggle="tooltip">
                                        <span class="contact-icon contact-phone">
                                            <i class="fas fa-phone-alt"></i>
                                        </span>
                                    </a>
                                    {% endif %}

                                    {% if tenant.email %}
                                    <a href="mailto:{{ tenant.email }}" title="إرسال بريد إلكتروني" data-bs-toggle="tooltip">
                                        <span class="contact-icon contact-email">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if tenant.nationality %}
                                <i class="fas fa-globe me-1 text-primary"></i>{{ tenant.nationality }}
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if tenant.has_active_contract %}
                                <span class="badge bg-success">لديه عقد نشط</span>
                                {% else %}
                                <span class="badge bg-danger">لا يوجد عقد نشط</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('tenants.view', tenant_id=tenant.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل" data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('tenants.edit', tenant_id=tenant.id) }}" class="btn btn-sm btn-primary" title="تعديل" data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteModal{{ tenant.id }}"
                                            title="حذف" data-bs-toggle="tooltip">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>

                                <!-- Modal for delete confirmation -->
                                <div class="modal fade" id="deleteModal{{ tenant.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ tenant.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ tenant.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف المستأجر <strong>{{ tenant.name }}</strong>؟
                                                {% if tenant.has_active_contract %}
                                                <div class="alert alert-warning mt-3">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>تنبيه: هذا المستأجر لديه عقد نشط!
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('tenants.delete', tenant_id=tenant.id) }}" method="POST" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- قائمة للشاشات الصغيرة (الهواتف) -->
            <div class="d-md-none">
                {% for tenant in tenants %}
                <div class="card mb-3 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center
                                {% if tenant.has_active_contract %}bg-success bg-opacity-10{% else %}bg-danger bg-opacity-10{% endif %}">
                        <h5 class="card-title mb-0">
                            <strong>{{ tenant.name }}</strong>
                        </h5>
                        <div>
                            {% if tenant.has_active_contract %}
                            <span class="badge bg-success">لديه عقد</span>
                            {% else %}
                            <span class="badge bg-danger">بدون عقد</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            {% if tenant.id_number %}
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-id-card text-primary me-2"></i>رقم الهوية:</span>
                                <span>{{ tenant.id_number }}</span>
                            </li>
                            {% endif %}

                            {% if tenant.nationality %}
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-globe text-primary me-2"></i>الجنسية:</span>
                                <span>{{ tenant.nationality }}</span>
                            </li>
                            {% endif %}

                            {% if tenant.phone %}
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-phone-alt text-success me-2"></i>رقم الهاتف:</span>
                                <a href="tel:{{ tenant.phone }}" class="text-decoration-none">{{ tenant.phone }}</a>
                            </li>
                            {% endif %}

                            {% if tenant.email %}
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-envelope text-primary me-2"></i>البريد الإلكتروني:</span>
                                <a href="mailto:{{ tenant.email }}" class="text-decoration-none">{{ tenant.email }}</a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ url_for('tenants.view', tenant_id=tenant.id) }}" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <a href="{{ url_for('tenants.edit', tenant_id=tenant.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalMobile{{ tenant.id }}">
                            <i class="fas fa-trash-alt me-1"></i>حذف
                        </button>
                    </div>

                    <!-- Modal for mobile delete confirmation -->
                    <div class="modal fade" id="deleteModalMobile{{ tenant.id }}" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">تأكيد الحذف</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    هل أنت متأكد من رغبتك في حذف المستأجر <strong>{{ tenant.name }}</strong>؟
                                    {% if tenant.has_active_contract %}
                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-exclamation-triangle me-2"></i>تنبيه: هذا المستأجر لديه عقد نشط!
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                    <form action="{{ url_for('tenants.delete', tenant_id=tenant.id) }}" method="POST">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger">حذف</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- عرض البطاقات -->
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                {% for tenant in tenants %}
                <div class="col">
                    <div class="card h-100 shadow-sm tenant-card">
                        <div class="card-header {% if tenant.has_active_contract %}bg-success bg-opacity-10{% else %}bg-danger bg-opacity-10{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <strong>{{ tenant.name }}</strong>
                                </h5>
                                <div>
                                    {% if tenant.has_active_contract %}
                                    <span class="badge bg-success">لديه عقد</span>
                                    {% else %}
                                    <span class="badge bg-danger">بدون عقد</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-user-circle tenant-icon {% if tenant.has_active_contract %}tenant-with-contract{% else %}tenant-without-contract{% endif %}"></i>
                            </div>
                            <ul class="list-group list-group-flush mb-3">
                                {% if tenant.id_number %}
                                <li class="list-group-item d-flex justify-content-between">
                                    <span><i class="fas fa-id-card text-primary me-2"></i>رقم الهوية:</span>
                                    <span>{{ tenant.id_number }}</span>
                                </li>
                                {% endif %}

                                {% if tenant.nationality %}
                                <li class="list-group-item d-flex justify-content-between">
                                    <span><i class="fas fa-globe text-primary me-2"></i>الجنسية:</span>
                                    <span>{{ tenant.nationality }}</span>
                                </li>
                                {% endif %}

                                <li class="list-group-item">
                                    <div class="d-flex justify-content-center mt-2">
                                        {% if tenant.phone %}
                                        <a href="tel:{{ tenant.phone }}" class="btn btn-sm btn-success me-2">
                                            <i class="fas fa-phone-alt me-1"></i>اتصال
                                        </a>
                                        {% endif %}

                                        {% if tenant.email %}
                                        <a href="mailto:{{ tenant.email }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-envelope me-1"></i>مراسلة
                                        </a>
                                        {% endif %}
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('tenants.view', tenant_id=tenant.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="{{ url_for('tenants.edit', tenant_id=tenant.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalCard{{ tenant.id }}">
                                    <i class="fas fa-trash-alt me-1"></i>حذف
                                </button>
                            </div>

                            <!-- Modal for card delete confirmation -->
                            <div class="modal fade" id="deleteModalCard{{ tenant.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من رغبتك في حذف المستأجر <strong>{{ tenant.name }}</strong>؟
                                            {% if tenant.has_active_contract %}
                                            <div class="alert alert-warning mt-3">
                                                <i class="fas fa-exclamation-triangle me-2"></i>تنبيه: هذا المستأجر لديه عقد نشط!
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('tenants.delete', tenant_id=tenant.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        {% else %}
        <div class="alert alert-info">
            لا يوجد مستأجرين حتى الآن. <a href="{{ url_for('tenants.add') }}" class="btn btn-sm btn-primary ms-2">إضافة مستأجر جديد</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث النموذج عند تغيير أي حقل (باستثناء حقل البحث)
        const autoSubmitElements = document.querySelectorAll('#searchForm select, #searchForm input[type="radio"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', function() {
                // إظهار مؤشر التحميل
                const submitBtn = document.querySelector('#searchForm button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التحميل...';
                    submitBtn.disabled = true;
                }

                // تأخير التقديم قليلاً للسماح بتحديث القيم
                setTimeout(() => {
                    document.getElementById('searchForm').submit();
                }, 100);
            });
        });

        // إضافة تأثير تمييز للصفوف عند التمرير فوقها
        const tableRows = document.querySelectorAll('table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('table-active');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('table-active');
            });
        });

        // تفعيل البحث الفوري عند الكتابة في حقل البحث
        const searchInput = document.getElementById('search');
        let searchTimeout;
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        document.getElementById('searchForm').submit();
                    }
                }, 500);
            });
        }

        // تفعيل التلميحات (tooltips)
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تأثيرات للبطاقات
        const tenantCards = document.querySelectorAll('.tenant-card');
        tenantCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('shadow');
            });
            card.addEventListener('mouseleave', function() {
                this.classList.remove('shadow');
            });
        });

        // تأثيرات لأيقونات الاتصال
        const contactIcons = document.querySelectorAll('.contact-icon');
        contactIcons.forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.2)';
            });
            icon.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // تفعيل مودال التأكيد للحذف
        const deleteButtons = document.querySelectorAll('.confirm-delete');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm-message'))) {
                    e.preventDefault();
                }
            });
        });
    });
</script>
{% endblock %}