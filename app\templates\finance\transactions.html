{% extends "base.html" %}

{% block title %}المعاملات المالية - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">المعاملات المالية</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('finance.add_transaction') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة معاملة جديدة
        </a>
        <a href="{{ url_for('finance.summary') }}" class="btn btn-info">
            <i class="fas fa-chart-pie me-1"></i>الملخص المالي
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>تصفية النتائج
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('finance.transactions') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="type" class="form-label">النوع</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">الكل</option>
                        <option value="income" {% if request.args.get('type') == 'income' %}selected{% endif %}>إيراد</option>
                        <option value="expense" {% if request.args.get('type') == 'expense' %}selected{% endif %}>مصروف</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="category" class="form-label">الفئة</label>
                    <select name="category" id="category" class="form-select">
                        <option value="">الكل</option>
                        <option value="rent" {% if request.args.get('category') == 'rent' %}selected{% endif %}>إيجار</option>
                        <option value="deposit" {% if request.args.get('category') == 'deposit' %}selected{% endif %}>تأمين</option>
                        <option value="maintenance" {% if request.args.get('category') == 'maintenance' %}selected{% endif %}>صيانة</option>
                        <option value="utilities" {% if request.args.get('category') == 'utilities' %}selected{% endif %}>مرافق</option>
                        <option value="taxes" {% if request.args.get('category') == 'taxes' %}selected{% endif %}>ضرائب</option>
                        <option value="insurance" {% if request.args.get('category') == 'insurance' %}selected{% endif %}>تأمين</option>
                        <option value="salary" {% if request.args.get('category') == 'salary' %}selected{% endif %}>رواتب</option>
                        <option value="commission" {% if request.args.get('category') == 'commission' %}selected{% endif %}>عمولة</option>
                        <option value="other" {% if request.args.get('category') == 'other' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request.args.get('date_from', '') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request.args.get('date_to', '') }}">
                </div>
            </div>
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{{ url_for('finance.transactions') }}" class="btn btn-secondary ms-2">
                    <i class="fas fa-redo me-1"></i>إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if transactions %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>التاريخ</th>
                        <th>النوع</th>
                        <th>الفئة</th>
                        <th>الوصف</th>
                        <th>المبلغ</th>
                        <th>متعلق بـ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.id }}</td>
                        <td>{{ transaction.transaction_date|format_date }}</td>
                        <td>
                            {% if transaction.type == 'income' %}
                            <span class="badge bg-success">إيراد</span>
                            {% else %}
                            <span class="badge bg-danger">مصروف</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if transaction.category == 'rent' %}
                            إيجار
                            {% elif transaction.category == 'deposit' %}
                            تأمين
                            {% elif transaction.category == 'maintenance' %}
                            صيانة
                            {% elif transaction.category == 'utilities' %}
                            مرافق
                            {% elif transaction.category == 'taxes' %}
                            ضرائب
                            {% elif transaction.category == 'insurance' %}
                            تأمين
                            {% elif transaction.category == 'salary' %}
                            رواتب
                            {% elif transaction.category == 'commission' %}
                            عمولة
                            {% else %}
                            أخرى
                            {% endif %}
                        </td>
                        <td>{{ transaction.description|truncate(30) }}</td>
                        <td>{{ transaction.amount|format_currency }}</td>
                        <td>
                            {% if transaction.related_to == 'contract' %}
                            <a href="{{ url_for('tenants.view_contract', contract_id=transaction.related_id) }}">
                                عقد #{{ transaction.related_id }}
                            </a>
                            {% elif transaction.related_to == 'unit' %}
                            <a href="{{ url_for('properties.view_unit', unit_id=transaction.related_id) }}">
                                وحدة #{{ transaction.related_id }}
                            </a>
                            {% elif transaction.related_to == 'building' %}
                            <a href="{{ url_for('properties.view_building', building_id=transaction.related_id) }}">
                                مبنى #{{ transaction.related_id }}
                            </a>
                            {% elif transaction.related_to == 'maintenance' %}
                            <a href="{{ url_for('properties.view_maintenance', maintenance_id=transaction.related_id) }}">
                                صيانة #{{ transaction.related_id }}
                            </a>
                            {% elif transaction.related_to == 'owner' %}
                            <a href="{{ url_for('owners.view', owner_id=transaction.related_id) }}">
                                مالك #{{ transaction.related_id }}
                            </a>
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('finance.view_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('finance.edit_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ url_for('finance.delete_transaction', transaction_id=transaction.id) }}" method="POST" class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذه المعاملة؟">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {# تم تعطيل الترقيم مؤقتًا حتى يتم تنفيذه بشكل صحيح #}

        {% else %}
        <div class="alert alert-info">
            لا توجد معاملات مالية تطابق معايير البحث. <a href="{{ url_for('finance.add_transaction') }}">إضافة معاملة جديدة</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
