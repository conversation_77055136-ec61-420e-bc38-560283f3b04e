import os
import uuid
import shutil
from datetime import datetime, timedelta
from flask import current_app
from werkzeug.utils import secure_filename
# استيراد دوال قاعدة البيانات
from app.db import query_db

# تحقق من وجود مكتبة pdf2image
try:
    from pdf2image import convert_from_path
    PDF2IMAGE_AVAILABLE = True
except ImportError:
    PDF2IMAGE_AVAILABLE = False

def allowed_file(filename, allowed_extensions=None):
    """التحقق مما إذا كان الملف مسموحًا به"""
    if allowed_extensions is None:
        allowed_extensions = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_file(file, folder=None):
    """حفظ الملف المرفوع"""
    if file and file.filename:
        filename = secure_filename(file.filename)
        # إنشاء اسم فريد للملف
        unique_filename = f"{uuid.uuid4().hex}_{filename}"

        # تحديد المجلد
        if folder is None:
            folder = current_app.config['UPLOAD_FOLDER']

        # التأكد من وجود المجلد
        os.makedirs(folder, exist_ok=True)

        # حفظ الملف
        file_path = os.path.join(folder, unique_filename)
        file.save(file_path)

        return file_path

    return None

def format_currency(value):
    """تنسيق القيمة كعملة"""
    if value is None:
        return f"0 {current_app.config['CURRENCY_SYMBOL']}"
    return f"{value:,.2f} {current_app.config['CURRENCY_SYMBOL']}"

def format_date(value):
    """تنسيق التاريخ"""
    if value is None:
        return ""
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d')
        except ValueError:
            return value
    return value.strftime(current_app.config['DATE_FORMAT'])

def get_expiring_contracts(days=7):
    """الحصول على العقود التي ستنتهي قريبًا"""
    today = datetime.now().date()
    expiry_date = today + timedelta(days=days)

    query = """
    SELECT c.*, t.name as tenant_name, u.unit_number, b.name as building_name
    FROM contracts c
    JOIN tenants t ON c.tenant_id = t.id
    JOIN units u ON c.unit_id = u.id
    JOIN buildings b ON u.building_id = b.id
    WHERE c.end_date BETWEEN ? AND ?
    AND c.status = 'active'
    """

    return query_db(query, (today.isoformat(), expiry_date.isoformat()))

def get_monthly_income(year, month):
    """الحصول على إجمالي الإيرادات الشهرية"""
    start_date = datetime(year, month, 1).date()

    # تحديد نهاية الشهر
    if month == 12:
        end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
    else:
        end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)

    query = """
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'income'
    """

    result = query_db(query, (start_date.isoformat(), end_date.isoformat()), one=True)
    return result['total'] if result and result['total'] is not None else 0

def get_monthly_expense(year, month):
    """الحصول على إجمالي المصروفات الشهرية"""
    start_date = datetime(year, month, 1).date()

    # تحديد نهاية الشهر
    if month == 12:
        end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
    else:
        end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)

    query = """
    SELECT SUM(amount) as total
    FROM transactions
    WHERE transaction_date BETWEEN ? AND ?
    AND type = 'expense'
    """

    result = query_db(query, (start_date.isoformat(), end_date.isoformat()), one=True)
    return result['total'] if result and result['total'] is not None else 0

def get_yearly_income_by_month(year):
    """الحصول على الإيرادات السنوية مقسمة حسب الشهر"""
    result = []

    for month in range(1, 13):
        income = get_monthly_income(year, month)
        result.append(income)

    return result

def get_yearly_expense_by_month(year):
    """الحصول على المصروفات السنوية مقسمة حسب الشهر"""
    result = []

    for month in range(1, 13):
        expense = get_monthly_expense(year, month)
        result.append(expense)

    return result

def get_unit_status_counts():
    """الحصول على عدد الوحدات حسب الحالة"""
    query = """
    SELECT status, COUNT(*) as count
    FROM units
    GROUP BY status
    """

    return query_db(query)

def get_contract_status_counts():
    """الحصول على عدد العقود حسب الحالة"""
    query = """
    SELECT status, COUNT(*) as count
    FROM contracts
    GROUP BY status
    """

    return query_db(query)

def convert_pdf_to_images(pdf_path, output_folder=None, dpi=150, fmt='png'):
    """تحويل ملف PDF إلى مجموعة من الصور

    Args:
        pdf_path (str): المسار الكامل لملف PDF
        output_folder (str, optional): مجلد الإخراج. إذا لم يتم تحديده، سيتم استخدام مجلد افتراضي.
        dpi (int, optional): دقة الصور. الافتراضي هو 150.
        fmt (str, optional): صيغة الصور. الافتراضي هو 'png'.

    Returns:
        list: قائمة بمسارات الصور المحولة
    """
    if not PDF2IMAGE_AVAILABLE:
        return []

    # تحديد مجلد الإخراج
    if output_folder is None:
        output_folder = os.path.join(current_app.static_folder, 'images', 'pdf_pages')

    # التأكد من وجود المجلد
    os.makedirs(output_folder, exist_ok=True)

    # استخراج اسم الملف بدون امتداد
    pdf_filename = os.path.basename(pdf_path)
    pdf_name = os.path.splitext(pdf_filename)[0]

    # إنشاء مجلد خاص بهذا الملف
    pdf_output_folder = os.path.join(output_folder, pdf_name)

    # إذا كان المجلد موجودًا، قم بحذفه وإعادة إنشائه
    if os.path.exists(pdf_output_folder):
        shutil.rmtree(pdf_output_folder)
    os.makedirs(pdf_output_folder, exist_ok=True)

    try:
        # تحويل صفحات PDF إلى صور
        pages = convert_from_path(pdf_path, dpi=dpi)

        # حفظ الصور
        image_paths = []
        for i, page in enumerate(pages, start=1):
            image_path = os.path.join(pdf_output_folder, f'page_{i}.{fmt}')
            page.save(image_path, fmt.upper())
            image_paths.append(image_path)

        return image_paths
    except Exception as e:
        print(f"Error converting PDF to images: {str(e)}")
        return []
