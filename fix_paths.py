"""
ملف لإصلاح مسارات الملفات في التطبيق عند تشغيله كملف تنفيذي واحد
"""

import os
import sys
import logging
import traceback

# إعداد التسجيل
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('fix_paths')

def fix_application_paths():
    """
    تصحيح مسارات الملفات في التطبيق عند تشغيله كملف تنفيذي واحد
    """
    try:
        logger.info("Starting path fixing process...")
        
        # تحديد المسار الأساسي للتطبيق
        if getattr(sys, 'frozen', False):
            # في حالة التشغيل كملف تنفيذي
            application_path = os.path.dirname(sys.executable)
            logger.info(f"Running as frozen application from: {application_path}")
        else:
            # في حالة التشغيل كسكريبت
            application_path = os.path.dirname(os.path.abspath(__file__))
            logger.info(f"Running as script from: {application_path}")
        
        # إنشاء المجلدات الضرورية
        required_dirs = [
            os.path.join(application_path, 'instance'),
            os.path.join(application_path, 'uploads'),
            os.path.join(application_path, 'uploads', 'buildings'),
            os.path.join(application_path, 'uploads', 'contracts'),
            os.path.join(application_path, 'uploads', 'documents'),
            os.path.join(application_path, 'uploads', 'owners'),
            os.path.join(application_path, 'uploads', 'tenants'),
            os.path.join(application_path, 'uploads', 'transactions')
        ]
        
        for directory in required_dirs:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"Created directory: {directory}")
            else:
                logger.info(f"Directory already exists: {directory}")
        
        # إضافة المسار الأساسي إلى مسار البحث
        if application_path not in sys.path:
            sys.path.insert(0, application_path)
            logger.info(f"Added {application_path} to sys.path")
        
        # إضافة مجلد app إلى مسار البحث
        app_path = os.path.join(application_path, 'app')
        if os.path.exists(app_path) and app_path not in sys.path:
            sys.path.insert(0, app_path)
            logger.info(f"Added {app_path} to sys.path")
        
        # إضافة مجلد _internal إلى مسار البحث إذا كان موجودًا
        internal_path = os.path.join(application_path, '_internal')
        if os.path.exists(internal_path) and internal_path not in sys.path:
            sys.path.insert(0, internal_path)
            logger.info(f"Added {internal_path} to sys.path")
        
        # إضافة مجلد _internal/app إلى مسار البحث إذا كان موجودًا
        internal_app_path = os.path.join(application_path, '_internal', 'app')
        if os.path.exists(internal_app_path) and internal_app_path not in sys.path:
            sys.path.insert(0, internal_app_path)
            logger.info(f"Added {internal_app_path} to sys.path")
        
        # تعيين المجلد الحالي
        os.chdir(application_path)
        logger.info(f"Changed current directory to: {application_path}")
        
        # طباعة مسار البحث للتشخيص
        logger.info("Python search path:")
        for path in sys.path:
            logger.info(f"  - {path}")
        
        # التحقق من وجود ملف config.py
        config_path = os.path.join(application_path, 'config.py')
        if not os.path.exists(config_path):
            logger.warning(f"config.py not found at: {config_path}")
            
            # البحث عن ملف config.py في مجلدات مختلفة
            possible_config_paths = [
                os.path.join(application_path, '_internal', 'config.py'),
                os.path.join(application_path, 'app', 'config.py')
            ]
            
            for path in possible_config_paths:
                if os.path.exists(path):
                    logger.info(f"Found config.py at: {path}")
                    # نسخ ملف config.py إلى المجلد الأساسي
                    import shutil
                    shutil.copy(path, config_path)
                    logger.info(f"Copied config.py to: {config_path}")
                    break
        
        # التحقق من وجود ملف schema.sql
        schema_path = os.path.join(application_path, 'schema.sql')
        if not os.path.exists(schema_path):
            logger.warning(f"schema.sql not found at: {schema_path}")
            
            # البحث عن ملف schema.sql في مجلدات مختلفة
            possible_schema_paths = [
                os.path.join(application_path, '_internal', 'schema.sql'),
                os.path.join(application_path, 'app', 'schema.sql')
            ]
            
            for path in possible_schema_paths:
                if os.path.exists(path):
                    logger.info(f"Found schema.sql at: {path}")
                    # نسخ ملف schema.sql إلى المجلد الأساسي
                    import shutil
                    shutil.copy(path, schema_path)
                    logger.info(f"Copied schema.sql to: {schema_path}")
                    break
        
        logger.info("Path fixing process completed successfully")
        return True
    
    except Exception as e:
        logger.error(f"Error fixing paths: {str(e)}")
        logger.error(traceback.format_exc())
        return False

# تطبيق الإصلاح عند استيراد الملف
fix_application_paths()
