#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات Windows Defender - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import subprocess
from pathlib import Path

class WindowsDefenderConfig:
    """إعدادات Windows Defender لتجنب الكشف الخاطئ"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        
    def get_exclusion_paths(self):
        """الحصول على المسارات المطلوب استثناؤها"""
        paths = [
            str(self.base_dir),
            str(self.base_dir / 'dist'),
            str(self.base_dir / 'build'),
            str(self.base_dir / 'backend'),
            str(self.base_dir / 'app'),
            str(self.base_dir / '*.exe'),
            str(self.base_dir / '*.spec'),
        ]
        return paths
    
    def get_exclusion_processes(self):
        """الحصول على العمليات المطلوب استثناؤها"""
        processes = [
            'python.exe',
            'pythonw.exe',
            'مكتب_عصام_الفت.exe',
            'server_manager.exe',
            'pyinstaller.exe',
        ]
        return processes
    
    def create_exclusion_script(self):
        """إنشاء سكريبت لإضافة الاستثناءات"""
        script_content = '''@echo off
echo إضافة استثناءات Windows Defender...
echo.

REM إضافة مجلدات للاستثناء
'''
        
        # إضافة المسارات
        for path in self.get_exclusion_paths():
            script_content += f'powershell -Command "Add-MpPreference -ExclusionPath \'{path}\'"\n'
        
        script_content += '''
REM إضافة عمليات للاستثناء
'''
        
        # إضافة العمليات
        for process in self.get_exclusion_processes():
            script_content += f'powershell -Command "Add-MpPreference -ExclusionProcess \'{process}\'"\n'
        
        script_content += '''
echo.
echo تم إضافة الاستثناءات بنجاح!
echo يرجى إعادة تشغيل الكمبيوتر لتطبيق التغييرات.
pause
'''
        
        script_file = self.base_dir / 'add_defender_exclusions.bat'
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_file
    
    def create_manual_instructions(self):
        """إنشاء تعليمات يدوية لإضافة الاستثناءات"""
        instructions = '''تعليمات إضافة استثناءات Windows Defender يدوياً
================================================

إذا لم يعمل السكريبت التلقائي، يمكنك إضافة الاستثناءات يدوياً:

1. افتح Windows Security (أمان Windows)
2. اذهب إلى "Virus & threat protection" (الحماية من الفيروسات والتهديدات)
3. انقر على "Manage settings" تحت "Virus & threat protection settings"
4. انقر على "Add or remove exclusions" (إضافة أو إزالة الاستثناءات)
5. انقر على "Add an exclusion" (إضافة استثناء)

أضف المجلدات التالية:
'''
        
        for path in self.get_exclusion_paths():
            instructions += f'- {path}\n'
        
        instructions += '''
أضف العمليات التالية:
'''
        
        for process in self.get_exclusion_processes():
            instructions += f'- {process}\n'
        
        instructions += '''
ملاحظات مهمة:
- تأكد من تشغيل Command Prompt كمدير
- قد تحتاج إلى إعادة تشغيل الكمبيوتر
- هذه الاستثناءات ضرورية لعمل البرنامج بشكل صحيح
- البرنامج آمن تماماً ولا يحتوي على أي فيروسات

للدعم الفني:
واتساب: 0201032540807
فيسبوك: https://www.facebook.com/almbarmg
'''
        
        instructions_file = self.base_dir / 'windows_defender_instructions.txt'
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        return instructions_file
    
    def get_pyinstaller_defender_options(self):
        """الحصول على خيارات PyInstaller لتجنب Windows Defender"""
        options = [
            # تقليل الكشف الخاطئ
            '--noupx',  # عدم استخدام UPX compression
            '--strip',  # إزالة معلومات التشخيص
            
            # استبعاد مكتبات مشبوهة
            '--exclude-module=tkinter',
            '--exclude-module=matplotlib',
            '--exclude-module=numpy',
            '--exclude-module=pandas',
            '--exclude-module=scipy',
            '--exclude-module=test',
            '--exclude-module=tests',
            '--exclude-module=unittest',
            '--exclude-module=pytest',
            '--exclude-module=setuptools',
            '--exclude-module=distutils',
            '--exclude-module=pip',
            '--exclude-module=wheel',
            '--exclude-module=pkg_resources',
            
            # تحسين الأمان
            '--exclude-module=pdb',
            '--exclude-module=pydoc',
            '--exclude-module=doctest',
            '--exclude-module=pickle',
            '--exclude-module=shelve',
            '--exclude-module=dbm',
            '--exclude-module=multiprocessing',
            '--exclude-module=concurrent',
            '--exclude-module=asyncio',
            
            # استبعاد مكتبات الشبكة المشبوهة
            '--exclude-module=ftplib',
            '--exclude-module=poplib',
            '--exclude-module=imaplib',
            '--exclude-module=nntplib',
            '--exclude-module=smtplib',
            '--exclude-module=telnetlib',
            '--exclude-module=xmlrpc',
            
            # استبعاد مكتبات النظام غير المطلوبة
            '--exclude-module=pty',
            '--exclude-module=tty',
            '--exclude-module=pipes',
            '--exclude-module=posix',
            '--exclude-module=pwd',
            '--exclude-module=spwd',
            '--exclude-module=grp',
            '--exclude-module=crypt',
            '--exclude-module=termios',
            '--exclude-module=resource',
            '--exclude-module=nis',
            '--exclude-module=syslog',
            '--exclude-module=commands',
            '--exclude-module=dl',
            '--exclude-module=DLFCN',
            
            # استبعاد مكتبات الصوت والصورة غير المطلوبة
            '--exclude-module=ossaudiodev',
            '--exclude-module=audioop',
            '--exclude-module=imageop',
            '--exclude-module=aifc',
            '--exclude-module=sunau',
            '--exclude-module=wave',
            '--exclude-module=chunk',
            '--exclude-module=colorsys',
            '--exclude-module=imghdr',
            '--exclude-module=sndhdr',
            
            # استبعاد مكتبات أخرى
            '--exclude-module=turtle',
            '--exclude-module=cmd',
            '--exclude-module=shlex',
        ]
        
        return options
    
    def create_clean_build_script(self):
        """إنشاء سكريبت بناء نظيف"""
        script_content = '''@echo off
echo بناء نظيف لتجنب Windows Defender...
echo.

REM تنظيف شامل
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build
if exist *.spec del *.spec
if exist __pycache__ rmdir /s /q __pycache__
for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"

REM تنظيف ملفات Python المؤقتة
del /s /q *.pyc 2>nul
del /s /q *.pyo 2>nul

REM إعادة تثبيت PyInstaller
python -m pip uninstall pyinstaller -y
python -m pip install pyinstaller

REM بناء نظيف
python build_exe_optimized.py

echo.
echo تم البناء النظيف!
pause
'''
        
        script_file = self.base_dir / 'clean_build.bat'
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_file
    
    def setup_all(self):
        """إعداد جميع ملفات Windows Defender"""
        print("🛡️ إعداد ملفات Windows Defender...")
        
        # إنشاء سكريبت الاستثناءات
        exclusion_script = self.create_exclusion_script()
        print(f"✅ تم إنشاء سكريبت الاستثناءات: {exclusion_script}")
        
        # إنشاء التعليمات اليدوية
        instructions_file = self.create_manual_instructions()
        print(f"✅ تم إنشاء ملف التعليمات: {instructions_file}")
        
        # إنشاء سكريبت البناء النظيف
        clean_script = self.create_clean_build_script()
        print(f"✅ تم إنشاء سكريبت البناء النظيف: {clean_script}")
        
        print("\n📋 الخطوات التالية:")
        print("1. شغل add_defender_exclusions.bat كمدير")
        print("2. أو اتبع التعليمات في windows_defender_instructions.txt")
        print("3. استخدم clean_build.bat للبناء النظيف")
        
        return {
            'exclusion_script': exclusion_script,
            'instructions_file': instructions_file,
            'clean_script': clean_script,
            'defender_options': self.get_pyinstaller_defender_options()
        }

def main():
    """الدالة الرئيسية"""
    config = WindowsDefenderConfig()
    result = config.setup_all()
    
    print("\n🎯 تم إعداد ملفات Windows Defender بنجاح!")
    print("📁 تحقق من الملفات المُنشأة في المجلد الحالي")
    
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
