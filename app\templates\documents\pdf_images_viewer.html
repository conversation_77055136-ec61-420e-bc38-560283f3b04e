{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .pdf-image-container {
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .pdf-image {
        max-width: 100%;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        border-radius: 5px;
    }
    
    .page-navigation {
        position: sticky;
        top: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        z-index: 100;
        margin-bottom: 20px;
    }
    
    .page-info {
        margin: 0 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-3">
        <div class="col-md-6">
            <h1 class="mb-0">{{ document.title }}</h1>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="{{ url_for('documents.view', document_id=document.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة
            </a>
            <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-success">
                <i class="fas fa-download me-1"></i>تنزيل
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <i class="fas fa-file-pdf me-2"></i>عرض المستند
        </div>
        <div class="card-body p-3">
            <div class="page-navigation">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button id="prev-page" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-chevron-right"></i> الصفحة السابقة
                        </button>
                        <button id="next-page" class="btn btn-sm btn-outline-primary">
                            الصفحة التالية <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="page-info">صفحة <span id="current-page">1</span> من <span id="total-pages">{{ image_paths|length }}</span></span>
                    </div>
                    <div>
                        <a href="{{ url_for('documents.serve_file', document_id=document.id) }}" target="_blank" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-external-link-alt"></i> فتح PDF الأصلي
                        </a>
                    </div>
                </div>
            </div>
            
            {% if image_paths %}
                {% for image_path in image_paths %}
                <div class="pdf-image-container" id="page-{{ loop.index }}" {% if loop.index > 1 %}style="display: none;"{% endif %}>
                    <img src="{{ url_for('static', filename=image_path) }}" class="pdf-image" alt="صفحة {{ loop.index }}">
                </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لم يتم العثور على صور لهذا المستند. قد يكون هناك مشكلة في تحويل ملف PDF إلى صور.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const totalPages = {{ image_paths|length }};
        let currentPage = 1;
        
        const prevPageBtn = document.getElementById('prev-page');
        const nextPageBtn = document.getElementById('next-page');
        const currentPageSpan = document.getElementById('current-page');
        
        // تحديث أزرار التنقل
        function updateNavigation() {
            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= totalPages;
            currentPageSpan.textContent = currentPage;
            
            // إخفاء جميع الصفحات
            for (let i = 1; i <= totalPages; i++) {
                const pageElement = document.getElementById(`page-${i}`);
                if (pageElement) {
                    pageElement.style.display = 'none';
                }
            }
            
            // إظهار الصفحة الحالية
            const currentPageElement = document.getElementById(`page-${currentPage}`);
            if (currentPageElement) {
                currentPageElement.style.display = 'block';
            }
        }
        
        // الانتقال إلى الصفحة السابقة
        prevPageBtn.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                updateNavigation();
            }
        });
        
        // الانتقال إلى الصفحة التالية
        nextPageBtn.addEventListener('click', function() {
            if (currentPage < totalPages) {
                currentPage++;
                updateNavigation();
            }
        });
        
        // تحديث أزرار التنقل عند تحميل الصفحة
        updateNavigation();
    });
</script>
{% endblock %}
