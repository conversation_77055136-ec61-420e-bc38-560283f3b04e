{% extends "base.html" %}

{% block title %}إدارة المستندات - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة المستندات</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('documents.add') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة مستند جديد
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>بحث وفلترة
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('documents.index') }}" id="searchForm">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="ابحث في العنوان أو الملاحظات..." value="{{ search_query }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="related_to" class="form-label">متعلق بـ</label>
                    <select class="form-select" id="related_to" name="related_to">
                        <option value="">الكل</option>
                        {% for option in related_to_options %}
                        <option value="{{ option.value }}" {% if related_to_filter == option.value %}selected{% endif %}>{{ option.label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="file_type" class="form-label">نوع الملف</label>
                    <select class="form-select" id="file_type" name="file_type">
                        <option value="">الكل</option>
                        {% for option in file_type_options %}
                        <option value="{{ option.value }}" {% if file_type_filter == option.value %}selected{% endif %}>{{ option.label }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end w-100">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                        <a href="{{ url_for('documents.index') }}" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-file-alt me-2"></i>قائمة المستندات
        </h5>
        <span class="badge bg-primary">{{ documents|length }} مستند</span>
    </div>
    <div class="card-body">
        {% if documents %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>العنوان</th>
                        <th>نوع الملف</th>
                        <th>متعلق بـ</th>
                        <th>تاريخ الرفع</th>
                        <th>تم الرفع بواسطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for document in documents %}
                    <tr>
                        <td>{{ document.id }}</td>
                        <td>{{ document.title }}</td>
                        <td>
                            {% if document.file_type == 'pdf' %}
                            <span class="badge bg-danger">PDF</span>
                            {% elif document.file_type in ['jpg', 'jpeg', 'png', 'gif'] %}
                            <span class="badge bg-success">صورة</span>
                            {% elif document.file_type in ['doc', 'docx'] %}
                            <span class="badge bg-primary">Word</span>
                            {% elif document.file_type in ['xls', 'xlsx'] %}
                            <span class="badge bg-success">Excel</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ document.file_type }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if document.related_to == 'owner' %}
                            <span class="badge bg-info">مالك: {{ document.related_name }}</span>
                            {% elif document.related_to == 'building' %}
                            <span class="badge bg-success">مبنى: {{ document.related_name }}</span>
                            {% elif document.related_to == 'unit' %}
                            <span class="badge bg-warning">وحدة: {{ document.related_name }}</span>
                            {% elif document.related_to == 'tenant' %}
                            <span class="badge bg-primary">مستأجر: {{ document.related_name }}</span>
                            {% elif document.related_to == 'contract' %}
                            <span class="badge bg-danger">عقد: {{ document.related_name }}</span>
                            {% elif document.related_to == 'transaction' %}
                            <span class="badge bg-secondary">معاملة: {{ document.related_name }}</span>
                            {% endif %}
                        </td>
                        <td>{{ document.upload_date|format_date }}</td>
                        <td>{{ document.uploaded_by_name or 'غير معروف' }}</td>
                        <td>
                            <a href="{{ url_for('documents.view', document_id=document.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                <i class="fas fa-info-circle"></i>
                            </a>
                            <a href="{{ url_for('documents.display_file', document_id=document.id) }}" class="btn btn-sm btn-primary" title="عرض الملف" target="_blank">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-sm btn-success" title="تنزيل">
                                <i class="fas fa-download"></i>
                            </a>
                            <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            لا توجد مستندات حتى الآن. <a href="{{ url_for('documents.add') }}">إضافة مستند جديد</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث النموذج عند تغيير أي حقل (باستثناء حقل البحث)
        const formElements = document.querySelectorAll('#searchForm select, #searchForm input[type="date"]');
        formElements.forEach(element => {
            element.addEventListener('change', function() {
                // تأخير التقديم قليلاً للسماح بتحديث القيم
                setTimeout(() => {
                    document.getElementById('searchForm').submit();
                }, 100);
            });
        });

        // إضافة تأثير تمييز للصفوف عند التمرير فوقها
        const tableRows = document.querySelectorAll('table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('table-active');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('table-active');
            });
        });
    });
</script>
{% endblock %}