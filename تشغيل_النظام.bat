@echo off
chcp 65001 >nul
title مكتب عصام الفت لإدارة الأملاك - تشغيل النظام

:main_menu
cls
echo ================================================================
echo                    مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.
echo اختر طريقة التشغيل:
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                      خيارات التشغيل                        │
echo ├─────────────────────────────────────────────────────────────┤
echo │ 1. تشغيل الخادم المحلي (وحدة التحكم)                      │
echo │ 2. تشغيل الواجهة الرسومية                                 │
echo │ 3. تشغيل خادم الشبكة                                       │
echo │ 4. تشغيل النسخة المحمولة                                   │
echo ├─────────────────────────────────────────────────────────────┤
echo │                      أدوات الإدارة                         │
echo ├─────────────────────────────────────────────────────────────┤
echo │ 5. تثبيت المتطلبات                                         │
echo │ 6. إعادة تعيين كلمة مرور المدير                           │
echo │ 7. إنشاء النسخة المحمولة                                   │
echo │ 8. إنشاء النسخة التنفيذية                                  │
echo │ 9. إنشاء جميع النسخ                                        │
echo ├─────────────────────────────────────────────────────────────┤
echo │                        معلومات                             │
echo ├─────────────────────────────────────────────────────────────┤
echo │ H. عرض المساعدة                                            │
echo │ I. معلومات النظام                                          │
echo │ 0. خروج                                                    │
echo └─────────────────────────────────────────────────────────────┘
echo.

set /p choice="اختر رقم أو حرف: "

if /i "%choice%"=="1" goto run_local
if /i "%choice%"=="2" goto run_gui
if /i "%choice%"=="3" goto run_network
if /i "%choice%"=="4" goto run_portable
if /i "%choice%"=="5" goto install_requirements
if /i "%choice%"=="6" goto reset_password
if /i "%choice%"=="7" goto create_portable
if /i "%choice%"=="8" goto create_executable
if /i "%choice%"=="9" goto create_all
if /i "%choice%"=="h" goto show_help
if /i "%choice%"=="i" goto show_info
if /i "%choice%"=="0" goto exit_program

echo.
echo ❌ اختيار غير صالح! يرجى المحاولة مرة أخرى.
timeout /t 2 >nul
goto main_menu

:run_local
echo.
echo 🚀 تشغيل الخادم المحلي...
python server_manager.py --console
goto end_operation

:run_gui
echo.
echo 🖥️ تشغيل الواجهة الرسومية...
python server_manager.py
goto end_operation

:run_network
echo.
echo 🌐 تشغيل خادم الشبكة...
python server_manager.py --network
goto end_operation

:run_portable
echo.
echo 📦 تشغيل النسخة المحمولة...
if exist run_portable.py (
    python run_portable.py
) else (
    echo ❌ ملف النسخة المحمولة غير موجود!
    echo يرجى إنشاء النسخة المحمولة أولاً (الخيار 7)
)
goto end_operation

:install_requirements
echo.
echo 📥 تثبيت المتطلبات...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    goto end_operation
)
pip install -r requirements.txt
echo ✅ تم تثبيت المتطلبات بنجاح!
goto end_operation

:reset_password
echo.
echo 🔑 إعادة تعيين كلمة مرور المدير...
python server_manager.py --reset-admin-password
goto end_operation

:create_portable
echo.
echo 📦 إنشاء النسخة المحمولة...
python create_portable_version.py
goto end_operation

:create_executable
echo.
echo 🔧 إنشاء النسخة التنفيذية...
python build_executable.py
goto end_operation

:create_all
echo.
echo 🏗️ إنشاء جميع النسخ...
call create_all_versions.bat
goto end_operation

:show_help
cls
echo ================================================================
echo                           المساعدة
echo ================================================================
echo.
echo 📖 دليل الاستخدام:
echo.
echo 1. الخادم المحلي: يعمل على هذا الجهاز فقط (127.0.0.1:5000)
echo 2. الواجهة الرسومية: واجهة مرئية لإدارة الخادم
echo 3. خادم الشبكة: يمكن الوصول إليه من أجهزة أخرى على الشبكة
echo 4. النسخة المحمولة: نسخة محسنة للتشغيل المحمول
echo.
echo 🔧 أدوات الإدارة:
echo 5. تثبيت المتطلبات: تثبيت مكتبات Python المطلوبة
echo 6. إعادة تعيين كلمة المرور: إعادة كلمة مرور المدير إلى admin123
echo 7-9. إنشاء النسخ: إنشاء نسخ مختلفة للتوزيع
echo.
echo 🔐 بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo 📞 الدعم الفني:
echo واتساب: 0201032540807
echo فيسبوك: https://www.facebook.com/almbarmg
echo.
pause
goto main_menu

:show_info
cls
echo ================================================================
echo                        معلومات النظام
echo ================================================================
echo.
echo 📋 معلومات المشروع:
echo الاسم: مكتب عصام الفت لإدارة الأملاك
echo الإصدار: 1.0.0
echo المطور: شركة المبرمج المصري
echo.
echo 🔧 المعلومات التقنية:
echo الإطار: Flask (Python)
echo قاعدة البيانات: SQLite
echo الواجهة: HTML, CSS, JavaScript, Bootstrap
echo الواجهة الرسومية: PyQt5
echo.
echo 📁 هيكل الملفات:
echo app/          - كود التطبيق الرئيسي
echo instance/     - قاعدة البيانات والإعدادات
echo uploads/      - الملفات المرفوعة
echo static/       - الملفات الثابتة
echo templates/    - قوالب HTML
echo.
echo 🌐 عناوين الوصول:
echo محلي: http://localhost:5000
echo شبكة: http://[IP_ADDRESS]:5000
echo.
pause
goto main_menu

:end_operation
echo.
echo ═══════════════════════════════════════════════════════════════
echo العملية مكتملة!
echo ═══════════════════════════════════════════════════════════════
echo.
set /p return="اضغط Enter للعودة إلى القائمة الرئيسية..."
goto main_menu

:exit_program
cls
echo ================================================================
echo                           وداعاً!
echo ================================================================
echo.
echo شكراً لاستخدام مكتب عصام الفت لإدارة الأملاك
echo.
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo.
echo ================================================================
timeout /t 3 >nul
exit /b 0
