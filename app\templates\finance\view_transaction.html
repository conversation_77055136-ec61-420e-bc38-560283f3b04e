{% extends "base.html" %}

{% block title %}تفاصيل المعاملة المالية - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">تفاصيل المعاملة المالية #{{ transaction.id }}</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('finance.edit_transaction', transaction_id=transaction.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('finance.transactions') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع
        </a>
        <a href="{{ url_for('reports.print_receipt', transaction_id=transaction.id) }}" class="btn btn-info" target="_blank">
            <i class="fas fa-print me-1"></i>طباعة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header {% if transaction.type == 'income' %}bg-success{% else %}bg-danger{% endif %} text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>بيانات المعاملة
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>رقم المعاملة</th>
                            <td>{{ transaction.id }}</td>
                        </tr>
                        <tr>
                            <th>النوع</th>
                            <td>
                                {% if transaction.type == 'income' %}
                                <span class="badge bg-success">إيراد</span>
                                {% else %}
                                <span class="badge bg-danger">مصروف</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>الفئة</th>
                            <td>
                                {% if transaction.category == 'rent' %}
                                إيجار
                                {% elif transaction.category == 'deposit' %}
                                تأمين
                                {% elif transaction.category == 'maintenance' %}
                                صيانة
                                {% elif transaction.category == 'utilities' %}
                                مرافق
                                {% elif transaction.category == 'taxes' %}
                                ضرائب
                                {% elif transaction.category == 'insurance' %}
                                تأمين
                                {% elif transaction.category == 'salary' %}
                                رواتب
                                {% elif transaction.category == 'commission' %}
                                عمولة
                                {% else %}
                                أخرى
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>المبلغ</th>
                            <td>{{ transaction.amount|format_currency }}</td>
                        </tr>
                        <tr>
                            <th>التاريخ</th>
                            <td>{{ transaction.transaction_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>الوصف</th>
                            <td>{{ transaction.description }}</td>
                        </tr>
                        <tr>
                            <th>طريقة الدفع</th>
                            <td>
                                {% if transaction.payment_method == 'cash' %}
                                نقدي
                                {% elif transaction.payment_method == 'bank_transfer' %}
                                تحويل بنكي
                                {% elif transaction.payment_method == 'check' %}
                                شيك
                                {% elif transaction.payment_method == 'credit_card' %}
                                بطاقة ائتمان
                                {% else %}
                                {{ transaction.payment_method }}
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>رقم المرجع</th>
                            <td>{{ transaction.reference_number or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>متعلق بـ</th>
                            <td>
                                {% if transaction.related_to == 'contract' %}
                                <a href="{{ url_for('tenants.view_contract', contract_id=transaction.related_id) }}">
                                    عقد #{{ transaction.related_id }}
                                </a>
                                {% elif transaction.related_to == 'unit' %}
                                <a href="{{ url_for('properties.view_unit', unit_id=transaction.related_id) }}">
                                    وحدة #{{ transaction.related_id }}
                                </a>
                                {% elif transaction.related_to == 'building' %}
                                <a href="{{ url_for('properties.view_building', building_id=transaction.related_id) }}">
                                    مبنى #{{ transaction.related_id }}
                                </a>
                                {% elif transaction.related_to == 'maintenance' %}
                                <a href="{{ url_for('properties.view_maintenance', maintenance_id=transaction.related_id) }}">
                                    صيانة #{{ transaction.related_id }}
                                </a>
                                {% elif transaction.related_to == 'owner' %}
                                <a href="{{ url_for('owners.view', owner_id=transaction.related_id) }}">
                                    مالك #{{ transaction.related_id }}
                                </a>
                                {% else %}
                                غير مرتبط
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>تم الإنشاء بواسطة</th>
                            <td>{{ transaction.created_by_name }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ transaction.created_at|format_date }}</td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ transaction.notes or 'لا توجد ملاحظات' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        {% if transaction.type == 'income' and transaction.category == 'rent' and transaction.related_to == 'contract' %}
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-contract me-2"></i>بيانات العقد
                </h5>
            </div>
            <div class="card-body">
                {% if contract %}
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>رقم العقد</th>
                            <td>{{ contract.contract_number or contract.id }}</td>
                        </tr>
                        <tr>
                            <th>المستأجر</th>
                            <td>
                                <a href="{{ url_for('tenants.view', tenant_id=contract.tenant_id) }}">
                                    {{ contract.tenant_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>الوحدة</th>
                            <td>
                                <a href="{{ url_for('properties.view_unit', unit_id=contract.unit_id) }}">
                                    {{ contract.unit_number }} - {{ contract.building_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ البداية</th>
                            <td>{{ contract.start_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ النهاية</th>
                            <td>{{ contract.end_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>قيمة الإيجار</th>
                            <td>{{ contract.rent_amount|format_currency }}</td>
                        </tr>
                        <tr>
                            <th>دورة الدفع</th>
                            <td>
                                {% if contract.payment_frequency == 'monthly' %}
                                شهري
                                {% elif contract.payment_frequency == 'quarterly' %}
                                ربع سنوي
                                {% elif contract.payment_frequency == 'biannual' %}
                                نصف سنوي
                                {% elif contract.payment_frequency == 'annual' %}
                                سنوي
                                {% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="mt-3">
                    <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>عرض تفاصيل العقد
                    </a>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    لا يمكن العثور على بيانات العقد المرتبط بهذه المعاملة.
                </div>
                {% endif %}
            </div>
        </div>
        {% elif transaction.type == 'expense' and transaction.category == 'maintenance' and transaction.related_to == 'maintenance' %}
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>بيانات الصيانة
                </h5>
            </div>
            <div class="card-body">
                {% if maintenance %}
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>رقم طلب الصيانة</th>
                            <td>{{ maintenance.id }}</td>
                        </tr>
                        <tr>
                            <th>الوحدة</th>
                            <td>
                                <a href="{{ url_for('properties.view_unit', unit_id=maintenance.unit_id) }}">
                                    {{ maintenance.unit_number }} - {{ maintenance.building_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الطلب</th>
                            <td>{{ maintenance.request_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>الوصف</th>
                            <td>{{ maintenance.description|truncate(100) }}</td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                {% if maintenance.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif maintenance.status == 'in_progress' %}
                                <span class="badge bg-info">قيد التنفيذ</span>
                                {% elif maintenance.status == 'completed' %}
                                <span class="badge bg-success">مكتمل</span>
                                {% elif maintenance.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإكمال</th>
                            <td>{{ maintenance.completion_date|format_date if maintenance.completion_date else 'غير مكتمل بعد' }}</td>
                        </tr>
                        <tr>
                            <th>التكلفة</th>
                            <td>{{ maintenance.cost|format_currency if maintenance.cost else 'غير محدد' }}</td>
                        </tr>
                    </tbody>
                </table>
                <div class="mt-3">
                    <a href="{{ url_for('properties.view_maintenance', maintenance_id=maintenance.id) }}" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>عرض تفاصيل الصيانة
                    </a>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    لا يمكن العثور على بيانات الصيانة المرتبطة بهذه المعاملة.
                </div>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>المستندات
                </h5>
            </div>
            <div class="card-body">
                {% if documents %}
                <div class="row">
                    {% for document in documents %}
                    <div class="col-md-6 mb-3">
                        <div class="card document-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ document.title }}</h5>
                                <p class="card-text text-muted">
                                    <small>
                                        <i class="fas fa-calendar-alt me-1"></i>{{ document.upload_date|format_date }}
                                    </small>
                                </p>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>تنزيل
                                    </a>
                                    <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مستندات مرفقة لهذه المعاملة.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('documents.add') }}?related_to=transaction&related_id={{ transaction.id }}" class="btn btn-info">
                        <i class="fas fa-plus-circle me-1"></i>إضافة مستند جديد
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
