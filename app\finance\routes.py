from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.finance import finance_bp
from app.forms import TransactionForm
from app.db_manager import query_db, insert_db, update_db, delete_db
from app.decorators import accountant_required
from datetime import datetime, timedelta
import os
from werkzeug.utils import secure_filename
from flask import current_app

def update_contract_fee_status(owner_id, amount, description):
    """
    تحديث حالة رسوم العقد عند تسجيل دفعة جديدة للمالك

    Args:
        owner_id: معرف المالك
        amount: مبلغ الدفعة
        description: وصف المعاملة

    Returns:
        bool: True إذا تم تحديث حالة رسوم العقد بنجاح، False خلاف ذلك
    """
    try:
        # البحث عن العقود التي تحتوي على رسوم غير مدفوعة بالكامل للمالك
        contracts = query_db('''
            SELECT c.id, c.contract_fee, c.contract_fee_paid, c.contract_fee_status,
                   u.unit_number, b.name as building_name, t.name as tenant_name
            FROM contracts c
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            JOIN tenants t ON c.tenant_id = t.id
            WHERE b.owner_id = ?
            AND c.contract_fee_status != 'paid'
            AND c.contract_fee > COALESCE(c.contract_fee_paid, 0)
            ORDER BY c.start_date DESC
        ''', (owner_id,))

        if not contracts:
            return False

        # التحقق من وصف المعاملة لمعرفة العقد المرتبط
        contract_to_update = None
        remaining_amount = amount

        # إذا كان الوصف يحتوي على معلومات محددة عن العقد
        for contract in contracts:
            unit_number = contract['unit_number']
            building_name = contract['building_name']
            tenant_name = contract['tenant_name']

            # التحقق مما إذا كان الوصف يحتوي على معلومات هذا العقد
            if (unit_number in description and
                (building_name in description or tenant_name in description)):
                contract_to_update = contract
                break

        # إذا لم يتم العثور على عقد محدد، نقوم بتحديث العقود بالترتيب حتى استنفاد المبلغ
        if not contract_to_update and contracts:
            for contract in contracts:
                contract_id = contract['id']
                contract_fee = contract['contract_fee'] or 0
                contract_fee_paid = contract['contract_fee_paid'] or 0
                remaining_fee = contract_fee - contract_fee_paid

                if remaining_amount <= 0:
                    break

                # تحديد المبلغ الذي سيتم إضافته لهذا العقد
                amount_to_add = min(remaining_amount, remaining_fee)
                new_paid_amount = contract_fee_paid + amount_to_add

                # تحديث حالة الدفع
                if new_paid_amount >= contract_fee:
                    new_status = 'paid'
                elif new_paid_amount > 0:
                    new_status = 'partially_paid'
                else:
                    new_status = 'unpaid'

                # تحديث العقد
                update_db('contracts', contract_id, {
                    'contract_fee_paid': new_paid_amount,
                    'contract_fee_status': new_status
                })

                # تحديث المبلغ المتبقي
                remaining_amount -= amount_to_add

                if remaining_amount <= 0:
                    break

        # إذا تم العثور على عقد محدد، نقوم بتحديثه فقط
        elif contract_to_update:
            contract_id = contract_to_update['id']
            contract_fee = contract_to_update['contract_fee'] or 0
            contract_fee_paid = contract_to_update['contract_fee_paid'] or 0
            remaining_fee = contract_fee - contract_fee_paid

            # تحديد المبلغ الذي سيتم إضافته
            amount_to_add = min(amount, remaining_fee)
            new_paid_amount = contract_fee_paid + amount_to_add

            # تحديث حالة الدفع
            if new_paid_amount >= contract_fee:
                new_status = 'paid'
            elif new_paid_amount > 0:
                new_status = 'partially_paid'
            else:
                new_status = 'unpaid'

            # تحديث العقد
            update_db('contracts', contract_id, {
                'contract_fee_paid': new_paid_amount,
                'contract_fee_status': new_status
            })

        return True
    except Exception as e:
        print(f"Error updating contract fee status: {str(e)}")
        return False

@finance_bp.route('/')
@login_required
@accountant_required
def index():
    """الصفحة الرئيسية للمالية"""
    # الحصول على إجمالي الإيرادات والمصروفات للشهر الحالي
    current_month = datetime.now().month
    current_year = datetime.now().year

    start_date = datetime(current_year, current_month, 1).date()
    if current_month == 12:
        end_date = datetime(current_year + 1, 1, 1).date() - timedelta(days=1)
    else:
        end_date = datetime(current_year, current_month + 1, 1).date() - timedelta(days=1)

    # إجمالي الإيرادات للشهر الحالي
    income_query = '''
        SELECT SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'income'
    '''
    income_result = query_db(income_query, (start_date.isoformat(), end_date.isoformat()), one=True)
    monthly_income = income_result['total'] if income_result and income_result['total'] is not None else 0

    # إجمالي المصروفات للشهر الحالي
    expense_query = '''
        SELECT SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'expense'
    '''
    expense_result = query_db(expense_query, (start_date.isoformat(), end_date.isoformat()), one=True)
    monthly_expense = expense_result['total'] if expense_result and expense_result['total'] is not None else 0

    # صافي الربح للشهر الحالي
    net_profit = monthly_income - monthly_expense

    # الحصول على آخر المعاملات المالية
    latest_transactions = query_db('''
        SELECT t.*, u.name as created_by_name,
               CASE
                   WHEN t.related_to = 'contract' THEN (
                       SELECT c.contract_number || ' - ' || te.name || ' (' || un.unit_number || ')'
                       FROM contracts c
                       JOIN tenants te ON c.tenant_id = te.id
                       JOIN units un ON c.unit_id = un.id
                       WHERE c.id = t.related_id
                   )
                   WHEN t.related_to = 'building' THEN (SELECT name FROM buildings WHERE id = t.related_id)
                   WHEN t.related_to = 'unit' THEN (
                       SELECT u.unit_number || ' - ' || b.name
                       FROM units u
                       JOIN buildings b ON u.building_id = b.id
                       WHERE u.id = t.related_id
                   )
                   ELSE NULL
               END as related_name
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        ORDER BY t.transaction_date DESC
        LIMIT 10
    ''')

    # الحصول على إحصائيات الإيرادات حسب الفئة
    income_by_category = query_db('''
        SELECT category, SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'income'
        GROUP BY category
        ORDER BY total DESC
    ''', (start_date.isoformat(), end_date.isoformat()))

    # الحصول على إحصائيات المصروفات حسب الفئة
    expense_by_category = query_db('''
        SELECT category, SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'expense'
        GROUP BY category
        ORDER BY total DESC
    ''', (start_date.isoformat(), end_date.isoformat()))

    return render_template('finance/index.html',
                           title='إدارة المالية',
                           monthly_income=monthly_income,
                           monthly_expense=monthly_expense,
                           net_profit=net_profit,
                           latest_transactions=latest_transactions,
                           income_by_category=income_by_category,
                           expense_by_category=expense_by_category,
                           current_month=start_date.strftime('%B %Y'))

@finance_bp.route('/transactions')
@login_required
@accountant_required
def transactions():
    """قائمة المعاملات المالية"""
    # الحصول على معايير التصفية من الـ URL
    transaction_type = request.args.get('type')
    category = request.args.get('category')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # بناء الاستعلام الأساسي
    query = '''
        SELECT t.*, u.name as created_by_name,
               CASE
                   WHEN t.related_to = 'contract' THEN (
                       SELECT c.contract_number || ' - ' || te.name || ' (' || un.unit_number || ')'
                       FROM contracts c
                       JOIN tenants te ON c.tenant_id = te.id
                       JOIN units un ON c.unit_id = un.id
                       WHERE c.id = t.related_id
                   )
                   WHEN t.related_to = 'building' THEN (SELECT name FROM buildings WHERE id = t.related_id)
                   WHEN t.related_to = 'unit' THEN (
                       SELECT u.unit_number || ' - ' || b.name
                       FROM units u
                       JOIN buildings b ON u.building_id = b.id
                       WHERE u.id = t.related_id
                   )
                   WHEN t.related_to = 'owner' THEN (SELECT name FROM owners WHERE id = t.related_id)
                   ELSE NULL
               END as related_name
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
    '''

    # إضافة شروط التصفية
    conditions = []
    params = []

    if transaction_type:
        conditions.append('t.type = ?')
        params.append(transaction_type)

    if category:
        conditions.append('t.category = ?')
        params.append(category)

    if start_date:
        conditions.append('t.transaction_date >= ?')
        params.append(start_date)

    if end_date:
        conditions.append('t.transaction_date <= ?')
        params.append(end_date)

    if conditions:
        query += ' WHERE ' + ' AND '.join(conditions)

    query += ' ORDER BY t.transaction_date DESC'

    # تنفيذ الاستعلام
    transactions_list = query_db(query, params)

    # الحصول على قائمة الفئات للتصفية
    categories = query_db('SELECT DISTINCT category FROM transactions ORDER BY category')

    return render_template('finance/transactions.html',
                           transactions=transactions_list,
                           categories=categories,
                           title='المعاملات المالية',
                           filters={
                               'type': transaction_type,
                               'category': category,
                               'start_date': start_date,
                               'end_date': end_date
                           })

@finance_bp.route('/transactions/add', methods=['GET', 'POST'])
@finance_bp.route('/add', methods=['GET', 'POST'])  # إضافة مسار بديل
@login_required
@accountant_required
def add_transaction():
    """إضافة معاملة مالية جديدة"""
    form = TransactionForm()

    # تحديث قائمة الخيارات بناءً على نوع العلاقة المحدد
    if form.related_to.data == 'contract':
        contracts = query_db('''
            SELECT c.id, c.contract_number, t.name as tenant_name, u.unit_number, b.name as building_name
            FROM contracts c
            JOIN tenants t ON c.tenant_id = t.id
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            WHERE c.status = 'active'
            ORDER BY t.name
        ''')
        form.related_id.choices = [(0, 'لا يوجد')] + [(contract['id'], f"{contract['contract_number'] or contract['id']} - {contract['tenant_name']} ({contract['unit_number']} - {contract['building_name']})") for contract in contracts]
    elif form.related_to.data == 'building':
        buildings = query_db('SELECT id, name FROM buildings ORDER BY name')
        form.related_id.choices = [(0, 'لا يوجد')] + [(building['id'], building['name']) for building in buildings]
    elif form.related_to.data == 'unit':
        units = query_db('''
            SELECT u.id, u.unit_number, b.name as building_name
            FROM units u
            JOIN buildings b ON u.building_id = b.id
            ORDER BY b.name, u.unit_number
        ''')
        form.related_id.choices = [(0, 'لا يوجد')] + [(unit['id'], f"{unit['unit_number']} - {unit['building_name']}") for unit in units]
    elif form.related_to.data == 'owner':
        owners = query_db('SELECT id, name FROM owners ORDER BY name')
        form.related_id.choices = [(0, 'لا يوجد')] + [(owner['id'], owner['name']) for owner in owners]
    else:
        form.related_id.choices = [(0, 'لا يوجد')]

    if form.validate_on_submit():
        data = {
            'transaction_date': form.transaction_date.data,
            'amount': form.amount.data,
            'type': form.type.data,
            'category': form.category.data,
            'description': form.description.data,
            'payment_method': form.payment_method.data,
            'reference_number': form.reference_number.data,
            'created_by': current_user.id,
            'created_at': datetime.now()
        }

        if form.related_to.data != 'other' and form.related_id.data != 0:
            data['related_to'] = form.related_to.data
            data['related_id'] = form.related_id.data

        transaction_id = insert_db('transactions', data)

        # تحديث حالة رسوم العقد إذا كانت المعاملة مرتبطة بمالك وكانت من فئة رسوم العقد
        if (form.related_to.data == 'owner' and form.related_id.data != 0 and
            form.category.data == 'contract_fee' and form.type.data == 'income'):
            owner_id = form.related_id.data
            amount = form.amount.data
            description = form.description.data or ''

            # تحديث حالة رسوم العقد
            update_result = update_contract_fee_status(owner_id, amount, description)
            # استخدام logging بدلاً من print للنصوص العربية
            if update_result:
                print(f"Contract fee status updated for owner {owner_id} with amount {amount}")
            else:
                print(f"No unpaid contracts found for owner {owner_id}")

        # معالجة المرفقات إذا تم تحميلها
        if form.attachment.data:
            # إنشاء مجلد المرفقات إذا لم يكن موجودًا
            upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'transactions')
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)

            # حفظ الملف
            filename = secure_filename(form.attachment.data.filename)
            file_path = os.path.join(upload_folder, f"{transaction_id}_{filename}")
            form.attachment.data.save(file_path)

            # إضافة المستند إلى قاعدة البيانات
            document_data = {
                'title': f"Receipt for transaction #{transaction_id}",
                'file_path': os.path.join('transactions', f"{transaction_id}_{filename}"),
                'upload_date': datetime.now().date(),
                'related_to': 'transaction',
                'related_id': transaction_id,
                'uploaded_by': current_user.id,
                'file_type': 'receipt',
                'document_type': 'receipt'
            }
            insert_db('documents', document_data)

        flash('تم إضافة المعاملة المالية بنجاح!', 'success')
        return redirect(url_for('finance.view_transaction', transaction_id=transaction_id))

    # تعيين التاريخ الافتراضي
    if request.method == 'GET':
        form.transaction_date.data = datetime.now().date()

        # إذا تم تمرير معلومات في الـ URL
        related_to = request.args.get('related_to')
        related_id = request.args.get('related_id', type=int)
        transaction_type = request.args.get('type')
        category = request.args.get('category')
        amount = request.args.get('amount', type=float)

        if related_to:
            form.related_to.data = related_to

        if related_id:
            form.related_id.data = related_id

        if transaction_type:
            form.type.data = transaction_type

        if category:
            form.category.data = category

        if amount:
            form.amount.data = amount

    return render_template('finance/transaction_form.html', form=form, title='إضافة معاملة مالية جديدة')

@finance_bp.route('/transactions/edit/<int:transaction_id>', methods=['GET', 'POST'])
@login_required
@accountant_required
def edit_transaction(transaction_id):
    """تعديل معاملة مالية"""
    transaction = query_db('SELECT * FROM transactions WHERE id = ?', (transaction_id,), one=True)

    if not transaction:
        flash('المعاملة المالية غير موجودة.', 'danger')
        return redirect(url_for('finance.transactions'))

    form = TransactionForm()

    # تحديث قائمة الخيارات بناءً على نوع العلاقة المحدد
    if form.related_to.data == 'contract' or transaction['related_to'] == 'contract':
        contracts = query_db('''
            SELECT c.id, c.contract_number, t.name as tenant_name, u.unit_number, b.name as building_name
            FROM contracts c
            JOIN tenants t ON c.tenant_id = t.id
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            ORDER BY t.name
        ''')
        form.related_id.choices = [(0, 'لا يوجد')] + [(contract['id'], f"{contract['contract_number'] or contract['id']} - {contract['tenant_name']} ({contract['unit_number']} - {contract['building_name']})") for contract in contracts]
    elif form.related_to.data == 'building' or transaction['related_to'] == 'building':
        buildings = query_db('SELECT id, name FROM buildings ORDER BY name')
        form.related_id.choices = [(0, 'لا يوجد')] + [(building['id'], building['name']) for building in buildings]
    elif form.related_to.data == 'unit' or transaction['related_to'] == 'unit':
        units = query_db('''
            SELECT u.id, u.unit_number, b.name as building_name
            FROM units u
            JOIN buildings b ON u.building_id = b.id
            ORDER BY b.name, u.unit_number
        ''')
        form.related_id.choices = [(0, 'لا يوجد')] + [(unit['id'], f"{unit['unit_number']} - {unit['building_name']}") for unit in units]
    elif form.related_to.data == 'owner' or transaction['related_to'] == 'owner':
        owners = query_db('SELECT id, name FROM owners ORDER BY name')
        form.related_id.choices = [(0, 'لا يوجد')] + [(owner['id'], owner['name']) for owner in owners]
    else:
        form.related_id.choices = [(0, 'لا يوجد')]

    if request.method == 'GET':
        form.transaction_date.data = transaction['transaction_date']
        form.amount.data = transaction['amount']
        form.type.data = transaction['type']
        form.category.data = transaction['category']
        form.description.data = transaction['description']
        form.related_to.data = transaction['related_to'] if transaction['related_to'] else 'other'
        form.related_id.data = transaction['related_id'] if transaction['related_id'] else 0
        form.payment_method.data = transaction['payment_method']
        form.reference_number.data = transaction['reference_number']

    if form.validate_on_submit():
        data = {
            'transaction_date': form.transaction_date.data,
            'amount': form.amount.data,
            'type': form.type.data,
            'category': form.category.data,
            'description': form.description.data,
            'payment_method': form.payment_method.data,
            'reference_number': form.reference_number.data,
            'updated_at': datetime.now()
        }

        if form.related_to.data != 'other' and form.related_id.data != 0:
            data['related_to'] = form.related_to.data
            data['related_id'] = form.related_id.data
        else:
            data['related_to'] = None
            data['related_id'] = None

        # الحصول على بيانات المعاملة القديمة للمقارنة
        old_transaction = query_db('SELECT * FROM transactions WHERE id = ?', (transaction_id,), one=True)

        # تحديث المعاملة
        update_db('transactions', transaction_id, data)

        # تحديث حالة رسوم العقد إذا كانت المعاملة مرتبطة بمالك وكانت من فئة رسوم العقد
        if (form.related_to.data == 'owner' and form.related_id.data != 0 and
            form.category.data == 'contract_fee' and form.type.data == 'income'):

            # التحقق مما إذا كان هناك تغيير في المبلغ أو المالك
            amount_changed = old_transaction['amount'] != form.amount.data
            owner_changed = (old_transaction['related_to'] != 'owner' or
                            old_transaction['related_id'] != form.related_id.data)

            if amount_changed or owner_changed:
                owner_id = form.related_id.data
                amount = form.amount.data
                description = form.description.data or ''

                # إذا تم تغيير المالك، يمكن إضافة منطق هنا لإعادة تعيين حالة رسوم العقد للمالك القديم
                # هذا الجزء اختياري ويمكن تطويره في المستقبل إذا لزم الأمر

                # تحديث حالة رسوم العقد للمالك الجديد
                update_result = update_contract_fee_status(owner_id, amount, description)
                if update_result:
                    print(f"Contract fee status updated for owner {owner_id} with amount {amount}")
                else:
                    print(f"No unpaid contracts found for owner {owner_id}")

        # معالجة المرفقات إذا تم تحميلها
        if form.attachment.data:
            # إنشاء مجلد المرفقات إذا لم يكن موجودًا
            upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'transactions')
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)

            # حفظ الملف
            filename = secure_filename(form.attachment.data.filename)
            file_path = os.path.join(upload_folder, f"{transaction_id}_{filename}")
            form.attachment.data.save(file_path)

            # إضافة المستند إلى قاعدة البيانات
            document_data = {
                'title': f"Receipt for transaction #{transaction_id}",
                'file_path': os.path.join('transactions', f"{transaction_id}_{filename}"),
                'upload_date': datetime.now().date(),
                'related_to': 'transaction',
                'related_id': transaction_id,
                'uploaded_by': current_user.id,
                'file_type': 'receipt',
                'document_type': 'receipt'
            }
            insert_db('documents', document_data)

        flash('تم تحديث المعاملة المالية بنجاح!', 'success')
        return redirect(url_for('finance.view_transaction', transaction_id=transaction_id))

    return render_template('finance/transaction_form.html', form=form, title='تعديل معاملة مالية', transaction=transaction)

@finance_bp.route('/transactions/view/<int:transaction_id>')
@login_required
@accountant_required
def view_transaction(transaction_id):
    """عرض تفاصيل المعاملة المالية"""
    transaction = query_db('''
        SELECT t.*, u.name as created_by_name,
               CASE
                   WHEN t.related_to = 'contract' THEN (
                       SELECT c.contract_number || ' - ' || te.name || ' (' || un.unit_number || ')'
                       FROM contracts c
                       JOIN tenants te ON c.tenant_id = te.id
                       JOIN units un ON c.unit_id = un.id
                       WHERE c.id = t.related_id
                   )
                   WHEN t.related_to = 'building' THEN (SELECT name FROM buildings WHERE id = t.related_id)
                   WHEN t.related_to = 'unit' THEN (
                       SELECT u.unit_number || ' - ' || b.name
                       FROM units u
                       JOIN buildings b ON u.building_id = b.id
                       WHERE u.id = t.related_id
                   )
                   WHEN t.related_to = 'owner' THEN (SELECT name FROM owners WHERE id = t.related_id)
                   ELSE NULL
               END as related_name
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        WHERE t.id = ?
    ''', (transaction_id,), one=True)

    if not transaction:
        flash('المعاملة المالية غير موجودة.', 'danger')
        return redirect(url_for('finance.transactions'))

    # الحصول على المستندات المرتبطة بالمعاملة
    documents = query_db('''
        SELECT d.*, u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.related_to = 'transaction' AND d.related_id = ?
        ORDER BY d.upload_date DESC
    ''', (transaction_id,))

    return render_template('finance/view_transaction.html',
                           transaction=transaction,
                           documents=documents,
                           title=f'تفاصيل المعاملة المالية: {transaction["id"]}')

@finance_bp.route('/transactions/delete/<int:transaction_id>', methods=['POST'])
@login_required
@accountant_required
def delete_transaction(transaction_id):
    """حذف معاملة مالية"""
    transaction = query_db('SELECT * FROM transactions WHERE id = ?', (transaction_id,), one=True)

    if not transaction:
        flash('المعاملة المالية غير موجودة.', 'danger')
        return redirect(url_for('finance.transactions'))

    # إذا كانت المعاملة مرتبطة بمالك وكانت من فئة رسوم العقد، نقوم بإعادة حساب المديونية
    if (transaction['related_to'] == 'owner' and
        transaction['category'] == 'contract_fee' and
        transaction['type'] == 'income'):

        owner_id = transaction['related_id']

        # الحصول على جميع العقود المرتبطة بالمالك
        contracts = query_db('''
            SELECT c.id, c.contract_fee,
                   (SELECT SUM(t.amount)
                    FROM transactions t
                    WHERE t.related_to = 'owner'
                    AND t.related_id = ?
                    AND t.category = 'contract_fee'
                    AND t.type = 'income'
                    AND t.id != ?) as total_paid
            FROM contracts c
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            WHERE b.owner_id = ?
        ''', (owner_id, transaction_id, owner_id))

        # تحديث حالة الدفع لكل عقد
        for contract in contracts:
            contract_id = contract['id']
            contract_fee = contract['contract_fee'] or 0
            total_paid = contract['total_paid'] or 0

            # تحديد حالة الدفع الجديدة
            if total_paid >= contract_fee:
                new_status = 'paid'
            elif total_paid > 0:
                new_status = 'partially_paid'
            else:
                new_status = 'unpaid'

            # تحديث العقد
            update_db('contracts', contract_id, {
                'contract_fee_paid': total_paid,
                'contract_fee_status': new_status
            })

    # حذف المستندات المرتبطة بالمعاملة
    query_db('DELETE FROM documents WHERE related_to = "transaction" AND related_id = ?', (transaction_id,))

    # حذف المعاملة
    delete_db('transactions', transaction_id)

    flash('تم حذف المعاملة المالية بنجاح!', 'success')
    return redirect(url_for('finance.transactions'))

@finance_bp.route('/api/related-options')
@login_required
@accountant_required
def get_related_options():
    """الحصول على خيارات المعرف المرتبط بناءً على نوع العلاقة"""
    related_to = request.args.get('related_to')
    options = []

    if related_to == 'contract':
        contracts = query_db('''
            SELECT c.id, c.contract_number, t.name as tenant_name, u.unit_number, b.name as building_name
            FROM contracts c
            JOIN tenants t ON c.tenant_id = t.id
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            WHERE c.status = 'active'
            ORDER BY t.name
        ''')
        options = [{'id': 0, 'text': 'لا يوجد'}] + [
            {'id': contract['id'], 'text': f"{contract['contract_number'] or contract['id']} - {contract['tenant_name']} ({contract['unit_number']} - {contract['building_name']})"}
            for contract in contracts
        ]
    elif related_to == 'building':
        buildings = query_db('SELECT id, name FROM buildings ORDER BY name')
        options = [{'id': 0, 'text': 'لا يوجد'}] + [
            {'id': building['id'], 'text': building['name']}
            for building in buildings
        ]
    elif related_to == 'unit':
        units = query_db('''
            SELECT u.id, u.unit_number, b.name as building_name
            FROM units u
            JOIN buildings b ON u.building_id = b.id
            ORDER BY b.name, u.unit_number
        ''')
        options = [{'id': 0, 'text': 'لا يوجد'}] + [
            {'id': unit['id'], 'text': f"{unit['unit_number']} - {unit['building_name']}"}
            for unit in units
        ]
    elif related_to == 'owner':
        owners = query_db('SELECT id, name FROM owners ORDER BY name')
        options = [{'id': 0, 'text': 'لا يوجد'}] + [
            {'id': owner['id'], 'text': owner['name']}
            for owner in owners
        ]
    else:
        options = [{'id': 0, 'text': 'لا يوجد'}]

    return jsonify(options)

@finance_bp.route('/summary')
@login_required
@accountant_required
def summary():
    """ملخص مالي"""
    # الحصول على معايير التصفية من الـ URL
    year = request.args.get('year', type=int, default=datetime.now().year)
    month = request.args.get('month', type=int, default=0)  # 0 يعني كل الشهور

    # تحديد نطاق التاريخ
    if month > 0:
        start_date = datetime(year, month, 1).date()
        if month == 12:
            end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)
        period_name = f"{start_date.strftime('%B')} {year}"
    else:
        start_date = datetime(year, 1, 1).date()
        end_date = datetime(year, 12, 31).date()
        period_name = str(year)

    # إجمالي الإيرادات
    income_query = '''
        SELECT SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'income'
    '''
    income_result = query_db(income_query, (start_date.isoformat(), end_date.isoformat()), one=True)
    total_income = income_result['total'] if income_result and income_result['total'] is not None else 0

    # إجمالي المصروفات
    expense_query = '''
        SELECT SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'expense'
    '''
    expense_result = query_db(expense_query, (start_date.isoformat(), end_date.isoformat()), one=True)
    total_expense = expense_result['total'] if expense_result and expense_result['total'] is not None else 0

    # صافي الربح
    net_profit = total_income - total_expense

    # الإيرادات حسب الفئة
    income_by_category = query_db('''
        SELECT category, SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'income'
        GROUP BY category
        ORDER BY total DESC
    ''', (start_date.isoformat(), end_date.isoformat()))

    # المصروفات حسب الفئة
    expense_by_category = query_db('''
        SELECT category, SUM(amount) as total
        FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        AND type = 'expense'
        GROUP BY category
        ORDER BY total DESC
    ''', (start_date.isoformat(), end_date.isoformat()))

    # الإيرادات حسب الشهر (إذا كان النطاق سنة كاملة)
    income_by_month = []
    expense_by_month = []

    if month == 0:
        for m in range(1, 13):
            m_start = datetime(year, m, 1).date()
            if m == 12:
                m_end = datetime(year + 1, 1, 1).date() - timedelta(days=1)
            else:
                m_end = datetime(year, m + 1, 1).date() - timedelta(days=1)

            # الإيرادات للشهر
            m_income = query_db('''
                SELECT SUM(amount) as total
                FROM transactions
                WHERE transaction_date BETWEEN ? AND ?
                AND type = 'income'
            ''', (m_start.isoformat(), m_end.isoformat()), one=True)

            income_by_month.append({
                'month': m_start.strftime('%B'),
                'total': m_income['total'] if m_income and m_income['total'] is not None else 0
            })

            # المصروفات للشهر
            m_expense = query_db('''
                SELECT SUM(amount) as total
                FROM transactions
                WHERE transaction_date BETWEEN ? AND ?
                AND type = 'expense'
            ''', (m_start.isoformat(), m_end.isoformat()), one=True)

            expense_by_month.append({
                'month': m_start.strftime('%B'),
                'total': m_expense['total'] if m_expense and m_expense['total'] is not None else 0
            })

    # إعداد البيانات للرسوم البيانية
    time_labels = []
    income_data = []
    expense_data = []

    # إذا كان النطاق سنة كاملة، استخدام الأشهر
    if month == 0:
        for item in income_by_month:
            time_labels.append(item['month'])
            income_data.append(item['total'])

        for item in expense_by_month:
            expense_data.append(item['total'])
    else:
        # في حالة عدم وجود بيانات شهرية، استخدام الفئات
        if income_by_category:
            for item in income_by_category:
                category_name = item['category']
                if category_name == 'rent':
                    category_name = 'إيجار'
                elif category_name == 'deposit':
                    category_name = 'تأمين'
                elif category_name == 'other':
                    category_name = 'أخرى'

                time_labels.append(category_name)
                income_data.append(item['total'])

        if expense_by_category:
            # معالجة بيانات المصروفات
            for item in expense_by_category:
                category_name = item['category']
                if category_name == 'maintenance':
                    category_name = 'صيانة'
                elif category_name == 'utilities':
                    category_name = 'مرافق'
                elif category_name == 'taxes':
                    category_name = 'ضرائب'
                elif category_name == 'insurance':
                    category_name = 'تأمين'
                elif category_name == 'salary':
                    category_name = 'رواتب'
                elif category_name == 'commission':
                    category_name = 'عمولة'
                elif category_name == 'other':
                    category_name = 'أخرى'

                expense_data.append(item['total'])

    # إعداد بيانات المباني - تجاهل هذا الاستعلام حاليًا بسبب مشكلة في هيكل الجدول
    income_by_building = []

    return render_template('finance/summary.html',
                           title='الملخص المالي',
                           period_name=period_name,
                           total_income=total_income,
                           total_expense=total_expense,
                           net_profit=net_profit,
                           income_by_category=income_by_category,
                           expense_by_category=expense_by_category,
                           income_by_month=income_by_month,
                           expense_by_month=expense_by_month,
                           income_by_building=income_by_building,
                           time_labels=time_labels,
                           income_data=income_data,
                           expense_data=expense_data,
                           year=year,
                           month=month)
