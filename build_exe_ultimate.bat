@echo off
chcp 65001 >nul
title إنشاء ملف EXE محسن - مكتب عصام الفت

echo ================================================================
echo                    إنشاء ملف EXE محسن
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

REM تحديد مجلد العمل الحالي
cd /d "%~dp0"

echo 🔍 التحقق من النظام...

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر
)

echo.
echo 📦 تحديث pip وتثبيت المتطلبات...
python -m pip install --upgrade pip
python -m pip install pyinstaller
python -m pip install -r requirements.txt

echo.
echo 🧹 تنظيف البناءات السابقة...
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build
if exist *.spec del *.spec

echo.
echo 🔨 إنشاء الملف التنفيذي المحسن...
echo ⏳ هذا قد يستغرق عدة دقائق...
echo.

REM استخدام السكريبت المحسن
python build_exe_optimized.py

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم إنشاء الملف التنفيذي بنجاح!
    echo.
    echo 📁 المجلد: dist\مكتب_عصام_الفت\
    echo 🚀 الملف التنفيذي: dist\مكتب_عصام_الفت\مكتب_عصام_الفت.exe
    echo 📋 ملف التشغيل: dist\مكتب_عصام_الفت\تشغيل_النظام.bat
    echo.
    echo 🎯 الميزات المحسنة:
    echo   ✓ حجم أصغر وأداء أفضل
    echo   ✓ حماية من Windows Defender
    echo   ✓ دعم كامل للأحرف العربية
    echo   ✓ إعداد تلقائي للمجلدات
    echo   ✓ ملفات تشغيل مساعدة
    echo.
    echo 📋 يمكنك الآن نسخ مجلد "مكتب_عصام_الفت" إلى أي جهاز Windows
    echo.
    
    REM فتح مجلد النتيجة
    set /p open_folder="هل تريد فتح مجلد النتيجة؟ (y/n): "
    if /i "%open_folder%"=="y" (
        explorer "dist\مكتب_عصام_الفت"
    )
    
) else (
    echo.
    echo ❌ فشل في إنشاء الملف التنفيذي
    echo.
    echo 💡 حلول مقترحة:
    echo 1. تأكد من تثبيت جميع المتطلبات
    echo 2. أغلق أي برامج مكافحة فيروسات مؤقتاً
    echo 3. شغل Command Prompt كمدير
    echo 4. تأكد من وجود مساحة كافية على القرص
    echo.
    echo 📞 للدعم الفني: واتساب 0201032540807
    echo.
)

echo ================================================================
echo                        انتهت العملية
echo ================================================================

pause
