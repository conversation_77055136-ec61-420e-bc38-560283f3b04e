"""
نظام إدارة مكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

class Config:
    """فئة الإعدادات الأساسية للتطبيق"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'مفتاح_سري_افتراضي_للتطوير'
    DATABASE_PATH = os.environ.get('DATABASE_PATH') or 'instance/realestate.db'
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 ميجابايت كحد أقصى لحجم الملفات المرفوعة

    # إعدادات البريد الإلكتروني
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS') == 'True'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER')

    # إعدادات التطبيق
    CURRENCY = 'ريال سعودي'
    CURRENCY_SYMBOL = 'ر.س'
    DATE_FORMAT = '%Y-%m-%d'

    # إعدادات التنبيهات
    CONTRACT_EXPIRY_ALERT_DAYS = 7  # عدد الأيام قبل انتهاء العقد للتنبيه
