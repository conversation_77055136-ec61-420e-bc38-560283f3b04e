{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-3">
        <div class="col-md-6">
            <h1 class="mb-0">{{ document.title }}</h1>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="{{ url_for('documents.view', document_id=document.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة
            </a>
            <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-success">
                <i class="fas fa-download me-1"></i>تنزيل
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <i class="fas fa-file me-2"></i>معاينة المستند
        </div>
        <div class="card-body p-0">
            {% if file_type == 'image' %}
            <div class="text-center p-3">
                <img src="{{ file_url }}" class="img-fluid" alt="{{ document.title }}">
            </div>
            {% elif file_type == 'pdf' %}
            <div class="ratio ratio-16x9">
                <iframe src="{{ file_url }}" allowfullscreen></iframe>
            </div>
            {% else %}
            <div class="alert alert-info m-3">
                <i class="fas fa-info-circle me-2"></i>لا يمكن معاينة هذا النوع من الملفات. يرجى تنزيل الملف لعرضه.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
