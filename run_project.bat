@echo off
chcp 65001 >nul
title مكتب عصام الفت لإدارة الأملاك

:main_menu
cls
echo ================================================================
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.
echo اختر طريقة التشغيل:
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                        خيارات التشغيل                      │
echo ├─────────────────────────────────────────────────────────────┤
echo │ 1. تشغيل الواجهة الرسومية (GUI)                           │
echo │ 2. تشغيل في وضع وحدة التحكم                              │
echo │ 3. تشغيل خادم الشبكة                                      │
echo │ 4. تشغيل خادم مخصص (منفذ مختلف)                          │
echo ├─────────────────────────────────────────────────────────────┤
echo │                        إعداد المشروع                       │
echo ├─────────────────────────────────────────────────────────────┤
echo │ 5. تثبيت المتطلبات                                         │
echo │ 6. إنشاء ملف EXE                                           │
echo │ 7. إعداد Windows Defender                                  │
echo │ 8. تنظيف المشروع                                           │
echo ├─────────────────────────────────────────────────────────────┤
echo │                        معلومات                             │
echo ├─────────────────────────────────────────────────────────────┤
echo │ H. عرض المساعدة                                            │
echo │ I. معلومات النظام                                          │
echo │ 0. خروج                                                    │
echo └─────────────────────────────────────────────────────────────┘
echo.

set /p choice="اختر رقم أو حرف: "

if "%choice%"=="1" goto run_gui
if "%choice%"=="2" goto run_console
if "%choice%"=="3" goto run_network
if "%choice%"=="4" goto run_custom
if "%choice%"=="5" goto install_requirements
if "%choice%"=="6" goto build_exe
if "%choice%"=="7" goto setup_defender
if "%choice%"=="8" goto clean_project
if /i "%choice%"=="h" goto show_help
if /i "%choice%"=="i" goto show_info
if "%choice%"=="0" goto exit_program

echo.
echo ❌ اختيار غير صالح! يرجى المحاولة مرة أخرى.
timeout /t 2 >nul
goto main_menu

:run_gui
cls
echo ================================================================
echo                    تشغيل الواجهة الرسومية
echo ================================================================
echo.
echo 🚀 بدء تشغيل الواجهة الرسومية...
echo.

python run_project.py
goto end_operation

:run_console
cls
echo ================================================================
echo                  تشغيل في وضع وحدة التحكم
echo ================================================================
echo.
echo 🖥️ بدء تشغيل الخادم في وضع وحدة التحكم...
echo 🌐 سيكون متاحاً على: http://localhost:5000
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo.

python run_project.py --console
goto end_operation

:run_network
cls
echo ================================================================
echo                      تشغيل خادم الشبكة
echo ================================================================
echo.
echo 🌐 بدء تشغيل خادم الشبكة...
echo 📡 سيكون متاحاً لجميع الأجهزة على الشبكة
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo.

python run_project.py --network
goto end_operation

:run_custom
cls
echo ================================================================
echo                      تشغيل خادم مخصص
echo ================================================================
echo.

set /p custom_host="أدخل عنوان IP (اتركه فارغاً للافتراضي 0.0.0.0): "
if "%custom_host%"=="" set custom_host=0.0.0.0

set /p custom_port="أدخل رقم المنفذ (اتركه فارغاً للافتراضي 5000): "
if "%custom_port%"=="" set custom_port=5000

echo.
echo 🚀 بدء تشغيل الخادم على %custom_host%:%custom_port%...
echo.

python run_project.py --network --host %custom_host% --port %custom_port%
goto end_operation

:install_requirements
cls
echo ================================================================
echo                      تثبيت المتطلبات
echo ================================================================
echo.
echo 📦 تثبيت المتطلبات...
echo.

python run_project.py --install
goto end_operation

:build_exe
cls
echo ================================================================
echo                      إنشاء ملف EXE
echo ================================================================
echo.
echo 🔨 بدء إنشاء ملف EXE...
echo ⏳ هذا قد يستغرق عدة دقائق...
echo.

python run_project.py --build-exe
goto end_operation

:setup_defender
cls
echo ================================================================
echo                   إعداد Windows Defender
echo ================================================================
echo.
echo 🛡️ إعداد Windows Defender...
echo.

python run_project.py --setup-defender
goto end_operation

:clean_project
cls
echo ================================================================
echo                      تنظيف المشروع
echo ================================================================
echo.
echo 🧹 تنظيف ملفات المشروع...
echo.

REM حذف مجلدات البناء
if exist dist (
    echo حذف مجلد dist...
    rmdir /s /q dist
)

if exist build (
    echo حذف مجلد build...
    rmdir /s /q build
)

REM حذف ملفات spec
if exist *.spec (
    echo حذف ملفات spec...
    del *.spec
)

REM حذف مجلدات __pycache__
echo حذف مجلدات __pycache__...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"

REM حذف ملفات Python المؤقتة
echo حذف ملفات Python المؤقتة...
del /s /q *.pyc 2>nul
del /s /q *.pyo 2>nul

REM حذف ملفات السجل
if exist *.log (
    echo حذف ملفات السجل...
    del *.log
)

echo.
echo ✅ تم تنظيف المشروع بنجاح!
goto end_operation

:show_help
cls
echo ================================================================
echo                          المساعدة
echo ================================================================
echo.
echo 📖 دليل الاستخدام:
echo.
echo 🖥️ الواجهة الرسومية:
echo    - أسهل طريقة للاستخدام
echo    - واجهة بديهية بالعربية
echo    - إدارة كاملة للخادم
echo.
echo 🖥️ وضع وحدة التحكم:
echo    - للخوادم والاستخدام المتقدم
echo    - أداء أفضل
echo    - مناسب للخوادم
echo.
echo 🌐 خادم الشبكة:
echo    - متاح لجميع الأجهزة على الشبكة
echo    - مشاركة النظام مع فريق العمل
echo    - يتطلب إعدادات Firewall
echo.
echo 🔨 إنشاء ملف EXE:
echo    - ملف مستقل لا يتطلب Python
echo    - سهل التوزيع والتثبيت
echo    - يعمل على أي جهاز Windows
echo.
echo 📞 للدعم الفني:
echo    واتساب: 0201032540807
echo    فيسبوك: https://www.facebook.com/almbarmg
echo.
pause
goto main_menu

:show_info
cls
echo ================================================================
echo                        معلومات النظام
echo ================================================================
echo.
echo 📋 معلومات المشروع:
echo الاسم: مكتب عصام الفت لإدارة الأملاك
echo الإصدار: 1.0.0
echo المطور: شركة المبرمج المصري
echo.
echo 🔧 معلومات تقنية:
echo اللغة: Python
echo الإطار: Flask
echo الواجهة الرسومية: PyQt5
echo قاعدة البيانات: SQLite
echo.
echo 📁 هيكل الملفات:
echo app/          - كود التطبيق الرئيسي
echo backend/      - خادم Flask منفصل
echo instance/     - قاعدة البيانات والإعدادات
echo uploads/      - الملفات المرفوعة
echo static/       - الملفات الثابتة
echo templates/    - قوالب HTML
echo.
echo 🌐 عناوين الوصول:
echo محلي: http://localhost:5000
echo شبكة: http://[IP_ADDRESS]:5000
echo.
pause
goto main_menu

:end_operation
echo.
echo ═══════════════════════════════════════════════════════════════
echo العملية مكتملة!
echo ═══════════════════════════════════════════════════════════════
echo.
set /p return="اضغط Enter للعودة إلى القائمة الرئيسية..."
goto main_menu

:exit_program
cls
echo ================================================================
echo                           وداعاً!
echo ================================================================
echo.
echo شكراً لاستخدام مكتب عصام الفت لإدارة الأملاك
echo.
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo.
echo ================================================================
timeout /t 3 >nul
exit /b 0
