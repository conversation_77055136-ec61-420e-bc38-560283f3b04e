{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة المستخدمين</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('auth.add_user') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة مستخدم جديد
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>آخر تسجيل دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.name }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.role == 'admin' %}
                            <span class="badge bg-danger">مدير</span>
                            {% elif user.role == 'accountant' %}
                            <span class="badge bg-success">محاسب</span>
                            {% elif user.role == 'data_entry' %}
                            <span class="badge bg-info">مدخل بيانات</span>
                            {% endif %}
                        </td>
                        <td>{{ user.last_login|format_date if user.last_login else 'لم يسجل الدخول بعد' }}</td>
                        <td>
                            <a href="{{ url_for('auth.edit_user', user_id=user.id) }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% if user.id != current_user.id %}
                            <form action="{{ url_for('auth.delete_user', user_id=user.id) }}" method="POST" class="d-inline">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستخدم؟">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            لا يوجد مستخدمين حتى الآن. <a href="{{ url_for('auth.add_user') }}">إضافة مستخدم جديد</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
