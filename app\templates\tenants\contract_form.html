{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.tenant_id.label(class="form-label") }}
                        {% if form.tenant_id.errors %}
                            {{ form.tenant_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.tenant_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.tenant_id(class="form-select") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.unit_id.label(class="form-label") }}
                        {% if form.unit_id.errors %}
                            {{ form.unit_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.unit_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.unit_id(class="form-select") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.contract_number.label(class="form-label") }}
                        {% if form.contract_number.errors %}
                            {{ form.contract_number(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.contract_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.contract_number(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.start_date.label(class="form-label") }}
                            {% if form.start_date.errors %}
                                {{ form.start_date(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.start_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.start_date(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.end_date.label(class="form-label") }}
                            {% if form.end_date.errors %}
                                {{ form.end_date(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.end_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.end_date(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.rent_amount.label(class="form-label") }}
                            {% if form.rent_amount.errors %}
                                {{ form.rent_amount(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.rent_amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.rent_amount(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.payment_frequency.label(class="form-label") }}
                            {% if form.payment_frequency.errors %}
                                {{ form.payment_frequency(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.payment_frequency.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.payment_frequency(class="form-control", list="paymentFrequencyOptions") }}
                                <datalist id="paymentFrequencyOptions">
                                    <option value="monthly">شهري</option>
                                    <option value="quarterly">ربع سنوي</option>
                                    <option value="biannual">نصف سنوي</option>
                                    <option value="annual">سنوي</option>
                                    <option value="one_payment">دفعة واحدة</option>
                                    <option value="two_payments">دفعتين</option>
                                    <option value="three_payments">ثلاث دفعات</option>
                                    <option value="four_payments">أربع دفعات</option>
                                </datalist>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.deposit_amount.label(class="form-label") }}
                            {% if form.deposit_amount.errors %}
                                {{ form.deposit_amount(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.deposit_amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.deposit_amount(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.contract_fee.label(class="form-label") }}
                            {% if form.contract_fee.errors %}
                                {{ form.contract_fee(class="form-control is-invalid", id="contract_fee", oninput="calculateRemaining()") }}
                                <div class="invalid-feedback">
                                    {% for error in form.contract_fee.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.contract_fee(class="form-control", id="contract_fee", oninput="calculateRemaining()") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.contract_fee_paid.label(class="form-label") }}
                            {% if form.contract_fee_paid.errors %}
                                {{ form.contract_fee_paid(class="form-control is-invalid", id="contract_fee_paid", oninput="calculateRemaining()") }}
                                <div class="invalid-feedback">
                                    {% for error in form.contract_fee_paid.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.contract_fee_paid(class="form-control", id="contract_fee_paid", oninput="calculateRemaining()") }}
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            <label class="form-label">المبلغ المتبقي</label>
                            <input type="text" class="form-control" id="remaining_fee" readonly>
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.contract_fee_status.label(class="form-label") }}
                            {% if form.contract_fee_status.errors %}
                                {{ form.contract_fee_status(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.contract_fee_status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.contract_fee_status(class="form-select", id="contract_fee_status") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {% if form.status.errors %}
                                {{ form.status(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.status(class="form-control", list="statusOptions") }}
                                <datalist id="statusOptions">
                                    <option value="active">نشط</option>
                                    <option value="expired">منتهي</option>
                                    <option value="terminated">ملغي</option>
                                    <option value="renewed">مجدد</option>
                                    <option value="pending">معلق</option>
                                    <option value="review">تحت المراجعة</option>
                                </datalist>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.terms.label(class="form-label") }}
                        {% if form.terms.errors %}
                            {{ form.terms(class="form-control is-invalid", rows=4) }}
                            <div class="invalid-feedback">
                                {% for error in form.terms.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.terms(class="form-control", rows=4) }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('tenants.contracts') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حساب المبلغ المتبقي عند تحميل الصفحة
        calculateRemaining();
    });

    function calculateRemaining() {
        const contractFee = parseFloat(document.getElementById('contract_fee').value) || 0;
        const contractFeePaid = parseFloat(document.getElementById('contract_fee_paid').value) || 0;
        const remainingFee = contractFee - contractFeePaid;
        const feeStatusSelect = document.getElementById('contract_fee_status');

        // عرض المبلغ المتبقي مع تنسيق العملة
        document.getElementById('remaining_fee').value = remainingFee.toFixed(2) + ' {{ config["CURRENCY_SYMBOL"] }}';

        // تحديث حالة دفع الرسوم تلقائيًا
        if (contractFee <= 0) {
            // لا توجد رسوم
            feeStatusSelect.value = 'paid';
        } else if (contractFeePaid <= 0) {
            // لم يتم دفع أي مبلغ
            feeStatusSelect.value = 'unpaid';
        } else if (contractFeePaid >= contractFee) {
            // تم دفع كامل المبلغ
            feeStatusSelect.value = 'paid';
        } else {
            // تم دفع جزء من المبلغ
            feeStatusSelect.value = 'partially_paid';
        }
    }
</script>
{% endblock %}