# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# إضافة المجلد الحالي إلى مسار البحث
sys.path.insert(0, r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود')

block_cipher = None

a = Analysis(
    ['server_manager.py'],
    pathex=[r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود'],
    binaries=[],
    datas=[
        (r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود/app', 'app'),
        (r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود/config.py', '.'),
        (r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود/schema.sql', '.'),
        (r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود/file.ico', '.'),
        (r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود/file.jpeg', '.'),
    ],
    hiddenimports=[
        'flask',
        'flask_login', 
        'flask_bcrypt',
        'flask_wtf',
        'werkzeug',
        'jinja2',
        'sqlite3',
        'bcrypt',
        'email_validator',
        'itsdangerous',
        'markupsafe',
        'wtforms',
        'datetime',
        'json',
        'os',
        'sys',
        'pathlib',
        'shutil',
        'tempfile',
        'io',
        'collections',
        'functools',
        'itertools',
        're',
        'string',
        'time',
        'hashlib',
        'secrets',
        'uuid',
        'base64',
        'urllib',
        'urllib.parse',
        'socket',
        'threading',
        'subprocess',
        'webbrowser',
        'argparse',
        'logging',
        'traceback',
        'importlib',
        'importlib.util'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'pytest',
        'setuptools',
        'distutils',
        'pip',
        'wheel',
        'pkg_resources',
        'PyQt5',
        'qrcode',
        'PIL'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='مكتب_عصام_الفت',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r'H:\مبيعات 2025\مشروع العقارات (أ-عصام)\السورس كود/file.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='مكتب_عصام_الفت',
)
