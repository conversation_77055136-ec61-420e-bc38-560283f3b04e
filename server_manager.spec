# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['server_manager.py'],
    pathex=[],
    binaries=[],
    datas=[('app', 'app'), ('app/templates', 'app/templates'), ('app/static', 'app/static'), ('config.py', '.'), ('schema.sql', '.'), ('fix_paths.py', '.'), ('monkey_patch.py', '.'), ('file.ico', '.'), ('file.jpeg', '.'), ('requirements.txt', '.')],
    hiddenimports=['flask', 'flask.app', 'flask.blueprints', 'flask.cli', 'flask.config', 'flask.ctx', 'flask.globals', 'flask.helpers', 'flask.json', 'flask.logging', 'flask.sessions', 'flask.signals', 'flask.templating', 'flask.testing', 'flask.views', 'flask.wrappers', 'flask_login', 'flask_login.config', 'flask_login.login_manager', 'flask_login.mixins', 'flask_login.signals', 'flask_login.utils', 'flask_bcrypt', 'flask_wtf', 'flask_wtf.csrf', 'flask_wtf.form', 'flask_wtf.file', 'flask_mail', 'werkzeug', 'werkzeug.serving', 'werkzeug.urls', 'werkzeug.utils', 'werkzeug.wrappers', 'werkzeug.exceptions', 'werkzeug.routing', 'werkzeug.security', 'werkzeug.datastructures', 'werkzeug.http', 'werkzeug.local', 'werkzeug.middleware', 'jinja2', 'jinja2.environment', 'jinja2.loaders', 'jinja2.runtime', 'jinja2.utils', 'jinja2.filters', 'jinja2.tests', 'jinja2.ext', 'wtforms', 'wtforms.fields', 'wtforms.form', 'wtforms.validators', 'wtforms.widgets', 'wtforms.csrf', 'PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'bcrypt', 'qrcode', 'qrcode.image', 'qrcode.image.pil', 'PIL', 'PIL.Image', 'PIL.ImageDraw', 'PIL.ImageFont', 'email_validator', 'itsdangerous', 'markupsafe', 'python_dotenv', 'sqlite3', 'datetime', 'os', 'sys', 'subprocess', 'socket', 'webbrowser', 'traceback', 'logging', 'threading', 'importlib', 'importlib.util', 'urllib', 'urllib.parse', 'json', 'base64', 'hashlib', 'secrets', 'uuid', 'pathlib', 'shutil', 'tempfile', 'io', 'collections', 'functools', 'itertools', 're', 'string', 'time', 'calendar', 'decimal', 'math', 'random', 'platform', 'signal', 'argparse', 'configparser', 'csv', 'xml', 'xml.etree', 'xml.etree.ElementTree', 'zipfile', 'tarfile', 'gzip', 'bz2', 'lzma', 'codecs', 'locale', 'gettext', 'unicodedata', 'encodings', 'encodings.utf_8', 'encodings.cp1256', 'encodings.ascii', 'encodings.latin1'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy', 'IPython', 'jupyter', 'notebook', 'pytest', 'setuptools', 'distutils', 'pip', 'wheel', 'pkg_resources'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='مكتب_عصام_الفت',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=true,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='file.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=['vcruntime140.dll', 'msvcp140.dll', 'python38.dll', 'python39.dll', 'python310.dll', 'python311.dll', 'python312.dll', 'python313.dll'],
    name='مكتب_عصام_الفت',
)
