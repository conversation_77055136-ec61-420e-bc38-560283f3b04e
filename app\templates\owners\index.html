{% extends "base.html" %}

{% block title %}إدارة الملاك - نظام إدارة العقارات{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* تحسينات عامة */
    .card {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .card-header {
        border-top-left-radius: 0.5rem !important;
        border-top-right-radius: 0.5rem !important;
    }

    /* تنسيق الجدول */
    .table th {
        font-weight: 600;
        white-space: nowrap;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    /* تنسيق البطاقات */
    .bg-opacity-10 {
        --bs-bg-opacity: 0.1;
    }

    .rounded-circle {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 767.98px) {
        .card-title {
            font-size: 1.1rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .mobile-action-buttons .dropdown-menu {
            min-width: 200px;
        }

        .list-group-item {
            padding: 0.75rem;
        }
    }

    /* تنسيق للشاشات المتوسطة والكبيرة */
    @media (min-width: 768px) {
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table th, .table td {
            vertical-align: middle;
        }
    }

    /* تأثيرات متحركة */
    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.1);
    }

    /* تنسيق الأيقونات */
    .fa-eye, .fa-info {
        color: #17a2b8;
    }

    .fa-edit, .fa-pencil-alt {
        color: #007bff;
    }

    .fa-trash-alt {
        color: #dc3545;
    }

    /* تنسيق الروابط */
    a {
        text-decoration: none;
    }

    /* تنسيق الفورم */
    .form-control:focus, .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تنسيق الأزرار المجمعة */
    .btn-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة الملاك</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('owners.add') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة مالك جديد
        </a>
    </div>
</div>

<div class="card mb-4 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>بحث وفلترة
        </h5>
        <button class="btn btn-sm btn-outline-primary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
            <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
        </button>
    </div>
    <div class="card-body collapse show" id="searchCollapse">
        <form method="GET" action="{{ url_for('owners.index') }}" id="searchForm">
            <div class="row g-3">
                <!-- حقل البحث - يظهر دائماً بعرض كامل -->
                <div class="col-12">
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="ابحث في الاسم، رقم الهوية، رقم الهاتف..."
                               value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            بحث
                        </button>
                        <a href="{{ url_for('owners.index') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i>
                        </a>
                    </div>
                </div>

                <!-- الفلاتر -->
                <div class="col-12 col-md-6 col-lg-3">
                    <label for="has_debt" class="form-label">المديونية</label>
                    <select class="form-select form-select-sm" id="has_debt" name="has_debt">
                        <option value="">الكل</option>
                        <option value="yes" {% if has_debt == 'yes' %}selected{% endif %}>لديهم مديونية</option>
                        <option value="no" {% if has_debt == 'no' %}selected{% endif %}>بدون مديونية</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="has_bank_info" class="form-label">معلومات البنك</label>
                    <select class="form-select form-select-sm" id="has_bank_info" name="has_bank_info">
                        <option value="">الكل</option>
                        <option value="yes" {% if has_bank_info == 'yes' %}selected{% endif %}>لديهم معلومات بنكية</option>
                        <option value="no" {% if has_bank_info == 'no' %}selected{% endif %}>بدون معلومات بنكية</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select form-select-sm" id="sort_by" name="sort_by">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم</option>
                        <option value="id_number" {% if sort_by == 'id_number' %}selected{% endif %}>رقم الهوية</option>
                        <option value="debt_desc" {% if sort_by == 'debt_desc' %}selected{% endif %}>المديونية (تنازلي)</option>
                        <option value="debt_asc" {% if sort_by == 'debt_asc' %}selected{% endif %}>المديونية (تصاعدي)</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="display_mode" class="form-label">طريقة العرض</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="display_mode" id="table_mode" value="table" {% if display_mode == 'table' or not display_mode %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="table_mode">
                            <i class="fas fa-table me-1"></i>جدول
                        </label>

                        <input type="radio" class="btn-check" name="display_mode" id="cards_mode" value="cards" {% if display_mode == 'cards' %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="cards_mode">
                            <i class="fas fa-th-large me-1"></i>بطاقات
                        </label>
                    </div>
                </div>
            </div>

            <!-- أزرار إضافية للشاشات المتوسطة والكبيرة -->
            <div class="d-none d-md-flex justify-content-end mt-3">
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>تطبيق الفلاتر
                    </button>
                    <a href="{{ url_for('owners.index') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين
                    </a>
                    <a href="{{ url_for('owners.add') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة مالك جديد
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>قائمة الملاك
        </h5>
        <span class="badge bg-primary">{{ owners|length }} مالك</span>
    </div>
    <div class="card-body">
        {% if owners %}
            {% if display_mode == 'table' %}
            <!-- عرض الجدول -->
            <!-- جدول للشاشات المتوسطة والكبيرة -->
            <div class="table-responsive d-none d-md-block">
                <table class="table table-striped table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">#</th>
                            <th>الاسم</th>
                            <th>رقم الهوية</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ الميلاد</th>
                            <th>العنوان</th>
                            <th>البنك</th>
                            <th>رقم الآيبان</th>
                            <th>المديونية</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for owner in owners %}
                        <tr>
                            <td class="text-center">{{ owner.id }}</td>
                            <td><strong>{{ owner.name }}</strong></td>
                            <td>
                                {% if owner.id_number %}
                                <span class="text-nowrap">{{ owner.id_number }}</span>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if owner.phone %}
                                <a href="tel:{{ owner.phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone-alt text-success me-1"></i>{{ owner.phone }}
                                </a>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if owner.email %}
                                <a href="mailto:{{ owner.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope text-primary me-1"></i>{{ owner.email }}
                                </a>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if owner.birth_date %}
                                <i class="fas fa-calendar-alt text-info me-1"></i>{{ owner.birth_date|format_date }}
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if owner.address %}
                                <span title="{{ owner.address }}">{{ owner.address|truncate(30) }}</span>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if owner.bank_name %}
                                <i class="fas fa-university text-primary me-1"></i>{{ owner.bank_name }}
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if owner.bank_iban %}
                                <span class="text-monospace small">{{ owner.bank_iban }}</span>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if owner.total_debt %}
                                <span class="badge bg-danger">{{ owner.total_debt|format_currency }}</span>
                                {% else %}
                                <span class="badge bg-success">لا توجد</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('owners.view', owner_id=owner.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('owners.edit', owner_id=owner.id) }}" class="btn btn-sm btn-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger confirm-delete"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteModal{{ owner.id }}"
                                            title="حذف">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>

                                <!-- Modal for delete confirmation -->
                                <div class="modal fade" id="deleteModal{{ owner.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ owner.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ owner.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف المالك <strong>{{ owner.name }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('owners.delete', owner_id=owner.id) }}" method="POST" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- قائمة للشاشات الصغيرة (الهواتف) -->
            <div class="d-md-none">
                {% for owner in owners %}
                <div class="card mb-3 border-{% if owner.total_debt %}danger{% else %}success{% endif %}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ owner.name }}</h5>
                        <div>
                            {% if owner.total_debt %}
                            <span class="badge bg-danger">{{ owner.total_debt|format_currency }}</span>
                            {% else %}
                            <span class="badge bg-success">لا توجد مديونية</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-id-card text-primary me-2"></i>رقم الهوية:</span>
                                <span>{{ owner.id_number or 'غير متوفر' }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-phone text-success me-2"></i>رقم الهاتف:</span>
                                {% if owner.phone %}
                                <a href="tel:{{ owner.phone }}" class="text-decoration-none">{{ owner.phone }}</a>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-envelope text-primary me-2"></i>البريد:</span>
                                {% if owner.email %}
                                <a href="mailto:{{ owner.email }}" class="text-decoration-none text-truncate" style="max-width: 150px;">{{ owner.email }}</a>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </li>
                            {% if owner.bank_name or owner.bank_iban %}
                            <li class="list-group-item">
                                <div><i class="fas fa-university text-primary me-2"></i>معلومات البنك:</div>
                                <div class="ms-4 mt-1">
                                    {% if owner.bank_name %}
                                    <div><strong>البنك:</strong> {{ owner.bank_name }}</div>
                                    {% endif %}
                                    {% if owner.bank_iban %}
                                    <div><strong>الآيبان:</strong> <span class="text-monospace small">{{ owner.bank_iban }}</span></div>
                                    {% endif %}
                                </div>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="card-footer">
                        <div class="mobile-action-buttons">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <a href="{{ url_for('owners.view', owner_id=owner.id) }}" class="btn btn-info">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </a>
                                </div>
                                <div>
                                    <div class="btn-group">
                                        <a href="{{ url_for('owners.edit', owner_id=owner.id) }}" class="btn btn-primary">
                                            <i class="fas fa-edit me-1"></i>تعديل
                                        </a>
                                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalMobile{{ owner.id }}">
                                            <i class="fas fa-trash-alt me-1"></i>حذف
                                        </button>
                                        <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <a class="dropdown-item" href="tel:{{ owner.phone }}" {% if not owner.phone %}disabled{% endif %}>
                                                    <i class="fas fa-phone-alt text-success me-2"></i>اتصال
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="mailto:{{ owner.email }}" {% if not owner.email %}disabled{% endif %}>
                                                    <i class="fas fa-envelope text-primary me-2"></i>إرسال بريد
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item" href="{{ url_for('owners.view', owner_id=owner.id) }}#buildings">
                                                    <i class="fas fa-building text-info me-2"></i>عرض العقارات
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="{{ url_for('owners.view', owner_id=owner.id) }}#transactions">
                                                    <i class="fas fa-money-bill-wave text-warning me-2"></i>المعاملات المالية
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Modal for mobile delete confirmation -->
                        <div class="modal fade" id="deleteModalMobile{{ owner.id }}" tabindex="-1" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">تأكيد الحذف</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        هل أنت متأكد من رغبتك في حذف المالك <strong>{{ owner.name }}</strong>؟
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                        <form action="{{ url_for('owners.delete', owner_id=owner.id) }}" method="POST">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <button type="submit" class="btn btn-danger">حذف</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- عرض البطاقات -->
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                {% for owner in owners %}
                <div class="col">
                    <div class="card h-100 shadow-sm {% if owner.total_debt %}border-danger{% else %}border-success{% endif %}">
                        <div class="card-header d-flex justify-content-between align-items-center {% if owner.total_debt %}bg-danger bg-opacity-10{% else %}bg-success bg-opacity-10{% endif %}">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-tie me-2"></i>{{ owner.name }}
                            </h5>
                            <div>
                                <span class="badge {% if owner.total_debt %}bg-danger{% else %}bg-success{% endif %}">
                                    {% if owner.total_debt %}
                                    {{ owner.total_debt|format_currency }}
                                    {% else %}
                                    لا توجد مديونية
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="p-3">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-2">
                                        <i class="fas fa-id-card text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">رقم الهوية</small>
                                        <strong>{{ owner.id_number or 'غير متوفر' }}</strong>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-2">
                                        <i class="fas fa-phone text-success"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">رقم الهاتف</small>
                                        {% if owner.phone %}
                                        <a href="tel:{{ owner.phone }}" class="text-decoration-none">{{ owner.phone }}</a>
                                        {% else %}
                                        <span class="text-muted">غير متوفر</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-light rounded-circle p-2 me-2">
                                        <i class="fas fa-envelope text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">البريد الإلكتروني</small>
                                        {% if owner.email %}
                                        <a href="mailto:{{ owner.email }}" class="text-decoration-none text-truncate d-inline-block" style="max-width: 200px;">{{ owner.email }}</a>
                                        {% else %}
                                        <span class="text-muted">غير متوفر</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <hr class="my-0">

                            <div class="accordion" id="ownerAccordion{{ owner.id }}">
                                <div class="accordion-item border-0">
                                    <h2 class="accordion-header" id="headingDetails{{ owner.id }}">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDetails{{ owner.id }}" aria-expanded="false" aria-controls="collapseDetails{{ owner.id }}">
                                            <i class="fas fa-info-circle me-2"></i>تفاصيل إضافية
                                        </button>
                                    </h2>
                                    <div id="collapseDetails{{ owner.id }}" class="accordion-collapse collapse" aria-labelledby="headingDetails{{ owner.id }}" data-bs-parent="#ownerAccordion{{ owner.id }}">
                                        <div class="accordion-body p-0">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item">
                                                    <small class="text-muted d-block">تاريخ الميلاد</small>
                                                    <span>{{ owner.birth_date|format_date if owner.birth_date else 'غير متوفر' }}</span>
                                                </li>
                                                <li class="list-group-item">
                                                    <small class="text-muted d-block">العنوان</small>
                                                    <span>{{ owner.address or 'غير متوفر' }}</span>
                                                </li>
                                                <li class="list-group-item">
                                                    <small class="text-muted d-block">اسم البنك</small>
                                                    <span>{{ owner.bank_name or 'غير متوفر' }}</span>
                                                </li>
                                                <li class="list-group-item">
                                                    <small class="text-muted d-block">رقم الآيبان</small>
                                                    <span class="text-monospace small">{{ owner.bank_iban or 'غير متوفر' }}</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('owners.view', owner_id=owner.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="{{ url_for('owners.edit', owner_id=owner.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalCard{{ owner.id }}">
                                    <i class="fas fa-trash-alt me-1"></i>حذف
                                </button>
                            </div>

                            <!-- Modal for card delete confirmation -->
                            <div class="modal fade" id="deleteModalCard{{ owner.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من رغبتك في حذف المالك <strong>{{ owner.name }}</strong>؟
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('owners.delete', owner_id=owner.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        {% else %}
        <div class="alert alert-info">
            لا يوجد ملاك حتى الآن. <a href="{{ url_for('owners.add') }}">إضافة مالك جديد</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث النموذج عند تغيير أي حقل (باستثناء حقل البحث)
        const autoSubmitElements = document.querySelectorAll('#searchForm select, #searchForm input[type="radio"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', function() {
                // إظهار مؤشر التحميل
                const submitBtn = document.querySelector('#searchForm button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التحميل...';
                    submitBtn.disabled = true;
                }

                // تأخير التقديم قليلاً للسماح بتحديث القيم
                setTimeout(() => {
                    document.getElementById('searchForm').submit();
                }, 100);
            });
        });

        // إضافة تأثير تمييز للصفوف عند التمرير فوقها
        const tableRows = document.querySelectorAll('table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('table-active');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('table-active');
            });
        });

        // تفعيل البحث الفوري عند الكتابة في حقل البحث
        const searchInput = document.getElementById('search');
        let searchTimeout;
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        document.getElementById('searchForm').submit();
                    }
                }, 500);
            });
        }

        // تفعيل الأزرار المتحركة للهواتف
        const mobileActionButtons = document.querySelectorAll('.mobile-action-buttons');
        mobileActionButtons.forEach(container => {
            const showBtn = container.querySelector('.show-actions');
            const actions = container.querySelector('.actions');

            if (showBtn && actions) {
                showBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    actions.classList.toggle('d-none');
                    this.querySelector('i').classList.toggle('fa-ellipsis-v');
                    this.querySelector('i').classList.toggle('fa-times');
                });
            }
        });

        // تفعيل التلميحات (tooltips)
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}