{% extends "base.html" %}

{% block title %}إدارة العقود - نظام إدارة العقارات{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* تحسينات عامة */
    .card {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .card-header {
        border-top-left-radius: 0.5rem !important;
        border-top-right-radius: 0.5rem !important;
    }

    /* تنسيق الجدول */
    .table th {
        font-weight: 600;
        white-space: nowrap;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    /* تنسيق البطاقات */
    .bg-opacity-10 {
        --bs-bg-opacity: 0.1;
    }

    .rounded-circle {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 767.98px) {
        .card-title {
            font-size: 1.1rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .mobile-action-buttons .dropdown-menu {
            min-width: 200px;
        }

        .list-group-item {
            padding: 0.75rem;
        }
    }

    /* تنسيق للشاشات المتوسطة والكبيرة */
    @media (min-width: 768px) {
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table th, .table td {
            vertical-align: middle;
        }
    }

    /* تأثيرات متحركة */
    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.1);
    }

    /* تنسيق الأيقونات */
    .fa-eye, .fa-info {
        color: #17a2b8;
    }

    .fa-edit, .fa-pencil-alt {
        color: #007bff;
    }

    .fa-trash-alt {
        color: #dc3545;
    }

    /* تنسيق الروابط */
    a {
        text-decoration: none;
    }

    /* تنسيق الفورم */
    .form-control:focus, .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تنسيق الأزرار المجمعة */
    .btn-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
    }

    /* تنسيق خاص بالعقود */
    .contract-card {
        transition: all 0.3s ease;
    }

    .contract-card:hover {
        transform: translateY(-5px);
    }

    .contract-icon {
        font-size: 2rem;
    }

    .contract-active {
        color: #28a745;
    }

    .contract-expired {
        color: #dc3545;
    }

    .contract-terminated {
        color: #ffc107;
    }

    .contract-renewed {
        color: #17a2b8;
    }

    .expiring-soon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
        100% {
            opacity: 1;
        }
    }

    .contract-details {
        border-right: 3px solid #dee2e6;
        padding-right: 1rem;
    }

    .contract-details.active {
        border-right-color: #28a745;
    }

    .contract-details.expired {
        border-right-color: #dc3545;
    }

    .contract-details.terminated {
        border-right-color: #ffc107;
    }

    .contract-details.renewed {
        border-right-color: #17a2b8;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة العقود</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('tenants.add_contract') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة عقد جديد
        </a>
    </div>
</div>

<div class="card mb-4 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>بحث وفلترة
        </h5>
        <button class="btn btn-sm btn-outline-primary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
            <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
        </button>
    </div>
    <div class="card-body collapse show" id="searchCollapse">
        <form method="GET" action="{{ url_for('tenants.contracts') }}" id="searchForm">
            <div class="row g-3">
                <!-- حقل البحث - يظهر دائماً بعرض كامل -->
                <div class="col-12">
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="ابحث في رقم العقد، اسم المستأجر..."
                               value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            بحث
                        </button>
                        <a href="{{ url_for('tenants.contracts') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i>
                        </a>
                    </div>
                </div>

                <!-- الفلاتر الرئيسية -->
                <div class="col-12 col-md-6 col-lg-3">
                    <label for="status" class="form-label">حالة العقد</label>
                    <select class="form-select form-select-sm" id="status" name="status">
                        <option value="">الكل</option>
                        <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                        <option value="expired" {% if status == 'expired' %}selected{% endif %}>منتهي</option>
                        <option value="terminated" {% if status == 'terminated' %}selected{% endif %}>ملغي</option>
                        <option value="renewed" {% if status == 'renewed' %}selected{% endif %}>مجدد</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="date_range" class="form-label">نطاق التاريخ</label>
                    <select class="form-select form-select-sm" id="date_range" name="date_range">
                        <option value="">الكل</option>
                        <option value="current" {% if date_range == 'current' %}selected{% endif %}>العقود الحالية</option>
                        <option value="expired" {% if date_range == 'expired' %}selected{% endif %}>العقود المنتهية</option>
                        <option value="expiring_soon" {% if date_range == 'expiring_soon' %}selected{% endif %}>تنتهي قريباً (خلال 30 يوم)</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="contract_fee_status" class="form-label">حالة رسوم العقد</label>
                    <select class="form-select form-select-sm" id="contract_fee_status" name="contract_fee_status">
                        <option value="">الكل</option>
                        <option value="paid" {% if contract_fee_status == 'paid' %}selected{% endif %}>مدفوعة بالكامل</option>
                        <option value="partially_paid" {% if contract_fee_status == 'partially_paid' %}selected{% endif %}>مدفوعة جزئياً</option>
                        <option value="unpaid" {% if contract_fee_status == 'unpaid' %}selected{% endif %}>غير مدفوعة</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select form-select-sm" id="sort_by" name="sort_by">
                        <option value="end_date" {% if sort_by == 'end_date' %}selected{% endif %}>تاريخ الانتهاء</option>
                        <option value="start_date" {% if sort_by == 'start_date' %}selected{% endif %}>تاريخ البداية</option>
                        <option value="tenant" {% if sort_by == 'tenant' %}selected{% endif %}>اسم المستأجر</option>
                        <option value="building" {% if sort_by == 'building' %}selected{% endif %}>اسم المبنى</option>
                        <option value="rent_amount" {% if sort_by == 'rent_amount' %}selected{% endif %}>قيمة الإيجار</option>
                    </select>
                </div>
            </div>

            <!-- فلاتر إضافية -->
            <div class="row g-3 mt-2">
                <div class="col-12">
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters" aria-expanded="false">
                        <i class="fas fa-sliders-h me-1"></i>خيارات متقدمة
                    </button>
                </div>

                <div class="col-12 collapse" id="advancedFilters">
                    <div class="card card-body bg-light">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <label for="building_id" class="form-label">المبنى</label>
                                <select class="form-select form-select-sm" id="building_id" name="building_id">
                                    <option value="">الكل</option>
                                    {% for building in buildings_list %}
                                    <option value="{{ building.id }}" {% if building_id == building.id|string %}selected{% endif %}>{{ building.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-12 col-md-6">
                                <label for="tenant_id" class="form-label">المستأجر</label>
                                <select class="form-select form-select-sm" id="tenant_id" name="tenant_id">
                                    <option value="">الكل</option>
                                    {% for tenant in tenants_list %}
                                    <option value="{{ tenant.id }}" {% if tenant_id == tenant.id|string %}selected{% endif %}>{{ tenant.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-12 col-md-6 col-lg-4">
                                <label for="display_mode" class="form-label">طريقة العرض</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="display_mode" id="table_mode" value="table" {% if display_mode == 'table' or not display_mode %}checked{% endif %}>
                                    <label class="btn btn-outline-primary" for="table_mode">
                                        <i class="fas fa-table me-1"></i>جدول
                                    </label>

                                    <input type="radio" class="btn-check" name="display_mode" id="cards_mode" value="cards" {% if display_mode == 'cards' %}checked{% endif %}>
                                    <label class="btn btn-outline-primary" for="cards_mode">
                                        <i class="fas fa-th-large me-1"></i>بطاقات
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار إضافية للشاشات المتوسطة والكبيرة -->
            <div class="d-none d-md-flex justify-content-end mt-3">
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>تطبيق الفلاتر
                    </button>
                    <a href="{{ url_for('tenants.contracts') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين
                    </a>
                    <a href="{{ url_for('tenants.add_contract') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة عقد جديد
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-file-contract me-2"></i>قائمة العقود
        </h5>
        <span class="badge bg-primary">{{ contracts|length }} عقد</span>
    </div>
    <div class="card-body">
        {% if contracts %}
            {% if display_mode == 'table' or not display_mode %}
            <!-- عرض الجدول -->
            <!-- جدول للشاشات المتوسطة والكبيرة -->
            <div class="table-responsive d-none d-md-block">
                <table class="table table-striped table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">#</th>
                            <th>رقم العقد</th>
                            <th>المستأجر</th>
                            <th>الوحدة / المبنى</th>
                            <th>المدة</th>
                            <th>قيمة الإيجار</th>
                            <th>الحالة</th>
                            <th>رسوم العقد</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for contract in contracts %}
                        <tr class="{% if contract.status == 'active' and contract.is_expiring_soon %}table-warning{% endif %}">
                            <td class="text-center">{{ contract.id }}</td>
                            <td><strong>{{ contract.contract_number or contract.id }}</strong></td>
                            <td>
                                <a href="{{ url_for('tenants.view', tenant_id=contract.tenant_id) }}" class="text-decoration-none">
                                    <i class="fas fa-user text-primary me-1"></i>{{ contract.tenant_name }}
                                </a>
                            </td>
                            <td>
                                <div>
                                    {% if contract.unit_id %}
                                    <a href="{{ url_for('properties.view_unit', unit_id=contract.unit_id) }}" class="text-decoration-none">
                                        <i class="fas fa-home text-success me-1"></i>{{ contract.unit_number }}
                                    </a>
                                    {% else %}
                                    <i class="fas fa-home text-success me-1"></i>{{ contract.unit_number }}
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {% if contract.building_id %}
                                    <a href="{{ url_for('properties.view_building', building_id=contract.building_id) }}" class="text-decoration-none">
                                        <i class="fas fa-building me-1"></i>{{ contract.building_name }}
                                    </a>
                                    {% else %}
                                    <i class="fas fa-building me-1"></i>{{ contract.building_name }}
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-calendar-alt text-primary me-1"></i>{{ contract.start_date|format_date }}
                                </div>
                                <div>
                                    <i class="fas fa-calendar-check text-{% if contract.status == 'active' and contract.is_expiring_soon %}danger expiring-soon{% else %}success{% endif %} me-1"></i>{{ contract.end_date|format_date }}
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold">{{ contract.rent_amount|format_currency }}</span>
                            </td>
                            <td>
                                {% if contract.status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                                {% if contract.is_expiring_soon %}
                                <span class="badge bg-warning text-dark mt-1">ينتهي قريباً</span>
                                {% endif %}
                                {% elif contract.status == 'expired' %}
                                <span class="badge bg-danger">منتهي</span>
                                {% elif contract.status == 'terminated' %}
                                <span class="badge bg-warning text-dark">ملغي</span>
                                {% elif contract.status == 'renewed' %}
                                <span class="badge bg-info">مجدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if contract.contract_fee_status == 'paid' %}
                                <span class="badge bg-success">مدفوعة بالكامل</span>
                                {% elif contract.contract_fee_status == 'partially_paid' %}
                                <span class="badge bg-warning text-dark">مدفوعة جزئياً</span>
                                {% else %}
                                <span class="badge bg-danger">غير مدفوعة</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل" data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('tenants.edit_contract', contract_id=contract.id) }}" class="btn btn-sm btn-primary" title="تعديل" data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteModal{{ contract.id }}"
                                            title="حذف" data-bs-toggle="tooltip">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>

                                <!-- Modal for delete confirmation -->
                                <div class="modal fade" id="deleteModal{{ contract.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ contract.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ contract.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف العقد رقم <strong>{{ contract.contract_number or contract.id }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('tenants.delete_contract', contract_id=contract.id) }}" method="POST" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- قائمة للشاشات الصغيرة (الهواتف) -->
            <div class="d-md-none">
                {% for contract in contracts %}
                <div class="card mb-3 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center
                                {% if contract.status == 'active' %}bg-success bg-opacity-10
                                {% elif contract.status == 'expired' %}bg-danger bg-opacity-10
                                {% elif contract.status == 'terminated' %}bg-warning bg-opacity-10
                                {% elif contract.status == 'renewed' %}bg-info bg-opacity-10{% endif %}">
                        <h5 class="card-title mb-0">
                            <strong>{{ contract.contract_number or contract.id }}</strong>
                        </h5>
                        <div>
                            {% if contract.status == 'active' %}
                            <span class="badge bg-success">نشط</span>
                            {% if contract.is_expiring_soon %}
                            <span class="badge bg-warning text-dark">ينتهي قريباً</span>
                            {% endif %}
                            {% elif contract.status == 'expired' %}
                            <span class="badge bg-danger">منتهي</span>
                            {% elif contract.status == 'terminated' %}
                            <span class="badge bg-warning text-dark">ملغي</span>
                            {% elif contract.status == 'renewed' %}
                            <span class="badge bg-info">مجدد</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-user text-primary me-2"></i>المستأجر:</span>
                                <a href="{{ url_for('tenants.view', tenant_id=contract.tenant_id) }}" class="text-decoration-none">{{ contract.tenant_name }}</a>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-home text-success me-2"></i>الوحدة:</span>
                                {% if contract.unit_id %}
                                <a href="{{ url_for('properties.view_unit', unit_id=contract.unit_id) }}" class="text-decoration-none">{{ contract.unit_number }}</a>
                                {% else %}
                                {{ contract.unit_number }}
                                {% endif %}
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-building text-secondary me-2"></i>المبنى:</span>
                                {% if contract.building_id %}
                                <a href="{{ url_for('properties.view_building', building_id=contract.building_id) }}" class="text-decoration-none">{{ contract.building_name }}</a>
                                {% else %}
                                {{ contract.building_name }}
                                {% endif %}
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-calendar-alt text-primary me-2"></i>المدة:</span>
                                <span>{{ contract.start_date|format_date }} - {{ contract.end_date|format_date }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-money-bill-wave text-success me-2"></i>قيمة الإيجار:</span>
                                <strong>{{ contract.rent_amount|format_currency }}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-file-invoice-dollar text-warning me-2"></i>رسوم العقد:</span>
                                {% if contract.contract_fee_status == 'paid' %}
                                <span class="badge bg-success">مدفوعة بالكامل</span>
                                {% elif contract.contract_fee_status == 'partially_paid' %}
                                <span class="badge bg-warning text-dark">مدفوعة جزئياً</span>
                                {% else %}
                                <span class="badge bg-danger">غير مدفوعة</span>
                                {% endif %}
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <a href="{{ url_for('tenants.edit_contract', contract_id=contract.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalMobile{{ contract.id }}">
                            <i class="fas fa-trash-alt me-1"></i>حذف
                        </button>
                    </div>

                    <!-- Modal for mobile delete confirmation -->
                    <div class="modal fade" id="deleteModalMobile{{ contract.id }}" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">تأكيد الحذف</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    هل أنت متأكد من رغبتك في حذف العقد رقم <strong>{{ contract.contract_number or contract.id }}</strong>؟
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                    <form action="{{ url_for('tenants.delete_contract', contract_id=contract.id) }}" method="POST">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger">حذف</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- عرض البطاقات -->
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                {% for contract in contracts %}
                <div class="col">
                    <div class="card h-100 shadow-sm contract-card">
                        <div class="card-header
                                {% if contract.status == 'active' %}bg-success bg-opacity-10
                                {% elif contract.status == 'expired' %}bg-danger bg-opacity-10
                                {% elif contract.status == 'terminated' %}bg-warning bg-opacity-10
                                {% elif contract.status == 'renewed' %}bg-info bg-opacity-10{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <strong>{{ contract.contract_number or contract.id }}</strong>
                                </h5>
                                <div>
                                    {% if contract.status == 'active' %}
                                    <span class="badge bg-success">نشط</span>
                                    {% if contract.is_expiring_soon %}
                                    <span class="badge bg-warning text-dark">ينتهي قريباً</span>
                                    {% endif %}
                                    {% elif contract.status == 'expired' %}
                                    <span class="badge bg-danger">منتهي</span>
                                    {% elif contract.status == 'terminated' %}
                                    <span class="badge bg-warning text-dark">ملغي</span>
                                    {% elif contract.status == 'renewed' %}
                                    <span class="badge bg-info">مجدد</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-file-contract contract-icon
                                    {% if contract.status == 'active' %}contract-active
                                    {% elif contract.status == 'expired' %}contract-expired
                                    {% elif contract.status == 'terminated' %}contract-terminated
                                    {% elif contract.status == 'renewed' %}contract-renewed{% endif %}"></i>
                            </div>
                            <div class="contract-details
                                    {% if contract.status == 'active' %}active
                                    {% elif contract.status == 'expired' %}expired
                                    {% elif contract.status == 'terminated' %}terminated
                                    {% elif contract.status == 'renewed' %}renewed{% endif %} mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <strong><i class="fas fa-user text-primary me-2"></i>المستأجر:</strong>
                                    <a href="{{ url_for('tenants.view', tenant_id=contract.tenant_id) }}" class="text-decoration-none">{{ contract.tenant_name }}</a>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <strong><i class="fas fa-home text-success me-2"></i>الوحدة:</strong>
                                    {% if contract.unit_id %}
                                    <a href="{{ url_for('properties.view_unit', unit_id=contract.unit_id) }}" class="text-decoration-none">{{ contract.unit_number }}</a>
                                    {% else %}
                                    {{ contract.unit_number }}
                                    {% endif %}
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <strong><i class="fas fa-calendar-alt text-primary me-2"></i>من:</strong>
                                    <span>{{ contract.start_date|format_date }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <strong><i class="fas fa-calendar-check text-danger me-2"></i>إلى:</strong>
                                    <span>{{ contract.end_date|format_date }}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <strong><i class="fas fa-money-bill-wave text-success me-2"></i>الإيجار:</strong>
                                    <span>{{ contract.rent_amount|format_currency }}</span>
                                </div>
                            </div>
                            <div class="text-center">
                                {% if contract.contract_fee_status == 'paid' %}
                                <span class="badge bg-success">رسوم العقد مدفوعة بالكامل</span>
                                {% elif contract.contract_fee_status == 'partially_paid' %}
                                <span class="badge bg-warning text-dark">رسوم العقد مدفوعة جزئياً</span>
                                {% else %}
                                <span class="badge bg-danger">رسوم العقد غير مدفوعة</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="{{ url_for('tenants.edit_contract', contract_id=contract.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalCard{{ contract.id }}">
                                    <i class="fas fa-trash-alt me-1"></i>حذف
                                </button>
                            </div>

                            <!-- Modal for card delete confirmation -->
                            <div class="modal fade" id="deleteModalCard{{ contract.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من رغبتك في حذف العقد رقم <strong>{{ contract.contract_number or contract.id }}</strong>؟
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('tenants.delete_contract', contract_id=contract.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        {% else %}
        <div class="alert alert-info">
            لا توجد عقود حتى الآن. <a href="{{ url_for('tenants.add_contract') }}" class="btn btn-sm btn-primary ms-2">إضافة عقد جديد</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث النموذج عند تغيير أي حقل (باستثناء حقل البحث)
        const autoSubmitElements = document.querySelectorAll('#searchForm select, #searchForm input[type="radio"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', function() {
                // إظهار مؤشر التحميل
                const submitBtn = document.querySelector('#searchForm button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التحميل...';
                    submitBtn.disabled = true;
                }

                // تأخير التقديم قليلاً للسماح بتحديث القيم
                setTimeout(() => {
                    document.getElementById('searchForm').submit();
                }, 100);
            });
        });

        // إضافة تأثير تمييز للصفوف عند التمرير فوقها
        const tableRows = document.querySelectorAll('table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('table-active');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('table-active');
            });
        });

        // تفعيل البحث الفوري عند الكتابة في حقل البحث
        const searchInput = document.getElementById('search');
        let searchTimeout;
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        document.getElementById('searchForm').submit();
                    }
                }, 500);
            });
        }

        // تفعيل التلميحات (tooltips)
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تأثيرات للبطاقات
        const contractCards = document.querySelectorAll('.contract-card');
        contractCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('shadow');
            });
            card.addEventListener('mouseleave', function() {
                this.classList.remove('shadow');
            });
        });

        // تفعيل مودال التأكيد للحذف
        const deleteButtons = document.querySelectorAll('.confirm-delete');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm-message'))) {
                    e.preventDefault();
                }
            });
        });

        // تفعيل الفلاتر المتقدمة
        const advancedFiltersBtn = document.querySelector('[data-bs-target="#advancedFilters"]');
        if (advancedFiltersBtn) {
            // فحص إذا كانت هناك قيم في الفلاتر المتقدمة، إذا كان كذلك، افتح القسم تلقائيًا
            const buildingId = document.getElementById('building_id') ? document.getElementById('building_id').value : '';
            const tenantId = document.getElementById('tenant_id') ? document.getElementById('tenant_id').value : '';
            const displayMode = document.querySelector('input[name="display_mode"]:checked') ? document.querySelector('input[name="display_mode"]:checked').value : '';

            if (buildingId || tenantId || (displayMode && displayMode !== 'table')) {
                const advancedFilters = document.getElementById('advancedFilters');
                if (advancedFilters) {
                    advancedFilters.classList.add('show');
                }
            }
        }

        // تمييز العقود التي تنتهي قريباً
        const today = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(today.getDate() + 30);

        // تفعيل تأثير النبض للعناصر التي تنتهي قريباً
        const expiringElements = document.querySelectorAll('.expiring-soon');
        expiringElements.forEach(element => {
            element.classList.add('expiring-soon');
        });
    });
</script>
{% endblock %}