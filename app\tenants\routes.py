from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.tenants import tenants_bp
from app.forms import TenantForm, ContractForm, EditContractForm
from app.db import query_db, insert_db, update_db, delete_db
from app.decorators import data_entry_required
from datetime import datetime
from flask import current_app

@tenants_bp.route('/')
@login_required
def index():
    """قائمة المستأجرين مع دعم البحث والفلترة"""
    # الحصول على معلمات البحث والفلترة
    search_query = request.args.get('search', '')
    nationality = request.args.get('nationality', '')
    occupation = request.args.get('occupation', '')
    has_active_contract = request.args.get('has_active_contract', '')
    sort_by = request.args.get('sort_by', 'name')
    display_mode = request.args.get('display_mode', 'table')  # إضافة معلمة طريقة العرض

    # بناء استعلام SQL الأساسي
    base_query = 'SELECT * FROM tenants WHERE 1=1'

    # إعداد قائمة المعلمات
    params = []

    # إضافة شروط البحث
    if search_query:
        base_query += " AND (name LIKE ? OR id_number LIKE ? OR phone LIKE ? OR email LIKE ?)"
        search_param = f'%{search_query}%'
        params.extend([search_param, search_param, search_param, search_param])

    # إضافة فلتر الجنسية
    if nationality:
        base_query += " AND nationality = ?"
        params.append(nationality)

    # إضافة فلتر المهنة
    if occupation:
        base_query += " AND occupation = ?"
        params.append(occupation)

    # إضافة الترتيب
    if sort_by == 'name':
        base_query += " ORDER BY name"
    elif sort_by == 'id_number':
        base_query += " ORDER BY id_number"
    elif sort_by == 'nationality':
        base_query += " ORDER BY nationality, name"
    elif sort_by == 'newest':
        base_query += " ORDER BY id DESC"
    else:
        base_query += " ORDER BY name"  # الترتيب الافتراضي

    # تنفيذ الاستعلام
    tenants = query_db(base_query, params)

    # فلترة المستأجرين حسب حالة العقد (يتم تنفيذها بعد استرجاع البيانات لأنها تتطلب استعلامات إضافية)
    if has_active_contract:
        filtered_tenants = []
        for tenant in tenants:
            # التحقق من وجود عقود نشطة للمستأجر
            active_contracts = query_db(
                'SELECT COUNT(*) as count FROM contracts WHERE tenant_id = ? AND status = "active"',
                (tenant['id'],),
                one=True
            )

            has_contract = active_contracts['count'] > 0

            if (has_active_contract == 'yes' and has_contract) or (has_active_contract == 'no' and not has_contract):
                filtered_tenants.append(tenant)

        tenants = filtered_tenants

    # الحصول على قائمة الجنسيات الفريدة للفلترة
    nationalities = query_db('SELECT DISTINCT nationality FROM tenants WHERE nationality IS NOT NULL AND nationality != "" ORDER BY nationality')
    nationalities = [nat['nationality'] for nat in nationalities]

    # الحصول على قائمة المهن الفريدة للفلترة
    occupations = query_db('SELECT DISTINCT occupation FROM tenants WHERE occupation IS NOT NULL AND occupation != "" ORDER BY occupation')
    occupations = [occ['occupation'] for occ in occupations]

    return render_template('tenants/index.html',
                          tenants=tenants,
                          nationalities=nationalities,
                          occupations=occupations,
                          search_query=search_query,
                          nationality=nationality,
                          occupation=occupation,
                          has_active_contract=has_active_contract,
                          sort_by=sort_by,
                          display_mode=display_mode,  # إضافة معلمة طريقة العرض
                          title='إدارة المستأجرين')

@tenants_bp.route('/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add():
    """إضافة مستأجر جديد"""
    form = TenantForm()

    if form.validate_on_submit():
        tenant_id = insert_db('tenants', {
            'name': form.name.data,
            'id_number': form.id_number.data,
            'phone': form.phone.data,
            'email': form.email.data,
            'nationality': form.nationality.data,
            'birth_date': form.birth_date.data,
            'occupation': form.occupation.data,
            'employer': form.employer.data,
            'emergency_contact': form.emergency_contact.data,
            'notes': form.notes.data
        })

        flash('تم إضافة المستأجر بنجاح!', 'success')
        return redirect(url_for('tenants.view', tenant_id=tenant_id))

    return render_template('tenants/form.html', form=form, title='إضافة مستأجر جديد')

@tenants_bp.route('/edit/<int:tenant_id>', methods=['GET', 'POST'])
@login_required
@data_entry_required
def edit(tenant_id):
    """تعديل مستأجر"""
    tenant = query_db('SELECT * FROM tenants WHERE id = ?', (tenant_id,), one=True)

    if not tenant:
        flash('المستأجر غير موجود.', 'danger')
        return redirect(url_for('tenants.index'))

    form = TenantForm()

    if request.method == 'GET':
        form.name.data = tenant['name']
        form.id_number.data = tenant['id_number']
        form.phone.data = tenant['phone']
        form.email.data = tenant['email']
        form.nationality.data = tenant['nationality']
        form.birth_date.data = tenant['birth_date']
        form.occupation.data = tenant['occupation']
        form.employer.data = tenant['employer']
        form.emergency_contact.data = tenant['emergency_contact']
        form.notes.data = tenant['notes']

    if form.validate_on_submit():
        update_db('tenants', tenant_id, {
            'name': form.name.data,
            'id_number': form.id_number.data,
            'phone': form.phone.data,
            'email': form.email.data,
            'nationality': form.nationality.data,
            'birth_date': form.birth_date.data,
            'occupation': form.occupation.data,
            'employer': form.employer.data,
            'emergency_contact': form.emergency_contact.data,
            'notes': form.notes.data
        })

        flash('تم تحديث بيانات المستأجر بنجاح!', 'success')
        return redirect(url_for('tenants.view', tenant_id=tenant_id))

    return render_template('tenants/form.html', form=form, title='تعديل بيانات المستأجر', tenant=tenant)

@tenants_bp.route('/view/<int:tenant_id>')
@login_required
def view(tenant_id):
    """عرض تفاصيل المستأجر"""
    tenant = query_db('SELECT * FROM tenants WHERE id = ?', (tenant_id,), one=True)

    if not tenant:
        flash('المستأجر غير موجود.', 'danger')
        return redirect(url_for('tenants.index'))

    # الحصول على العقود النشطة للمستأجر
    active_contracts = query_db('''
        SELECT c.*, u.unit_number, b.name as building_name, b.address as building_address
        FROM contracts c
        JOIN units u ON c.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        WHERE c.tenant_id = ? AND c.status = 'active'
        ORDER BY c.end_date
    ''', (tenant_id,))

    # الحصول على سجل العقود السابقة للمستأجر
    contracts_history = query_db('''
        SELECT c.*, u.unit_number, b.name as building_name
        FROM contracts c
        JOIN units u ON c.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        WHERE c.tenant_id = ? AND c.status != 'active'
        ORDER BY c.end_date DESC
    ''', (tenant_id,))

    # الحصول على المستندات المرتبطة بالمستأجر
    documents = query_db('''
        SELECT d.*, u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.related_to = 'tenant' AND d.related_id = ?
        ORDER BY d.upload_date DESC
    ''', (tenant_id,))

    # الحصول على المعاملات المالية المرتبطة بالمستأجر (من خلال العقود)
    transactions = query_db('''
        SELECT t.*, c.contract_number
        FROM transactions t
        JOIN contracts c ON t.related_id = c.id
        WHERE t.related_to = 'contract' AND c.tenant_id = ?
        ORDER BY t.transaction_date DESC
        LIMIT 10
    ''', (tenant_id,))

    return render_template('tenants/view.html',
                           tenant=tenant,
                           active_contracts=active_contracts,
                           contracts_history=contracts_history,
                           documents=documents,
                           transactions=transactions,
                           title=f'تفاصيل المستأجر: {tenant["name"]}')

@tenants_bp.route('/delete/<int:tenant_id>', methods=['POST'])
@login_required
@data_entry_required
def delete(tenant_id):
    """حذف مستأجر"""
    tenant = query_db('SELECT * FROM tenants WHERE id = ?', (tenant_id,), one=True)

    if not tenant:
        flash('المستأجر غير موجود.', 'danger')
        return redirect(url_for('tenants.index'))

    # التحقق من وجود عقود مرتبطة بالمستأجر
    contracts = query_db('SELECT COUNT(*) as count FROM contracts WHERE tenant_id = ?', (tenant_id,), one=True)

    if contracts['count'] > 0:
        flash('لا يمكن حذف المستأجر لأنه مرتبط بعقود. يرجى حذف العقود أولاً.', 'danger')
        return redirect(url_for('tenants.view', tenant_id=tenant_id))

    # حذف المستندات المرتبطة بالمستأجر
    query_db('DELETE FROM documents WHERE related_to = "tenant" AND related_id = ?', (tenant_id,))

    # حذف المستأجر
    delete_db('tenants', tenant_id)

    flash('تم حذف المستأجر بنجاح!', 'success')
    return redirect(url_for('tenants.index'))

@tenants_bp.route('/contracts')
@login_required
def contracts():
    """قائمة العقود مع دعم البحث والفلترة"""
    # الحصول على معلمات البحث والفلترة
    search_query = request.args.get('search', '')
    building_id = request.args.get('building_id', '')
    tenant_id = request.args.get('tenant_id', '')
    status = request.args.get('status', '')
    date_range = request.args.get('date_range', '')
    contract_fee_status = request.args.get('contract_fee_status', '')
    sort_by = request.args.get('sort_by', 'end_date')
    display_mode = request.args.get('display_mode', 'table')  # إضافة معلمة طريقة العرض

    # بناء استعلام SQL الأساسي
    base_query = '''
        SELECT c.*, t.name as tenant_name, t.id as tenant_id,
               u.unit_number, u.id as unit_id,
               b.name as building_name, b.id as building_id
        FROM contracts c
        JOIN tenants t ON c.tenant_id = t.id
        JOIN units u ON c.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        WHERE 1=1
    '''

    # إعداد قائمة المعلمات
    params = []

    # إضافة شروط البحث
    if search_query:
        base_query += " AND (c.contract_number LIKE ? OR t.name LIKE ? OR u.unit_number LIKE ? OR b.name LIKE ?)"
        search_param = f'%{search_query}%'
        params.extend([search_param, search_param, search_param, search_param])

    # إضافة فلتر المبنى
    if building_id:
        base_query += " AND b.id = ?"
        params.append(building_id)

    # إضافة فلتر المستأجر
    if tenant_id:
        base_query += " AND t.id = ?"
        params.append(tenant_id)

    # إضافة فلتر حالة العقد
    if status:
        base_query += " AND c.status = ?"
        params.append(status)

    # إضافة فلتر حالة رسوم العقد
    if contract_fee_status:
        base_query += " AND c.contract_fee_status = ?"
        params.append(contract_fee_status)

    # إضافة فلتر نطاق التاريخ
    today = datetime.now().date()
    # استخدام timedelta لإضافة 30 يوم بطريقة آمنة
    from datetime import timedelta
    thirty_days_from_now = (today + timedelta(days=30))

    if date_range == 'current':
        base_query += " AND c.start_date <= ? AND c.end_date >= ?"
        params.extend([today, today])
    elif date_range == 'expired':
        base_query += " AND c.end_date < ?"
        params.append(today)
    elif date_range == 'expiring_soon':
        base_query += " AND c.end_date BETWEEN ? AND ?"
        params.extend([today, thirty_days_from_now])

    # إضافة الترتيب
    if sort_by == 'end_date':
        base_query += " ORDER BY c.end_date"
    elif sort_by == 'start_date':
        base_query += " ORDER BY c.start_date"
    elif sort_by == 'tenant':
        base_query += " ORDER BY t.name"
    elif sort_by == 'building':
        base_query += " ORDER BY b.name, u.unit_number"
    elif sort_by == 'rent_amount':
        base_query += " ORDER BY c.rent_amount DESC"
    else:
        base_query += " ORDER BY c.status, c.end_date"  # الترتيب الافتراضي

    # تنفيذ الاستعلام
    contracts_raw = query_db(base_query, params)

    # تحويل كائنات sqlite3.Row إلى قواميس عادية يمكن تعديلها
    contracts_list = []
    today = datetime.now().date()
    thirty_days_from_now = (today + timedelta(days=30))

    for contract_row in contracts_raw:
        # تحويل كائن Row إلى قاموس
        contract = dict(contract_row)

        # إضافة خاصية is_expiring_soon
        contract['is_expiring_soon'] = (
            contract['status'] == 'active' and
            contract['end_date'] >= today and
            contract['end_date'] <= thirty_days_from_now
        )

        contracts_list.append(contract)

    # الحصول على قائمة المباني للفلترة
    buildings_list = query_db('SELECT id, name FROM buildings ORDER BY name')

    # الحصول على قائمة المستأجرين للفلترة
    tenants_list = query_db('SELECT id, name FROM tenants ORDER BY name')

    return render_template('tenants/contracts.html',
                          contracts=contracts_list,
                          buildings_list=buildings_list,
                          tenants_list=tenants_list,
                          search_query=search_query,
                          building_id=building_id,
                          tenant_id=tenant_id,
                          status=status,
                          date_range=date_range,
                          contract_fee_status=contract_fee_status,
                          sort_by=sort_by,
                          display_mode=display_mode,  # إضافة معلمة طريقة العرض
                          title='إدارة العقود')

@tenants_bp.route('/contracts/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add_contract():
    """إضافة عقد جديد"""
    form = ContractForm()

    # تحميل قائمة الوحدات للاختيار (الوحدات الشاغرة فقط)
    units = query_db('''
        SELECT u.id, u.unit_number, b.name as building_name, u.rent_amount
        FROM units u
        JOIN buildings b ON u.building_id = b.id
        WHERE u.status = 'vacant' OR u.status = 'reserved'
        ORDER BY b.name, u.unit_number
    ''')
    form.unit_id.choices = [(unit['id'], f"{unit['unit_number']} - {unit['building_name']} ({unit['rent_amount']} ر.س)") for unit in units]

    # تحميل قائمة المستأجرين للاختيار
    tenants = query_db('SELECT id, name FROM tenants ORDER BY name')
    form.tenant_id.choices = [(tenant['id'], tenant['name']) for tenant in tenants]

    if form.validate_on_submit():
        # التحقق من أن الوحدة شاغرة
        unit = query_db('SELECT * FROM units WHERE id = ?', (form.unit_id.data,), one=True)
        if unit['status'] not in ['vacant', 'reserved']:
            flash('الوحدة غير شاغرة. يرجى اختيار وحدة أخرى.', 'danger')
            return redirect(url_for('tenants.add_contract'))

        # إنشاء العقد
        contract_id = insert_db('contracts', {
            'unit_id': form.unit_id.data,
            'tenant_id': form.tenant_id.data,
            'start_date': form.start_date.data,
            'end_date': form.end_date.data,
            'rent_amount': form.rent_amount.data,
            'payment_frequency': form.payment_frequency.data,
            'deposit_amount': form.deposit_amount.data,
            'contract_fee': form.contract_fee.data,
            'contract_fee_paid': form.contract_fee_paid.data,
            'contract_fee_status': form.contract_fee_status.data,
            'contract_number': form.contract_number.data,
            'status': form.status.data,
            'terms': form.terms.data,
            'notes': form.notes.data
        })

        # تحديث حالة الوحدة إلى مشغولة إذا كان العقد نشطًا
        if form.status.data == 'active':
            update_db('units', form.unit_id.data, {'status': 'occupied'})

        # إضافة معاملة مالية لرسوم العقد إذا كان هناك رسوم مدفوعة
        if form.contract_fee_paid.data and form.contract_fee_paid.data > 0:
            # الحصول على معلومات الوحدة والمبنى والمالك
            unit_info = query_db('''
                SELECT u.unit_number, b.name as building_name, b.owner_id, o.name as owner_name
                FROM units u
                JOIN buildings b ON u.building_id = b.id
                JOIN owners o ON b.owner_id = o.id
                WHERE u.id = ?
            ''', (form.unit_id.data,), one=True)

            if unit_info:
                owner_id = unit_info['owner_id']
                owner_name = unit_info['owner_name']
                building_name = unit_info['building_name']
                unit_number = unit_info['unit_number']

                # الحصول على اسم المستأجر
                tenant = query_db('SELECT name FROM tenants WHERE id = ?', (form.tenant_id.data,), one=True)
                tenant_name = tenant['name'] if tenant else 'غير معروف'

                # إضافة معاملة مالية مرتبطة بالمالك
                transaction_id = insert_db('transactions', {
                    'transaction_date': datetime.now().date(),
                    'amount': form.contract_fee_paid.data,
                    'type': 'income',
                    'category': 'contract_fee',
                    'description': f'رسوم عقد الوحدة {unit_number} في {building_name} للمستأجر {tenant_name}',
                    'related_to': 'owner',  # تغيير من 'contract' إلى 'owner'
                    'related_id': owner_id,  # تغيير من contract_id إلى owner_id
                    'payment_method': 'cash',  # افتراضي
                    'created_by': current_user.id if hasattr(current_user, 'id') else None,
                    'created_at': datetime.now()
                })

        flash('تم إضافة العقد بنجاح!', 'success')
        return redirect(url_for('tenants.view_contract', contract_id=contract_id))

    # إذا تم تمرير معرف الوحدة في الـ URL
    unit_id = request.args.get('unit_id', type=int)
    if unit_id:
        form.unit_id.data = unit_id
        unit = query_db('SELECT rent_amount FROM units WHERE id = ?', (unit_id,), one=True)
        if unit:
            form.rent_amount.data = unit['rent_amount']

    # إذا تم تمرير معرف المستأجر في الـ URL
    tenant_id = request.args.get('tenant_id', type=int)
    if tenant_id:
        form.tenant_id.data = tenant_id

    # تعيين التواريخ الافتراضية
    today = datetime.now().date()
    form.start_date.data = today
    form.end_date.data = datetime(today.year + 1, today.month, today.day).date()

    return render_template('tenants/contract_form.html', form=form, title='إضافة عقد جديد')

@tenants_bp.route('/contracts/edit/<int:contract_id>', methods=['GET', 'POST'])
@login_required
@data_entry_required
def edit_contract(contract_id):
    """تعديل عقد"""
    contract = query_db('SELECT * FROM contracts WHERE id = ?', (contract_id,), one=True)

    if not contract:
        flash('العقد غير موجود.', 'danger')
        return redirect(url_for('tenants.contracts'))

    form = EditContractForm()

    # تحميل قائمة الوحدات للاختيار (الوحدة الحالية + الوحدات الشاغرة)
    units = query_db('''
        SELECT u.id, u.unit_number, b.name as building_name, u.rent_amount
        FROM units u
        JOIN buildings b ON u.building_id = b.id
        WHERE u.id = ? OR u.status = 'vacant' OR u.status = 'reserved'
        ORDER BY b.name, u.unit_number
    ''', (contract['unit_id'],))
    form.unit_id.choices = [(unit['id'], f"{unit['unit_number']} - {unit['building_name']} ({unit['rent_amount']} ر.س)") for unit in units]

    # تحميل قائمة المستأجرين للاختيار
    tenants = query_db('SELECT id, name FROM tenants ORDER BY name')
    form.tenant_id.choices = [(tenant['id'], tenant['name']) for tenant in tenants]

    if request.method == 'GET':
        form.id.data = contract['id']
        form.unit_id.data = contract['unit_id']
        form.tenant_id.data = contract['tenant_id']
        form.start_date.data = contract['start_date']
        form.end_date.data = contract['end_date']
        form.rent_amount.data = contract['rent_amount']
        form.payment_frequency.data = contract['payment_frequency']
        form.deposit_amount.data = contract['deposit_amount']
        form.contract_fee.data = contract['contract_fee']
        form.contract_fee_paid.data = contract['contract_fee_paid']
        form.contract_fee_status.data = contract['contract_fee_status']
        form.contract_number.data = contract['contract_number']
        form.status.data = contract['status']
        form.terms.data = contract['terms']
        form.notes.data = contract['notes']

    if form.validate_on_submit():
        # التحقق من أن الوحدة الجديدة شاغرة إذا تم تغييرها
        if form.unit_id.data != contract['unit_id']:
            unit = query_db('SELECT * FROM units WHERE id = ?', (form.unit_id.data,), one=True)
            if unit['status'] not in ['vacant', 'reserved']:
                flash('الوحدة الجديدة غير شاغرة. يرجى اختيار وحدة أخرى.', 'danger')
                return redirect(url_for('tenants.edit_contract', contract_id=contract_id))

        # تحديث العقد
        update_db('contracts', contract_id, {
            'unit_id': form.unit_id.data,
            'tenant_id': form.tenant_id.data,
            'start_date': form.start_date.data,
            'end_date': form.end_date.data,
            'rent_amount': form.rent_amount.data,
            'payment_frequency': form.payment_frequency.data,
            'deposit_amount': form.deposit_amount.data,
            'contract_fee': form.contract_fee.data,
            'contract_fee_paid': form.contract_fee_paid.data,
            'contract_fee_status': form.contract_fee_status.data,
            'contract_number': form.contract_number.data,
            'status': form.status.data,
            'terms': form.terms.data,
            'notes': form.notes.data
        })

        # تحديث حالة الوحدة القديمة إلى شاغرة إذا تم تغيير الوحدة أو تغيير حالة العقد
        if form.unit_id.data != contract['unit_id'] or form.status.data != 'active':
            # التحقق من عدم وجود عقود نشطة أخرى للوحدة القديمة
            active_contracts = query_db('''
                SELECT COUNT(*) as count
                FROM contracts
                WHERE unit_id = ? AND status = 'active' AND id != ?
            ''', (contract['unit_id'], contract_id), one=True)

            if active_contracts['count'] == 0:
                update_db('units', contract['unit_id'], {'status': 'vacant'})

        # تحديث حالة الوحدة الجديدة إلى مشغولة إذا كان العقد نشطًا
        if form.status.data == 'active':
            update_db('units', form.unit_id.data, {'status': 'occupied'})

        # إضافة معاملة مالية لرسوم العقد إذا كان هناك تغيير في المبلغ المدفوع
        if form.contract_fee_paid.data != contract['contract_fee_paid']:
            # الحصول على معلومات الوحدة والمبنى والمالك
            unit_info = query_db('''
                SELECT u.unit_number, b.name as building_name, b.owner_id, o.name as owner_name
                FROM units u
                JOIN buildings b ON u.building_id = b.id
                JOIN owners o ON b.owner_id = o.id
                WHERE u.id = ?
            ''', (form.unit_id.data,), one=True)

            if unit_info:
                owner_id = unit_info['owner_id']
                owner_name = unit_info['owner_name']
                building_name = unit_info['building_name']
                unit_number = unit_info['unit_number']

                # الحصول على اسم المستأجر
                tenant = query_db('SELECT name FROM tenants WHERE id = ?', (form.tenant_id.data,), one=True)
                tenant_name = tenant['name'] if tenant else 'غير معروف'

                # حساب الفرق في المبلغ المدفوع
                payment_difference = form.contract_fee_paid.data - (contract['contract_fee_paid'] or 0)

                if payment_difference > 0:
                    # إضافة معاملة مالية للمبلغ الإضافي المدفوع مرتبطة بالمالك
                    insert_db('transactions', {
                        'transaction_date': datetime.now().date(),
                        'amount': payment_difference,
                        'type': 'income',
                        'category': 'contract_fee',
                        'description': f'دفعة إضافية لرسوم عقد الوحدة {unit_number} في {building_name} للمستأجر {tenant_name}',
                        'related_to': 'owner',  # تغيير من 'contract' إلى 'owner'
                        'related_id': owner_id,  # تغيير من contract_id إلى owner_id
                        'payment_method': 'cash',  # افتراضي
                        'created_by': current_user.id if hasattr(current_user, 'id') else None,
                        'created_at': datetime.now()
                    })

        flash('تم تحديث العقد بنجاح!', 'success')
        return redirect(url_for('tenants.view_contract', contract_id=contract_id))

    return render_template('tenants/contract_form.html', form=form, title='تعديل العقد', contract=contract)

@tenants_bp.route('/contracts/view/<int:contract_id>')
@login_required
def view_contract(contract_id):
    """عرض تفاصيل العقد"""
    contract = query_db('''
        SELECT c.*, t.name as tenant_name, t.phone as tenant_phone, t.email as tenant_email,
               u.unit_number, b.name as building_name, b.address as building_address
        FROM contracts c
        JOIN tenants t ON c.tenant_id = t.id
        JOIN units u ON c.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        WHERE c.id = ?
    ''', (contract_id,), one=True)

    if not contract:
        flash('العقد غير موجود.', 'danger')
        return redirect(url_for('tenants.contracts'))

    # الحصول على المستندات المرتبطة بالعقد
    documents = query_db('''
        SELECT d.*, u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.related_to = 'contract' AND d.related_id = ?
        ORDER BY d.upload_date DESC
    ''', (contract_id,))

    # الحصول على المعاملات المالية المرتبطة بالعقد
    transactions = query_db('''
        SELECT t.*, u.name as created_by_name
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        WHERE t.related_to = 'contract' AND t.related_id = ?
        ORDER BY t.transaction_date DESC
    ''', (contract_id,))

    # حساب مدة العقد بالأشهر
    start_date = contract['start_date']
    end_date = contract['end_date']
    contract_duration = (end_date.year - start_date.year) * 12 + end_date.month - start_date.month

    return render_template('tenants/view_contract.html',
                           contract=contract,
                           documents=documents,
                           payments=transactions,  # تغيير الاسم من transactions إلى payments
                           contract_duration=contract_duration,
                           title=f'تفاصيل العقد: {contract["contract_number"] or contract["id"]}')

@tenants_bp.route('/contracts/delete/<int:contract_id>', methods=['POST'])
@login_required
@data_entry_required
def delete_contract(contract_id):
    """حذف عقد"""
    contract = query_db('SELECT * FROM contracts WHERE id = ?', (contract_id,), one=True)

    if not contract:
        flash('العقد غير موجود.', 'danger')
        return redirect(url_for('tenants.contracts'))

    # التحقق من وجود معاملات مالية مرتبطة بالعقد
    transactions = query_db('SELECT COUNT(*) as count FROM transactions WHERE related_to = "contract" AND related_id = ?', (contract_id,), one=True)

    if transactions['count'] > 0:
        flash('لا يمكن حذف العقد لأنه مرتبط بمعاملات مالية. يرجى حذف المعاملات أولاً.', 'danger')
        return redirect(url_for('tenants.view_contract', contract_id=contract_id))

    # حذف المستندات المرتبطة بالعقد
    query_db('DELETE FROM documents WHERE related_to = "contract" AND related_id = ?', (contract_id,))

    # تحديث حالة الوحدة إلى شاغرة إذا كان العقد نشطًا
    if contract['status'] == 'active':
        # التحقق من عدم وجود عقود نشطة أخرى للوحدة
        active_contracts = query_db('''
            SELECT COUNT(*) as count
            FROM contracts
            WHERE unit_id = ? AND status = 'active' AND id != ?
        ''', (contract['unit_id'], contract_id), one=True)

        if active_contracts['count'] == 0:
            update_db('units', contract['unit_id'], {'status': 'vacant'})

    # حذف العقد
    delete_db('contracts', contract_id)

    flash('تم حذف العقد بنجاح!', 'success')
    return redirect(url_for('tenants.contracts'))
