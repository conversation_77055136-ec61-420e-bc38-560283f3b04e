<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة العقارات{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    {% block styles %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fas fa-building me-2"></i>نظام إدارة العقارات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and request.endpoint.startswith('dashboard') %}active{% endif %}" href="{{ url_for('dashboard.index') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and request.endpoint.startswith('owners') %}active{% endif %}" href="{{ url_for('owners.index') }}">
                            <i class="fas fa-user-tie me-1"></i>الملاك
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and request.endpoint.startswith('properties') %}active{% endif %}" href="#" id="propertiesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-home me-1"></i>العقارات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="propertiesDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('properties.buildings') }}">المباني</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('properties.units') }}">الوحدات</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and request.endpoint.startswith('tenants') %}active{% endif %}" href="#" id="tenantsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-users me-1"></i>المستأجرين
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="tenantsDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('tenants.index') }}">المستأجرين</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('tenants.contracts') }}">العقود</a></li>
                        </ul>
                    </li>
                    {% if current_user.is_accountant() %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint and request.endpoint.startswith('finance') %}active{% endif %}" href="#" id="financeDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-money-bill-wave me-1"></i>المالية
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="financeDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('finance.index') }}">لوحة المالية</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('finance.transactions') }}">المعاملات</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('finance.summary') }}">الملخص المالي</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and request.endpoint.startswith('documents') %}active{% endif %}" href="{{ url_for('documents.index') }}">
                            <i class="fas fa-file-alt me-1"></i>المستندات
                        </a>
                    </li>
                    {% if current_user.is_admin() %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and request.endpoint == 'auth.users' %}active{% endif %}" href="{{ url_for('auth.users') }}">
                            <i class="fas fa-user-cog me-1"></i>المستخدمين
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2025 نظام إدارة العقارات. جميع الحقوق محفوظة.</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
