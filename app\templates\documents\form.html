{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {% if form.title.errors %}
                            {{ form.title(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.title(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.file.label(class="form-label") }}
                        {% if form.file.errors %}
                            {{ form.file(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.file(class="form-control") }}
                        {% endif %}
                        <small class="form-text text-muted">
                            الملفات المسموح بها: PDF, PNG, JPG, JPEG, DOC, DOCX, XLS, XLSX
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.related_to.label(class="form-label") }}
                        {% if form.related_to.errors %}
                            {{ form.related_to(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.related_to.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.related_to(class="form-select") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.related_id.label(class="form-label") }}
                        {% if form.related_id.errors %}
                            {{ form.related_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.related_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.related_id(class="form-select") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('documents.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const relatedToSelect = document.getElementById('related_to');
        const relatedIdSelect = document.getElementById('related_id');
        
        // تحديث قائمة المعرفات المرتبطة عند تغيير نوع العلاقة
        relatedToSelect.addEventListener('change', function() {
            // إرسال طلب AJAX للحصول على الخيارات المناسبة
            fetch(`/documents/get_related_items?related_to=${relatedToSelect.value}`)
                .then(response => response.json())
                .then(data => {
                    // حذف جميع الخيارات الحالية
                    relatedIdSelect.innerHTML = '';
                    
                    // إضافة الخيارات الجديدة
                    data.forEach(item => {
                        const option = document.createElement('option');
                        option.value = item.id;
                        option.textContent = item.name;
                        relatedIdSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error:', error));
        });
        
        // تشغيل الحدث عند تحميل الصفحة إذا كان هناك قيمة محددة مسبقًا
        if (relatedToSelect.value) {
            relatedToSelect.dispatchEvent(new Event('change'));
        }
    });
</script>
{% endblock %}
