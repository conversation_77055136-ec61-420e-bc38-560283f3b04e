@echo off
chcp 65001 >nul
title إنشاء جميع نسخ مكتب عصام الفت

echo ================================================================
echo                    إنشاء جميع نسخ التطبيق
echo ================================================================
echo                    مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

REM تحديد مجلد العمل الحالي
cd /d "%~dp0"

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo سيتم إنشاء النسخ التالية:
echo 1. النسخة المحمولة (تتطلب Python)
echo 2. النسخة التنفيذية (لا تتطلب Python)
echo.

set /p choice="هل تريد المتابعة؟ (y/n): "
if /i "%choice%" neq "y" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo ================================================================
echo                    إنشاء النسخة المحمولة
echo ================================================================
echo.

python create_portable_version.py

echo.
echo ================================================================
echo                    إنشاء النسخة التنفيذية
echo ================================================================
echo.

python build_executable.py

echo.
echo ================================================================
echo                    اكتملت جميع العمليات
echo ================================================================
echo.
echo تم إنشاء النسخ التالية:
echo - النسخة المحمولة: portable_version/
echo - ملف ZIP المحمول: مكتب_عصام_الفت_النسخة_المحمولة.zip
echo - النسخة التنفيذية: dist/مكتب_عصام_الفت/
echo.
echo يمكنك الآن توزيع هذه النسخ على أي جهاز Windows
echo.

pause
