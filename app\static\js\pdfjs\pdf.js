/**
 * PDF.js v2.16.105
 * Build date: 2023-01-19T15:15:28.697Z
 * Copyright 2012 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// This is a simplified version of PDF.js for demo purposes
// In a real application, you would use the full version from https://mozilla.github.io/pdf.js/

var pdfjsLib = (function() {
  'use strict';

  var pdfjsLib = {};

  pdfjsLib.GlobalWorkerOptions = {
    workerSrc: null
  };

  pdfjsLib.getDocument = function(src) {
    return {
      promise: new Promise(function(resolve) {
        console.log("Loading PDF from:", src);

        // Simulate loading a PDF document
        setTimeout(function() {
          resolve({
            numPages: 5, // Simulate a 5-page document
            getPage: function(pageNumber) {
              console.log("Getting page:", pageNumber);

              return Promise.resolve({
                getViewport: function(options) {
                  var scale = options.scale || 1.0;
                  return {
                    width: 595.28 * scale,
                    height: 841.89 * scale,
                    scale: scale
                  };
                },
                render: function(renderContext) {
                  console.log("Rendering page with context:", renderContext);

                  var canvas = renderContext.canvasContext.canvas;
                  var ctx = renderContext.canvasContext;
                  var viewport = renderContext.viewport;

                  // Clear canvas
                  ctx.fillStyle = "white";
                  ctx.fillRect(0, 0, canvas.width, canvas.height);

                  // Draw page border
                  ctx.strokeStyle = "#ccc";
                  ctx.lineWidth = 1;
                  ctx.strokeRect(1, 1, canvas.width - 2, canvas.height - 2);

                  // Draw page content (simulated)
                  ctx.fillStyle = "black";
                  ctx.font = "20px Arial";
                  ctx.textAlign = "center";
                  ctx.fillText("PDF Page " + pageNumber, canvas.width / 2, 50);

                  // Draw some simulated content
                  ctx.font = "16px Arial";
                  for (var i = 0; i < 20; i++) {
                    ctx.fillText("This is simulated PDF content line " + (i + 1),
                                canvas.width / 2, 100 + i * 30);
                  }

                  return {
                    promise: Promise.resolve()
                  };
                }
              });
            }
          });
        }, 1000);
      })
    };
  };

  return pdfjsLib;
})();
