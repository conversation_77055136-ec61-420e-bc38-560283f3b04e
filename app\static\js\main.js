// وظائف عامة للتطبيق

// تهيئة التلميحات
function initTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة النوافذ المنبثقة
function initPopovers() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '';
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('ar-SA', options);
}

// تنسيق العملة
function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '0 ر.س';
    return amount.toLocaleString('ar-SA') + ' ر.س';
}

// تحديث حقول النموذج بناءً على الاختيار
function updateFormFields() {
    // تحديث حقول النموذج بناءً على نوع العلاقة
    $('#related_to').on('change', function() {
        var relatedTo = $(this).val();
        var relatedIdField = $('#related_id');
        
        // إعادة تعيين الخيارات
        relatedIdField.empty();
        
        // تحميل الخيارات المناسبة بناءً على نوع العلاقة
        if (relatedTo === 'contract') {
            // تحميل العقود
            $.getJSON('/api/contracts', function(data) {
                relatedIdField.append($('<option>', {
                    value: 0,
                    text: 'اختر العقد'
                }));
                
                $.each(data, function(index, contract) {
                    relatedIdField.append($('<option>', {
                        value: contract.id,
                        text: contract.display_name
                    }));
                });
            });
        } else if (relatedTo === 'building') {
            // تحميل المباني
            $.getJSON('/api/buildings', function(data) {
                relatedIdField.append($('<option>', {
                    value: 0,
                    text: 'اختر المبنى'
                }));
                
                $.each(data, function(index, building) {
                    relatedIdField.append($('<option>', {
                        value: building.id,
                        text: building.name
                    }));
                });
            });
        } else if (relatedTo === 'unit') {
            // تحميل الوحدات
            $.getJSON('/api/units', function(data) {
                relatedIdField.append($('<option>', {
                    value: 0,
                    text: 'اختر الوحدة'
                }));
                
                $.each(data, function(index, unit) {
                    relatedIdField.append($('<option>', {
                        value: unit.id,
                        text: unit.display_name
                    }));
                });
            });
        } else if (relatedTo === 'tenant') {
            // تحميل المستأجرين
            $.getJSON('/api/tenants', function(data) {
                relatedIdField.append($('<option>', {
                    value: 0,
                    text: 'اختر المستأجر'
                }));
                
                $.each(data, function(index, tenant) {
                    relatedIdField.append($('<option>', {
                        value: tenant.id,
                        text: tenant.name
                    }));
                });
            });
        } else if (relatedTo === 'owner') {
            // تحميل الملاك
            $.getJSON('/api/owners', function(data) {
                relatedIdField.append($('<option>', {
                    value: 0,
                    text: 'اختر المالك'
                }));
                
                $.each(data, function(index, owner) {
                    relatedIdField.append($('<option>', {
                        value: owner.id,
                        text: owner.name
                    }));
                });
            });
        } else {
            // إذا كان نوع العلاقة غير معروف
            relatedIdField.append($('<option>', {
                value: 0,
                text: 'غير متاح'
            }));
        }
    });
}

// تأكيد الحذف
function confirmDelete(event, message) {
    if (!confirm(message || 'هل أنت متأكد من رغبتك في الحذف؟')) {
        event.preventDefault();
        return false;
    }
    return true;
}

// تهيئة الدوال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التلميحات
    initTooltips();
    
    // تهيئة النوافذ المنبثقة
    initPopovers();
    
    // تهيئة تحديث حقول النموذج
    if (typeof $ !== 'undefined') {
        updateFormFields();
    }
    
    // تهيئة تأكيد الحذف
    document.querySelectorAll('.confirm-delete').forEach(function(element) {
        element.addEventListener('click', function(event) {
            var message = this.getAttribute('data-confirm-message');
            return confirmDelete(event, message);
        });
    });
    
    // إخفاء رسائل التنبيه بعد 5 ثوانٍ
    setTimeout(function() {
        document.querySelectorAll('.alert').forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});
