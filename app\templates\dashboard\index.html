{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-4">لوحة التحكم</h1>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">الملاك</h6>
                        <h2 class="card-text">{{ owners_count }}</h2>
                    </div>
                    <i class="fas fa-user-tie fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>إجمالي الملاك</span>
                <a href="{{ url_for('owners.index') }}" class="text-white">عرض <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">المباني</h6>
                        <h2 class="card-text">{{ buildings_count }}</h2>
                    </div>
                    <i class="fas fa-building fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>إجمالي المباني</span>
                <a href="{{ url_for('properties.buildings') }}" class="text-white">عرض <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">الوحدات</h6>
                        <h2 class="card-text">{{ units_count }}</h2>
                    </div>
                    <i class="fas fa-home fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>إجمالي الوحدات</span>
                <a href="{{ url_for('properties.units') }}" class="text-white">عرض <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">العقود النشطة</h6>
                        <h2 class="card-text">{{ active_contracts_count }}</h2>
                    </div>
                    <i class="fas fa-file-contract fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>العقود الحالية</span>
                <a href="{{ url_for('tenants.contracts') }}" class="text-white">عرض <i class="fas fa-arrow-circle-left"></i></a>
            </div>
        </div>
    </div>
</div>

<!-- الملخص المالي -->
{% if current_user.is_accountant() %}
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>الإيرادات الشهرية
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="text-success">{{ monthly_income|format_currency }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>المصروفات الشهرية
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="text-danger">{{ monthly_expense|format_currency }}</h3>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>صافي الربح الشهري
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="{% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ net_profit|format_currency }}
                </h3>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row mb-4">
    <!-- العقود التي ستنتهي قريبًا -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header bg-warning">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>العقود التي ستنتهي قريبًا
                </h5>
            </div>
            <div class="card-body">
                {% if expiring_contracts %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>المستأجر</th>
                                <th>الوحدة</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contract in expiring_contracts %}
                            <tr>
                                <td>{{ contract.tenant_name }}</td>
                                <td>{{ contract.unit_number }} - {{ contract.building_name }}</td>
                                <td>{{ contract.end_date|format_date }}</td>
                                <td>
                                    <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-center">لا توجد عقود ستنتهي قريبًا.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- آخر المعاملات المالية -->
    {% if current_user.is_accountant() %}
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header bg-info">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-check-alt me-2"></i>آخر المعاملات المالية
                </h5>
            </div>
            <div class="card-body">
                {% if latest_transactions %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in latest_transactions %}
                            <tr>
                                <td>{{ transaction.transaction_date|format_date }}</td>
                                <td>
                                    {% if transaction.type == 'income' %}
                                    <span class="badge bg-success">إيراد</span>
                                    {% else %}
                                    <span class="badge bg-danger">مصروف</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.amount|format_currency }}</td>
                                <td>
                                    <a href="{{ url_for('finance.view_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-center">لا توجد معاملات مالية حديثة.</p>
                {% endif %}
            </div>
        </div>
    </div>
    {% else %}
    <div class="col-md-6 mb-3">
        <!-- عنصر بديل لمستخدمي مدخل البيانات -->
        <div class="card">
            <div class="card-header bg-secondary">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <p class="text-center">يمكنك إدارة المستأجرين والعقود والوحدات والمباني من القائمة الجانبية.</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- الرسوم البيانية -->
{% if current_user.is_accountant() %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>الإيرادات والمصروفات السنوية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="yearlyChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% if current_user.is_accountant() %}
        // بيانات الرسم البياني السنوي
        var yearlyData = {
            labels: ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [
                {
                    label: 'الإيرادات',
                    backgroundColor: 'rgba(40, 167, 69, 0.2)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1,
                    data: {{ yearly_income|tojson }}
                },
                {
                    label: 'المصروفات',
                    backgroundColor: 'rgba(220, 53, 69, 0.2)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 1,
                    data: {{ yearly_expense|tojson }}
                }
            ]
        };

        // إنشاء الرسم البياني السنوي
        var yearlyChart = new Chart(document.getElementById('yearlyChart'), {
            type: 'bar',
            data: yearlyData,
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        {% endif %}
    });
</script>
{% endblock %}
