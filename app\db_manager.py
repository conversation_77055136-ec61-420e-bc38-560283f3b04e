import sqlite3
import os
import time
from datetime import datetime
from flask import current_app, g
import threading

# قفل لضمان عدم حدوث تعارض في الوصول إلى قاعدة البيانات
db_lock = threading.Lock()

def adapt_datetime(val):
    """تحويل كائن datetime إلى نص"""
    return val.isoformat()

def convert_datetime(val):
    """تحويل نص إلى كائن datetime"""
    try:
        return datetime.fromisoformat(val.decode())
    except ValueError:
        # إذا كان التنسيق غير صالح، إرجاع النص كما هو
        return val.decode()

def get_db_connection():
    """إنشاء اتصال جديد بقاعدة البيانات"""
    # التأكد من وجود مجلد instance
    os.makedirs(os.path.dirname(current_app.config['DATABASE_PATH']), exist_ok=True)

    # تسجيل محولات التاريخ والوقت
    sqlite3.register_adapter(datetime, adapt_datetime)
    sqlite3.register_converter("timestamp", convert_datetime)

    # إنشاء اتصال جديد
    conn = sqlite3.connect(
        current_app.config['DATABASE_PATH'],
        detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES,
        timeout=30,  # زيادة مهلة الانتظار للاتصال
        isolation_level=None  # استخدام وضع الالتزام التلقائي
    )
    conn.row_factory = sqlite3.Row

    # تكوين قاعدة البيانات
    conn.execute("PRAGMA foreign_keys = ON")
    conn.execute("PRAGMA busy_timeout = 30000")  # 30 ثانية

    return conn

def execute_query(query, params=(), fetch_one=False, commit=False):
    """تنفيذ استعلام على قاعدة البيانات مع التعامل مع الأخطاء وإعادة المحاولة"""
    max_retries = 10
    retry_delay = 1.0  # ثانية واحدة

    for attempt in range(max_retries):
        try:
            # استخدام القفل لمنع التعارض
            with db_lock:
                # إنشاء اتصال جديد لكل عملية
                conn = get_db_connection()

                # تنفيذ الاستعلام
                cursor = conn.execute(query, params)

                # الحصول على النتائج إذا كان مطلوبًا
                if fetch_one:
                    result = cursor.fetchone()
                else:
                    result = cursor.fetchall()

                # الحصول على معرف السجل المدرج إذا كان الاستعلام إدراج
                last_id = cursor.lastrowid

                # إغلاق المؤشر
                cursor.close()

                # إغلاق الاتصال
                conn.close()

                # إرجاع النتائج
                if query.strip().upper().startswith("INSERT"):
                    return last_id
                else:
                    return result

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e) and attempt < max_retries - 1:
                # إذا كانت قاعدة البيانات مقفلة، ننتظر ثم نحاول مرة أخرى
                time.sleep(retry_delay * (attempt + 1))  # زيادة التأخير مع كل محاولة
                continue
            else:
                # إذا كان الخطأ من نوع آخر أو وصلنا للمحاولة الأخيرة، نرفع الخطأ
                raise
        except Exception as e:
            # أي خطأ آخر، نرفعه مباشرة
            raise

    # إذا وصلنا إلى هنا، فقد فشلت جميع المحاولات
    raise sqlite3.OperationalError("فشلت جميع محاولات الاتصال بقاعدة البيانات")

def query_db(query, args=(), one=False):
    """تنفيذ استعلام واسترجاع النتائج"""
    result = execute_query(query, args, fetch_one=one)
    return result

def insert_db(table, fields=None):
    """إدراج سجل جديد في الجدول المحدد"""
    if fields is None:
        return None

    # إضافة حقول التاريخ تلقائيًا إذا كانت موجودة في الجدول
    if 'created_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings', 'units']:
        fields['created_at'] = datetime.now()
    if 'updated_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings', 'units']:
        fields['updated_at'] = datetime.now()

    placeholders = ', '.join('?' * len(fields))
    columns = ', '.join(fields.keys())

    query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"

    return execute_query(query, list(fields.values()))

def update_db(table, id, fields=None):
    """تحديث سجل موجود في الجدول المحدد"""
    if fields is None:
        return False

    # إضافة حقل updated_at تلقائيًا إذا كان موجودًا في الجدول
    if 'updated_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings', 'units']:
        fields['updated_at'] = datetime.now()

    set_clause = ', '.join([f"{key} = ?" for key in fields.keys()])

    query = f"UPDATE {table} SET {set_clause} WHERE id = ?"

    values = list(fields.values())
    values.append(id)

    execute_query(query, values)
    return True

def delete_db(table, id):
    """حذف سجل من الجدول المحدد"""
    query = f"DELETE FROM {table} WHERE id = ?"

    execute_query(query, (id,))
    return True
