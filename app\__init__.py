"""
نظام إدارة مكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import importlib.util
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('app_init')

# تطبيق إصلاح المسارات قبل أي استيراد آخر
try:
    logger.info("Applying path fixes...")

    # محاولة استيراد ملف fix_paths.py
    fix_paths_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'fix_paths.py')
    if os.path.exists(fix_paths_path):
        logger.info(f"Loading path fixes from: {fix_paths_path}")
        spec = importlib.util.spec_from_file_location("fix_paths", fix_paths_path)
        fix_paths_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(fix_paths_module)
    else:
        logger.warning(f"Path fix file not found at: {fix_paths_path}")

        # تطبيق إصلاح المسارات مباشرة
        logger.info("Applying direct path fixes...")

        # تحديد المسار الأساسي للتطبيق
        if getattr(sys, 'frozen', False):
            # في حالة التشغيل كملف تنفيذي
            application_path = os.path.dirname(sys.executable)
            logger.info(f"Running as frozen application from: {application_path}")
        else:
            # في حالة التشغيل كسكريبت
            application_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            logger.info(f"Running as script from: {application_path}")

        # إنشاء المجلدات الضرورية
        required_dirs = [
            os.path.join(application_path, 'instance'),
            os.path.join(application_path, 'uploads'),
            os.path.join(application_path, 'uploads', 'buildings'),
            os.path.join(application_path, 'uploads', 'contracts'),
            os.path.join(application_path, 'uploads', 'documents'),
            os.path.join(application_path, 'uploads', 'owners'),
            os.path.join(application_path, 'uploads', 'tenants'),
            os.path.join(application_path, 'uploads', 'transactions')
        ]

        for directory in required_dirs:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"Created directory: {directory}")

        # إضافة المسار الأساسي إلى مسار البحث
        if application_path not in sys.path:
            sys.path.insert(0, application_path)
            logger.info(f"Added {application_path} to sys.path")
except Exception as e:
    logger.error(f"Error applying path fixes: {str(e)}")
    import traceback
    logger.error(traceback.format_exc())

# تطبيق إصلاح Werkzeug وFlask-Login قبل استيراد Flask
try:
    print("Applying patches before importing Flask...")

    # محاولة استيراد ملف patch_werkzeug.py
    patch_werkzeug_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'patch_werkzeug.py')
    if os.path.exists(patch_werkzeug_path):
        print(f"Loading Werkzeug patch from: {patch_werkzeug_path}")
        spec = importlib.util.spec_from_file_location("patch_werkzeug", patch_werkzeug_path)
        patch_werkzeug_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(patch_werkzeug_module)
        patch_werkzeug_module.patch_werkzeug()
    else:
        print(f"Werkzeug patch file not found at: {patch_werkzeug_path}")

        # تطبيق الإصلاح مباشرة
        print("Applying direct Werkzeug patch...")
        import werkzeug.urls
        from urllib.parse import parse_qs, quote, quote_plus, unquote, unquote_plus

        if not hasattr(werkzeug.urls, 'url_decode'):
            print("Adding url_decode to werkzeug.urls")

            def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
                """تنفيذ بديل لـ url_decode"""
                try:
                    from werkzeug.datastructures import MultiDict
                    result_cls = MultiDict
                except ImportError:
                    result_cls = dict

                if cls is not None:
                    result_cls = cls

                result = result_cls()
                parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)

                for key, values in parsed.items():
                    for value in values:
                        if hasattr(result, 'add'):
                            result.add(key, value)
                        else:
                            result[key] = value

                return result

            werkzeug.urls.url_decode = url_decode
            print("Added url_decode to werkzeug.urls")

        # إضافة دوال أخرى مفقودة
        missing_functions = []
        for func_name in ['url_encode', 'url_quote', 'url_quote_plus', 'url_unquote', 'url_unquote_plus']:
            if not hasattr(werkzeug.urls, func_name):
                missing_functions.append(func_name)

        if missing_functions:
            print(f"Adding missing functions to werkzeug.urls: {', '.join(missing_functions)}")

            if 'url_quote' in missing_functions:
                werkzeug.urls.url_quote = quote
                print("Added url_quote")

            if 'url_quote_plus' in missing_functions:
                werkzeug.urls.url_quote_plus = quote_plus
                print("Added url_quote_plus")

            if 'url_unquote' in missing_functions:
                werkzeug.urls.url_unquote = unquote
                print("Added url_unquote")

            if 'url_unquote_plus' in missing_functions:
                werkzeug.urls.url_unquote_plus = unquote_plus
                print("Added url_unquote_plus")

    # محاولة استيراد ملف patch_flask_login.py
    patch_flask_login_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'patch_flask_login.py')
    if os.path.exists(patch_flask_login_path):
        print(f"Loading Flask-Login patch from: {patch_flask_login_path}")
        spec = importlib.util.spec_from_file_location("patch_flask_login", patch_flask_login_path)
        patch_flask_login_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(patch_flask_login_module)
        patch_flask_login_module.patch_flask_login()
    else:
        print(f"Flask-Login patch file not found at: {patch_flask_login_path}")

    print("Patches applied successfully")
except Exception as e:
    print(f"Error applying patches: {str(e)}")
    import traceback
    traceback.print_exc()

# استيراد Flask والمكتبات الأخرى
from flask import Flask
from flask_login import LoginManager, login_required
from flask_bcrypt import Bcrypt
from flask_wtf.csrf import CSRFProtect
from flask_mail import Mail

# تهيئة الإضافات
login_manager = LoginManager()
bcrypt = Bcrypt()
csrf = CSRFProtect()
mail = Mail()

def create_app(test_config=None):
    """إنشاء وتكوين تطبيق Flask"""
    # إنشاء وتكوين التطبيق
    app = Flask(__name__, instance_relative_config=True)

    # تحميل الإعدادات الافتراضية
    app.config.from_object('config.Config')

    # تحميل إعدادات الاختبار إذا تم تمريرها
    if test_config is not None:
        app.config.from_mapping(test_config)

    # التأكد من وجود مجلد instance
    try:
        os.makedirs(app.instance_path, exist_ok=True)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    except OSError:
        pass

    # إضافة مسار ثابت للملفات المرفوعة
    from flask import send_from_directory, abort, render_template
    from markupsafe import Markup

    # إضافة مسار ثابت للملفات المرفوعة
    @app.route('/uploads/<path:filename>')
    def uploaded_file(filename):
        try:
            return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
        except:
            abort(404)

    @app.route('/file/<int:document_id>')
    @login_required
    def view_document_file(document_id):
        """عرض ملف المستند مباشرة"""
        from app.utils import query_db
        import os

        print(f"Viewing document with ID: {document_id}")

        document = query_db('SELECT * FROM documents WHERE id = ?', (document_id,), one=True)

        if not document:
            print(f"Document with ID {document_id} not found")
            abort(404)

        # استخراج اسم الملف من المسار
        filename = document['file_path']
        print(f"Filename from database: {filename}")

        # البحث عن الملف في مجلد التحميل
        upload_folder = app.config['UPLOAD_FOLDER']
        print(f"Upload folder: {upload_folder}")

        # محاولة العثور على الملف بالاسم الدقيق
        file_path = os.path.join(upload_folder, filename)
        print(f"Full file path: {file_path}")
        print(f"File exists: {os.path.exists(file_path)}")

        if os.path.exists(file_path):
            print(f"Sending file: {filename}")
            return send_from_directory(upload_folder, filename)

        # محاولة العثور على الملف بدون مسافات
        filename_no_spaces = filename.replace(' ', '')
        print(f"Filename without spaces: {filename_no_spaces}")

        for file in os.listdir(upload_folder):
            print(f"Checking file: {file}")
            if file.replace(' ', '') == filename_no_spaces:
                print(f"Found matching file: {file}")
                return send_from_directory(upload_folder, file)

        # محاولة العثور على الملف بالجزء الأول من الاسم
        filename_prefix = filename.split('_')[0]
        print(f"Filename prefix: {filename_prefix}")

        for file in os.listdir(upload_folder):
            if file.startswith(filename_prefix):
                print(f"Found file with matching prefix: {file}")
                return send_from_directory(upload_folder, file)

        # إذا لم يتم العثور على الملف
        print(f"File not found for document ID: {document_id}")
        abort(404)

    # تهيئة الإضافات
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'

    bcrypt.init_app(app)
    csrf.init_app(app)
    mail.init_app(app)

    # تهيئة قاعدة البيانات
    from . import db
    db.init_app(app)

    # تسجيل blueprints
    from .auth import auth_bp
    app.register_blueprint(auth_bp)

    from .dashboard import dashboard_bp
    app.register_blueprint(dashboard_bp)

    from .owners import owners_bp
    app.register_blueprint(owners_bp)

    from .properties import properties_bp
    app.register_blueprint(properties_bp)

    from .tenants import tenants_bp
    app.register_blueprint(tenants_bp)

    from .documents import documents_bp
    app.register_blueprint(documents_bp)

    from .finance import finance_bp
    app.register_blueprint(finance_bp)

    from .reports import reports_bp
    app.register_blueprint(reports_bp)

    # تسجيل معالج الخطأ 404
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('errors/404.html'), 404

    # تسجيل معالج الخطأ 500
    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('errors/500.html'), 500

    # تسجيل دالة مساعدة للقوالب
    @app.template_filter('format_currency')
    def format_currency(value):
        if value is None:
            return f"0 {app.config['CURRENCY_SYMBOL']}"
        return f"{value:,.2f} {app.config['CURRENCY_SYMBOL']}"

    @app.template_filter('format_date')
    def format_date(value):
        if value is None:
            return ""
        if isinstance(value, str):
            try:
                value = datetime.strptime(value, '%Y-%m-%d')
            except ValueError:
                return value
        return value.strftime(app.config['DATE_FORMAT'])

    @app.template_filter('nl2br')
    def nl2br(value):
        """تحويل السطور الجديدة إلى علامات <br>"""
        if value:
            return Markup(value.replace('\n', '<br>'))

    return app

# استيراد النماذج المطلوبة
from datetime import datetime
