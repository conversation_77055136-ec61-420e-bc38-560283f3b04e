/*
نظام إدارة مكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: *************
جميع الحقوق محفوظة © 2025
*/

-- إ<PERSON><PERSON><PERSON><PERSON> جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'accountant', 'data_entry')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الملاك
CREATE TABLE IF NOT EXISTS owners (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    id_number TEXT UNIQUE,
    phone TEXT,
    email TEXT,
    address TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المباني
CREATE TABLE IF NOT EXISTS buildings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    district TEXT,
    city TEXT NOT NULL,
    owner_id INTEGER NOT NULL,
    deed_number TEXT,
    build_year INTEGER,
    floors_count INTEGER,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES owners (id)
);

-- إنشاء جدول الوحدات
CREATE TABLE IF NOT EXISTS units (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_number TEXT NOT NULL,
    building_id INTEGER NOT NULL,
    floor_number INTEGER,
    type TEXT NOT NULL,
    area REAL,
    rooms_count INTEGER,
    bathrooms_count INTEGER,
    rent_amount REAL NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('vacant', 'occupied', 'maintenance', 'reserved')),
    site_number_electricity TEXT,
    meter_number_electricity TEXT,
    water_meter_number TEXT,
    water_meter_account TEXT,
    electricity_meter_type TEXT CHECK (electricity_meter_type IN ('shared', 'separate', 'other', NULL)),
    water_meter_type TEXT CHECK (water_meter_type IN ('shared', 'separate', 'other', NULL)),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (building_id) REFERENCES buildings (id)
);

-- إنشاء جدول المستأجرين
CREATE TABLE IF NOT EXISTS tenants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    id_number TEXT UNIQUE,
    phone TEXT NOT NULL,
    email TEXT,
    nationality TEXT,
    birth_date DATE,
    occupation TEXT,
    employer TEXT,
    emergency_contact TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول العقود
CREATE TABLE IF NOT EXISTS contracts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_id INTEGER NOT NULL,
    tenant_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    rent_amount REAL NOT NULL,
    payment_frequency TEXT NOT NULL,
    deposit_amount REAL,
    contract_fee REAL,
    contract_fee_paid REAL DEFAULT 0,
    contract_number TEXT UNIQUE,
    status TEXT NOT NULL,
    termination_date DATE,
    termination_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (unit_id) REFERENCES units (id),
    FOREIGN KEY (tenant_id) REFERENCES tenants (id)
);

-- إنشاء جدول المستندات
CREATE TABLE IF NOT EXISTS documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    related_to TEXT NOT NULL CHECK (related_to IN ('owner', 'building', 'unit', 'tenant', 'contract', 'transaction')),
    related_id INTEGER NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    uploaded_by INTEGER,
    FOREIGN KEY (uploaded_by) REFERENCES users (id)
);

-- إنشاء جدول المعاملات المالية
CREATE TABLE IF NOT EXISTS transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_date DATE NOT NULL,
    amount REAL NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
    category TEXT NOT NULL,
    description TEXT,
    related_to TEXT CHECK (related_to IN ('contract', 'building', 'unit', 'other')),
    related_id INTEGER,
    payment_method TEXT,
    reference_number TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- إنشاء جدول قراءات العدادات
CREATE TABLE IF NOT EXISTS meter_readings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_id INTEGER NOT NULL,
    reading_date DATE NOT NULL,
    electricity_reading REAL,
    water_reading REAL,
    gas_reading REAL,
    notes TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (unit_id) REFERENCES units (id),
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- إنشاء جدول التنبيهات
CREATE TABLE IF NOT EXISTS notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('contract_expiry', 'payment_due', 'maintenance', 'other')),
    related_to TEXT CHECK (related_to IN ('contract', 'unit', 'building', 'tenant', 'other')),
    related_id INTEGER,
    is_read BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP
);

-- إنشاء جدول طلبات الصيانة
CREATE TABLE IF NOT EXISTS maintenance_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_id INTEGER NOT NULL,
    tenant_id INTEGER,
    request_date DATE NOT NULL,
    description TEXT NOT NULL,
    priority TEXT NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
    completion_date DATE,
    cost REAL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (unit_id) REFERENCES units (id),
    FOREIGN KEY (tenant_id) REFERENCES tenants (id)
);

-- إدخال مستخدم أدمن افتراضي (كلمة المرور: admin123)
INSERT OR IGNORE INTO users (username, password, name, email, role)
VALUES ('admin', '$2b$12$rj8MnLcKBxAgL7GUHvYkQOuUUEL2JZjzh5GRH1vFbVBGMQXJyuUtq', 'مدير النظام', '<EMAIL>', 'admin');
