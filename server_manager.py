#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إدارة مكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import sys
import os
import subprocess
import signal
import socket
import webbrowser
import traceback
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('server_manager')

# تطبيق إصلاح المسارات قبل أي استيراد آخر
try:
    logger.info("Applying path fixes...")

    # محاولة استيراد ملف fix_paths.py
    fix_paths_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fix_paths.py')
    if os.path.exists(fix_paths_path):
        logger.info(f"Loading path fixes from: {fix_paths_path}")
        import importlib.util
        spec = importlib.util.spec_from_file_location("fix_paths", fix_paths_path)
        fix_paths_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(fix_paths_module)
    else:
        logger.warning(f"Path fix file not found at: {fix_paths_path}")

    # تم إزالة استيراد fix_owners.py لأنه لم يعد ضروريًا
except Exception as e:
    logger.error(f"Error applying fixes: {str(e)}")
    traceback.print_exc()

# تطبيق إصلاحات قبل استيراد أي مكتبات أخرى
try:
    # محاولة استيراد ملف الإصلاح الشامل
    monkey_patch_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'monkey_patch.py')
    if os.path.exists(monkey_patch_path):
        print(f"Loading monkey patch from: {monkey_patch_path}")
        import importlib.util
        spec = importlib.util.spec_from_file_location("monkey_patch", monkey_patch_path)
        monkey_patch = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(monkey_patch)
    else:
        # البحث عن الملف في مجلدات مختلفة
        possible_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), '_internal', 'monkey_patch.py'),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'monkey_patch.py'),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'werkzeug_fix.py'),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), '_internal', 'werkzeug_fix.py')
        ]

        for path in possible_paths:
            if os.path.exists(path):
                print(f"Loading patch from: {path}")
                import importlib.util
                spec = importlib.util.spec_from_file_location("patch", path)
                patch_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(patch_module)
                break
        else:
            # تطبيق الإصلاح مباشرة
            print("Applying direct patch...")

            # تطبيق إصلاح Werkzeug
            try:
                import werkzeug.urls
                from urllib.parse import parse_qs, quote, quote_plus, unquote, unquote_plus

                if not hasattr(werkzeug.urls, 'url_decode'):
                    print("Adding url_decode to werkzeug.urls")

                    def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
                        """تنفيذ بديل لـ url_decode"""
                        result = {}
                        parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)

                        for key, values in parsed.items():
                            for value in values:
                                result[key] = value

                        return result

                    werkzeug.urls.url_decode = url_decode
                    print("Added url_decode to werkzeug.urls")

                # إضافة دوال أخرى مفقودة
                missing_functions = []
                for func_name in ['url_encode', 'url_quote', 'url_quote_plus', 'url_unquote', 'url_unquote_plus']:
                    if not hasattr(werkzeug.urls, func_name):
                        missing_functions.append(func_name)

                if missing_functions:
                    print(f"Adding missing functions to werkzeug.urls: {', '.join(missing_functions)}")

                    if 'url_quote' in missing_functions:
                        werkzeug.urls.url_quote = quote

                    if 'url_quote_plus' in missing_functions:
                        werkzeug.urls.url_quote_plus = quote_plus

                    if 'url_unquote' in missing_functions:
                        werkzeug.urls.url_unquote = unquote

                    if 'url_unquote_plus' in missing_functions:
                        werkzeug.urls.url_unquote_plus = unquote_plus
            except Exception as werkzeug_error:
                print(f"Error applying Werkzeug patch: {str(werkzeug_error)}")
except Exception as e:
    print(f"Error loading patches: {str(e)}")
    traceback.print_exc()

# تعيين المجلد الحالي للتطبيق
if getattr(sys, 'frozen', False):
    # في حالة التشغيل كملف تنفيذي
    application_path = os.path.dirname(sys.executable)
    print(f"Running as frozen application from: {application_path}")
    os.chdir(application_path)
    # إضافة المجلد الحالي إلى مسار البحث
    if application_path not in sys.path:
        sys.path.insert(0, application_path)

    # إنشاء المجلدات الضرورية إذا لم تكن موجودة
    os.makedirs(os.path.join(application_path, 'instance'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'buildings'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'contracts'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'documents'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'owners'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'tenants'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'transactions'), exist_ok=True)

    # إضافة مجلد _internal إلى مسار البحث إذا كان موجودًا
    internal_path = os.path.join(application_path, '_internal')
    if os.path.exists(internal_path) and internal_path not in sys.path:
        print(f"Adding internal path to sys.path: {internal_path}")
        sys.path.insert(0, internal_path)
else:
    # في حالة التشغيل كسكريبت
    application_path = os.path.dirname(os.path.abspath(__file__))
    print(f"Running as script from: {application_path}")
    if application_path not in sys.path:
        sys.path.insert(0, application_path)

    # إنشاء المجلدات الضرورية إذا لم تكن موجودة
    os.makedirs(os.path.join(application_path, 'instance'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'buildings'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'contracts'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'documents'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'owners'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'tenants'), exist_ok=True)
    os.makedirs(os.path.join(application_path, 'uploads', 'transactions'), exist_ok=True)

# طباعة مسار البحث للتشخيص
print("Python search path:")
for path in sys.path:
    print(f"  - {path}")

# ضبط ترميز النصوص للتعامل مع اللغة العربية
try:
    # محاولة ضبط ترميز النظام
    if sys.platform.startswith('win'):
        # على نظام Windows
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        if hasattr(sys, 'frozen'):
            # في حالة التشغيل كملف تنفيذي
            try:
                sys.stdout.reconfigure(encoding='utf-8')
                sys.stderr.reconfigure(encoding='utf-8')
            except:
                # Fallback for older Python versions
                import codecs
                sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
                sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
except Exception as e:
    print(f"تحذير: فشل في ضبط الترميز: {str(e)}")
try:
    import qrcode
    from io import BytesIO
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QLabel,
                                QVBoxLayout, QHBoxLayout, QWidget, QComboBox,
                                QLineEdit, QGroupBox, QFormLayout, QSpacerItem,
                                QSizePolicy, QMessageBox, QRadioButton, QButtonGroup,
                                QToolButton, QDialog, QTextEdit, QDialogButtonBox)
    from PyQt5.QtGui import QPixmap, QFont, QIcon, QImage
    from PyQt5.QtCore import Qt, QTimer, QSize, QByteArray
    GUI_AVAILABLE = True
except ImportError as e:
    print(f"GUI libraries not available: {e}")
    GUI_AVAILABLE = False

class ServerManager(QMainWindow):
    def __init__(self):
        super().__init__()

        # تهيئة المتغيرات
        self.process_id = None
        self.is_server_running = False
        self.server_url = None
        self.qr_dialog = None
        self.http_server = None
        self.server_thread = None

        # إعداد واجهة المستخدم
        self.init_ui()

    def init_ui(self):
        # إعداد النافذة الرئيسية
        self.setWindowTitle("مدير خادم مكتب عصام الفت")
        self.setMinimumSize(600, 500)
        self.setWindowIcon(QIcon('file.jpeg'))

        # إنشاء الويدجت الرئيسي والتخطيط
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # إضافة الشعار والعنوان
        header_layout = QHBoxLayout()

        # إضافة الشعار
        logo_label = QLabel()
        pixmap = QPixmap('file.jpeg')
        scaled_pixmap = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        logo_label.setPixmap(scaled_pixmap)
        logo_label.setFixedSize(100, 100)
        header_layout.addWidget(logo_label)

        # إضافة العنوان
        title_label = QLabel("مكتب عصام الفت لإدارة الأملاك")
        title_font = QFont("Arial", 18, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        main_layout.addLayout(header_layout)

        # إضافة خط فاصل
        separator = QLabel()
        separator.setFrameShape(QLabel.HLine)
        separator.setFrameShadow(QLabel.Sunken)
        main_layout.addWidget(separator)

        # إعدادات الخادم
        server_group = QGroupBox("إعدادات الخادم")
        server_layout = QVBoxLayout()
        server_group.setLayout(server_layout)

        # نوع الخادم (محلي أو شبكة)
        server_type_label = QLabel("اختر نوع تشغيل الخادم:")
        server_type_label.setAlignment(Qt.AlignCenter)
        server_type_label.setFont(QFont("Arial", 10, QFont.Bold))
        server_layout.addWidget(server_type_label)

        self.server_type_group = QButtonGroup()
        server_type_layout = QHBoxLayout()

        self.local_radio = QRadioButton("خادم محلي (على هذا الجهاز فقط)")
        self.local_radio.setChecked(True)
        self.server_type_group.addButton(self.local_radio)
        server_type_layout.addWidget(self.local_radio)

        self.network_radio = QRadioButton("خادم شبكة (متاح لجميع الأجهزة)")
        self.server_type_group.addButton(self.network_radio)
        server_type_layout.addWidget(self.network_radio)

        server_layout.addLayout(server_type_layout)

        # إضافة وصف توضيحي
        description_label = QLabel(
            "الخادم المحلي: يمكن الوصول إليه فقط من هذا الجهاز.\n"
            "خادم الشبكة: يمكن الوصول إليه من أي جهاز متصل بنفس الشبكة."
        )
        description_label.setAlignment(Qt.AlignCenter)
        server_layout.addWidget(description_label)

        # إخفاء حقول الإدخال ولكن الاحتفاظ بها للاستخدام الداخلي
        self.ip_input = QLineEdit("127.0.0.1")
        self.ip_input.setVisible(False)

        self.port_input = QLineEdit("5000")
        self.port_input.setVisible(False)

        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["وضع التطوير", "وضع الإنتاج"])
        self.mode_combo.setCurrentIndex(0)  # وضع التطوير افتراضيًا
        self.mode_combo.setVisible(False)

        main_layout.addWidget(server_group)

        # أزرار التحكم
        control_layout = QHBoxLayout()

        self.start_button = QPushButton("تشغيل الخادم")
        self.start_button.setFixedHeight(40)
        self.start_button.clicked.connect(self.start_server)
        control_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("إيقاف الخادم")
        self.stop_button.setFixedHeight(40)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_server)
        control_layout.addWidget(self.stop_button)

        main_layout.addLayout(control_layout)

        # حالة الخادم
        self.status_label = QLabel("الخادم متوقف")
        self.status_label.setAlignment(Qt.AlignCenter)
        status_font = QFont("Arial", 12)
        self.status_label.setFont(status_font)
        main_layout.addWidget(self.status_label)

        # إضافة مساحة فارغة
        spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        main_layout.addItem(spacer)

        # إضافة حقوق الملكية
        footer_group = QGroupBox("حقوق الملكية")
        footer_layout = QVBoxLayout()

        # نص حقوق الملكية
        copyright_label = QLabel("تم التطوير والبرمجة بواسطة شركة المبرمج المصري")
        copyright_label.setAlignment(Qt.AlignCenter)
        footer_font = QFont("Arial", 9)
        copyright_label.setFont(footer_font)
        footer_layout.addWidget(copyright_label)

        # أزرار التواصل الاجتماعي
        social_layout = QHBoxLayout()

        # زر فيسبوك
        fb_button = self.create_social_button(
            "فيسبوك",
            "https://www.facebook.com/almbarmg",
            self.get_facebook_icon()
        )
        social_layout.addWidget(fb_button)

        # زر واتساب
        whatsapp_button = self.create_social_button(
            "واتساب",
            "https://wa.me/201032540807",
            self.get_whatsapp_icon()
        )
        social_layout.addWidget(whatsapp_button)

        footer_layout.addLayout(social_layout)

        # حقوق النشر
        rights_label = QLabel("جميع الحقوق محفوظة © 2025")
        rights_label.setAlignment(Qt.AlignCenter)
        rights_label.setFont(footer_font)
        footer_layout.addWidget(rights_label)

        footer_group.setLayout(footer_layout)
        main_layout.addWidget(footer_group)

        # ربط إشارات التغيير
        self.local_radio.toggled.connect(self.update_server_type)
        self.network_radio.toggled.connect(self.update_server_type)

        # تحديث واجهة المستخدم
        self.update_server_type()

    def update_server_type(self):
        """تحديث واجهة المستخدم بناءً على نوع الخادم المحدد"""
        if self.local_radio.isChecked():
            # خادم محلي - يمكن الوصول إليه فقط من هذا الجهاز
            self.ip_input.setText("127.0.0.1")
        else:
            # خادم شبكة - يمكن الوصول إليه من أي جهاز على الشبكة
            self.ip_input.setText("0.0.0.0")

    def create_social_button(self, text, url, icon=None):
        """إنشاء زر للتواصل الاجتماعي"""
        button = QPushButton(text)
        button.setCursor(Qt.PointingHandCursor)
        button.setFixedHeight(30)

        if icon:
            button.setIcon(icon)
            button.setIconSize(QSize(16, 16))

        button.clicked.connect(lambda: webbrowser.open(url))
        return button

    def get_facebook_icon(self):
        """إنشاء أيقونة فيسبوك"""
        # استخدام أيقونة نصية بدلاً من صورة
        return QIcon.fromTheme("facebook", QIcon.fromTheme("web-browser"))

    def get_whatsapp_icon(self):
        """إنشاء أيقونة واتساب"""
        # استخدام أيقونة نصية بدلاً من صورة
        return QIcon.fromTheme("whatsapp", QIcon.fromTheme("phone"))

    def start_server(self):
        """تشغيل خادم Flask"""
        if self.is_server_running:
            return

        # الحصول على إعدادات الخادم
        host = self.ip_input.text()
        port = self.port_input.text()

        # التحقق من صحة المنفذ
        try:
            port_num = int(port)
            if port_num < 1 or port_num > 65535:
                raise ValueError("رقم المنفذ غير صالح")
        except ValueError:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم منفذ صالح (1-65535)")
            return

        # التحقق من توفر المنفذ
        if not self.is_port_available(host, port_num):
            print(f"Port {port_num} is busy, attempting to close it")
            # محاولة إغلاق المنفذ بالقوة
            port_closed = self.close_port(port_num)

            # انتظار لحظة للتأكد من تحرير المنفذ
            import time
            time.sleep(1)

            # التحقق مرة أخرى
            if not self.is_port_available(host, port_num):
                if port_closed:
                    print(f"Port {port_num} is still busy even after closing attempt")
                    QMessageBox.warning(self, "خطأ", f"المنفذ {port} مشغول بالفعل ولا يمكن تحريره. يرجى إعادة تشغيل التطبيق أو اختيار منفذ آخر.")
                else:
                    print(f"Failed to close port {port_num}")
                    QMessageBox.warning(self, "خطأ", f"المنفذ {port} مشغول بالفعل ولا يمكن تحريره. يرجى إعادة تشغيل التطبيق أو اختيار منفذ آخر.")
                return
            else:
                print(f"Successfully closed port {port_num}, it is now available")

        # تحديد وضع التشغيل - لم يعد مطلوبًا

        # تحديد المعلمات
        if self.network_radio.isChecked():
            # إذا كان خادم شبكة، نستخدم 0.0.0.0
            host = "0.0.0.0"
        else:
            # إذا كان خادم محلي، نستخدم 127.0.0.1
            host = "127.0.0.1"

        print(f"Starting server on {host}:{port}")

        try:
            # تشغيل الخادم في خيط منفصل
            import threading

            # تعريف دالة تشغيل الخادم
            def run_flask_server():
                try:
                    print("Starting Flask server thread...")

                    # استيراد الوحدات المطلوبة
                    try:
                        print("Importing required modules...")

                        # تطبيق إصلاحات مرة أخرى داخل الخيط
                        try:
                            # البحث عن ملف الإصلاح في مجلدات مختلفة
                            possible_fix_paths = [
                                os.path.join(application_path, 'monkey_patch.py'),
                                os.path.join(application_path, '_internal', 'monkey_patch.py'),
                                os.path.join(application_path, 'werkzeug_fix.py'),
                                os.path.join(application_path, '_internal', 'werkzeug_fix.py'),
                                os.path.join(application_path, 'patch_werkzeug.py'),
                                os.path.join(application_path, '_internal', 'patch_werkzeug.py'),
                                os.path.join(application_path, 'patch_flask_login.py'),
                                os.path.join(application_path, '_internal', 'patch_flask_login.py')
                            ]

                            for fix_path in possible_fix_paths:
                                if os.path.exists(fix_path):
                                    print(f"Loading patch in thread from: {fix_path}")
                                    import importlib.util
                                    spec = importlib.util.spec_from_file_location("patch", fix_path)
                                    patch_module = importlib.util.module_from_spec(spec)
                                    spec.loader.exec_module(patch_module)
                                    break
                            else:
                                # تطبيق الإصلاح مباشرة
                                print("Applying direct patch in thread...")

                                # تطبيق إصلاح Werkzeug
                                try:
                                    import werkzeug.urls
                                    from urllib.parse import parse_qs, quote, quote_plus, unquote, unquote_plus

                                    if not hasattr(werkzeug.urls, 'url_decode'):
                                        print("Adding url_decode to werkzeug.urls in thread")

                                        def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
                                            """تنفيذ بديل لـ url_decode"""
                                            result = {}
                                            parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)

                                            for key, values in parsed.items():
                                                for value in values:
                                                    result[key] = value

                                            return result

                                        werkzeug.urls.url_decode = url_decode
                                        print("Added url_decode to werkzeug.urls in thread")

                                    # إضافة دوال أخرى مفقودة
                                    missing_functions = []
                                    for func_name in ['url_encode', 'url_quote', 'url_quote_plus', 'url_unquote', 'url_unquote_plus']:
                                        if not hasattr(werkzeug.urls, func_name):
                                            missing_functions.append(func_name)

                                    if missing_functions:
                                        print(f"Adding missing functions to werkzeug.urls in thread: {', '.join(missing_functions)}")

                                        if 'url_quote' in missing_functions:
                                            werkzeug.urls.url_quote = quote

                                        if 'url_quote_plus' in missing_functions:
                                            werkzeug.urls.url_quote_plus = quote_plus

                                        if 'url_unquote' in missing_functions:
                                            werkzeug.urls.url_unquote = unquote

                                        if 'url_unquote_plus' in missing_functions:
                                            werkzeug.urls.url_unquote_plus = unquote_plus
                                except Exception as werkzeug_error:
                                    print(f"Error applying Werkzeug patch in thread: {str(werkzeug_error)}")
                        except Exception as fix_error:
                            print(f"Error loading patches in thread: {str(fix_error)}")
                            traceback.print_exc()

                        # استيراد الوحدات المطلوبة
                        # محاولة استيراد من backend أولاً، ثم من app
                        try:
                            from backend.app import create_flask_app
                            app = create_flask_app()
                            print("Flask app imported from backend successfully")
                        except ImportError:
                            try:
                                from app import create_app
                                app = create_app()
                                print("Flask app imported from app successfully")
                            except ImportError as ie:
                                print(f"Error importing Flask app: {str(ie)}")
                                raise

                        import bcrypt
                        from datetime import datetime
                        import sqlite3
                        print("Modules imported successfully")
                    except ImportError as ie:
                        print(f"Error importing modules: {str(ie)}")
                        traceback.print_exc()
                        self.is_server_running = False
                        return

                    # التطبيق تم إنشاؤه بالفعل في قسم الاستيراد
                    try:
                        print("Flask app ready")
                        if 'app' not in locals():
                            raise Exception("Flask app not created")
                        print("Flask app validated successfully")
                    except Exception as app_error:
                        print(f"Error validating Flask app: {str(app_error)}")
                        traceback.print_exc()
                        self.is_server_running = False
                        return

                    # التحقق من وجود قاعدة البيانات
                    db_path = app.config['DATABASE_PATH']
                    print(f"Database path: {db_path}")

                    if not os.path.exists(db_path):
                        print("Setting up a new database...")

                        # إعداد قاعدة البيانات
                        def setup_database(app_config):
                            print("Starting database setup...")

                            # التأكد من وجود مجلد instance
                            instance_dir = os.path.dirname(app_config['DATABASE_PATH'])
                            if not os.path.exists(instance_dir):
                                os.makedirs(instance_dir)
                                print(f"Created directory: {instance_dir}")

                            # مسار قاعدة البيانات
                            db_path = app_config['DATABASE_PATH']

                            # قراءة ملف schema.sql
                            schema_path = os.path.join(application_path, 'schema.sql')
                            if not os.path.exists(schema_path):
                                print(f"Error: File {schema_path} not found!")

                                # البحث عن الملف في مجلدات مختلفة
                                possible_paths = [
                                    os.path.join(application_path, '_internal', 'schema.sql'),
                                    os.path.join(application_path, 'schema.sql'),
                                    'schema.sql'
                                ]

                                for path in possible_paths:
                                    print(f"Checking for schema at: {path}")
                                    if os.path.exists(path):
                                        schema_path = path
                                        print(f"Found schema at: {schema_path}")
                                        break

                                if not os.path.exists(schema_path):
                                    print("Could not find schema.sql in any location!")
                                    return False

                            try:
                                print(f"Reading schema from: {schema_path}")
                                with open(schema_path, 'r', encoding='utf-8') as f:
                                    schema_sql = f.read()

                                # إنشاء قاعدة البيانات وتنفيذ الاستعلامات
                                print(f"Creating database at: {db_path}")
                                conn = sqlite3.connect(db_path)
                                conn.executescript(schema_sql)

                                # إنشاء مستخدم افتراضي (مدير)
                                username = "admin"
                                password = "admin123"
                                name = "مدير النظام"
                                email = "<EMAIL>"
                                role = "admin"

                                # تشفير كلمة المرور
                                hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

                                # إدراج المستخدم في قاعدة البيانات
                                print("Creating default admin user")
                                conn.execute('''
                                INSERT INTO users (username, password, name, email, role, created_at)
                                VALUES (?, ?, ?, ?, ?, ?)
                                ''', (username, hashed_password, name, email, role, datetime.now()))

                                # حفظ التغييرات وإغلاق الاتصال
                                conn.commit()
                                conn.close()

                                print("Database setup completed successfully!")
                                print(f"Default user created: Username: {username}, Password: {password}")

                                # التأكد من وجود مجلد uploads
                                uploads_dir = app_config['UPLOAD_FOLDER']
                                print(f"Setting up uploads directory: {uploads_dir}")
                                if not os.path.exists(uploads_dir):
                                    os.makedirs(uploads_dir)
                                    # إنشاء المجلدات الفرعية
                                    subdirs = ['buildings', 'contracts', 'documents', 'owners', 'tenants', 'transactions']
                                    for subdir in subdirs:
                                        os.makedirs(os.path.join(uploads_dir, subdir), exist_ok=True)
                                    print(f"Created directory: {uploads_dir} with subdirectories")

                                return True
                            except Exception as e:
                                print(f"Error during database setup: {str(e)}")
                                traceback.print_exc()
                                return False

                        # إعداد قاعدة البيانات
                        setup_result = setup_database(app.config)
                        if not setup_result:
                            print("Database setup failed!")
                            self.is_server_running = False
                            return

                    # تشغيل الخادم
                    print(f"Starting Flask server on {host}:{port}")

                    try:
                        # تعديل دالة run لتعمل في خيط منفصل
                        from werkzeug.serving import make_server

                        # إنشاء خادم HTTP
                        print(f"Creating HTTP server on {host}:{port_num}")
                        self.http_server = make_server(host, port_num, app, threaded=True)
                        print("HTTP server created successfully")

                        # تشغيل الخادم
                        print("Starting HTTP server...")
                        self.http_server.serve_forever()
                    except Exception as server_error:
                        print(f"Error starting HTTP server: {str(server_error)}")
                        traceback.print_exc()
                        self.is_server_running = False

                except Exception as e:
                    print(f"Error in Flask server thread: {str(e)}")
                    traceback.print_exc()
                    # إرسال إشارة لإيقاف الخادم
                    self.is_server_running = False

            # إنشاء خيط جديد لتشغيل الخادم
            self.server_thread = threading.Thread(target=run_flask_server)
            self.server_thread.daemon = True  # جعل الخيط daemon لإيقافه عند إغلاق البرنامج
            self.server_thread.start()

            # تعيين معرف العملية (استخدام قيمة وهمية)
            self.process_id = 999999
            print(f"Server started in thread")

            # انتظار لحظة للتأكد من بدء تشغيل السيرفر
            import time
            time.sleep(2)

            # اعتبار العملية بدأت بنجاح
            process_started = True

            if process_started:
                self.is_server_running = True
                print(f"Server started successfully")

                # فتح المتصفح تلقائيًا
                url = f"http://{host}:{port}"
                if host == "0.0.0.0":
                    url = f"http://127.0.0.1:{port}"
                try:
                    webbrowser.open(url)
                except:
                    pass

                # تحديث واجهة المستخدم
                status_text = f"الخادم يعمل على {host}:{port}"
                if host == "127.0.0.1":
                    status_text = f"الخادم يعمل محليًا على المنفذ {port}"
                elif host == "0.0.0.0":
                    # الحصول على عنوان IP المحلي للجهاز لعرضه في حالة الخادم الشبكة
                    try:
                        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                        s.connect(("*******", 80))
                        local_ip = s.getsockname()[0]
                        s.close()
                        status_text = f"الخادم يعمل على الشبكة: {local_ip}:{port}"
                    except:
                        status_text = f"الخادم يعمل على الشبكة على المنفذ {port}"

                self.status_label.setText(status_text)
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.local_radio.setEnabled(False)
                self.network_radio.setEnabled(False)

                # إنشاء رابط الخادم
                protocol = "http"
                display_host = host
                if host == "0.0.0.0":
                    # الحصول على عنوان IP المحلي للجهاز
                    try:
                        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                        s.connect(("*******", 80))
                        display_host = s.getsockname()[0]
                        s.close()
                    except:
                        display_host = "localhost"

                self.server_url = f"{protocol}://{display_host}:{port}"

                # إضافة زر مشاركة إذا كان الخادم على الشبكة
                if host == "0.0.0.0" or (host != "127.0.0.1" and host != "localhost"):
                    # إنشاء زر المشاركة إذا لم يكن موجودًا
                    if not hasattr(self, 'share_button'):
                        self.share_button = QPushButton("مشاركة الخادم")
                        self.share_button.setFixedHeight(40)
                        self.share_button.clicked.connect(self.show_share_dialog)
                        control_layout = self.start_button.parent().layout()
                        control_layout.addWidget(self.share_button)
                    else:
                        self.share_button.setVisible(True)

                    self.share_button.setEnabled(True)

                    # عرض نافذة المشاركة تلقائيًا
                    QTimer.singleShot(500, self.show_share_dialog)

                QMessageBox.information(self, "تم التشغيل", f"تم تشغيل الخادم بنجاح على {host}:{port}")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في بدء تشغيل الخادم")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تشغيل الخادم: {str(e)}")

    def stop_server(self):
        """إيقاف خادم Flask"""
        if not self.is_server_running:
            return

        try:
            print(f"Stopping server...")

            # إيقاف خادم HTTP إذا كان موجودًا
            if hasattr(self, 'http_server') and self.http_server:
                print("Shutting down HTTP server...")
                try:
                    # إيقاف الخادم في خيط منفصل
                    import threading

                    def shutdown_server():
                        try:
                            self.http_server.shutdown()
                            print("HTTP server shutdown completed")
                        except Exception as e:
                            print(f"Error during HTTP server shutdown: {str(e)}")

                    # تشغيل دالة الإيقاف في خيط منفصل
                    shutdown_thread = threading.Thread(target=shutdown_server)
                    shutdown_thread.daemon = True
                    shutdown_thread.start()

                    # انتظار إنهاء الخيط
                    import time
                    time.sleep(1)

                    # تعيين الخادم إلى None
                    self.http_server = None
                except Exception as e:
                    print(f"Error shutting down HTTP server: {str(e)}")

            # إغلاق أي منافذ مفتوحة على المنفذ المحدد
            port = int(self.port_input.text())
            self.close_port(port)

            # تحديث حالة التطبيق
            self.server_stopped()
            QMessageBox.information(self, "تم الإيقاف", "تم إيقاف الخادم بنجاح")

        except Exception as e:
            print(f"Error stopping server: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إيقاف الخادم: {str(e)}")

            # في حالة الفشل، نحاول إعادة تعيين حالة التطبيق
            self.is_server_running = False
            self.process_id = 0
            self.status_label.setText("الخادم متوقف")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.update_server_type()  # لتحديث عنوان IP بناءً على نوع الخادم
            self.local_radio.setEnabled(True)
            self.network_radio.setEnabled(True)



    def close_port(self, port):
        """إغلاق المنفذ المحدد بالقوة"""
        try:
            # محاولة إغلاق المنفذ باستخدام أمر نظام التشغيل
            import subprocess
            import platform

            print(f"Attempting to close port {port}")

            # التحقق أولاً مما إذا كان المنفذ مشغولاً
            if self.is_port_available("127.0.0.1", port):
                print(f"Port {port} is already available, no need to close")
                return True

            if platform.system() == "Windows":
                # في نظام Windows - استخدام طريقة أكثر دقة
                # الحصول على PID للعملية التي تستخدم المنفذ
                cmd_find = f'netstat -ano | findstr :{port} | findstr LISTENING'
                result = subprocess.run(cmd_find, shell=True, capture_output=True, text=True)

                killed_any = False
                if result.stdout:
                    # استخراج PID من النتيجة
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            try:
                                pid_num = int(pid)
                                if pid_num > 0:  # تجنب إنهاء عمليات النظام
                                    print(f"Killing process with PID: {pid_num}")
                                    # استخدام عدة طرق لإنهاء العملية
                                    # الطريقة الأولى: taskkill
                                    kill_result = subprocess.run(f'taskkill /F /PID {pid_num}', shell=True, capture_output=True, text=True)
                                    print(f"Kill result (taskkill): {kill_result.stdout}")

                                    # انتظار لحظة
                                    import time
                                    time.sleep(0.5)

                                    # الطريقة الثانية: استخدام wmic
                                    kill_result2 = subprocess.run(f'wmic process where ProcessId={pid_num} delete', shell=True, capture_output=True, text=True)
                                    print(f"Kill result (wmic): {kill_result2.stdout}")
                                    killed_any = True
                            except ValueError:
                                pass

                # إذا لم يتم إنهاء أي عملية، نستخدم طريقة بديلة
                if not killed_any:
                    print("Using alternative method to close port")
                    # طريقة بديلة إذا فشلت الطريقة الأولى - استخدام cmd بدلاً من PowerShell
                    cmd_alt = f'cmd /c "for /f "tokens=5" %a in (\'netstat -aon ^| findstr :{port} ^| findstr LISTENING\') do taskkill /F /PID %a"'
                    subprocess.run(cmd_alt, shell=True)
            else:
                # في نظام Linux/Mac
                cmd = f"lsof -i :{port} | grep LISTEN | awk '{{print $2}}' | xargs -r kill -9"
                subprocess.run(cmd, shell=True)

            # انتظار لحظة للتأكد من إغلاق المنفذ
            import time
            time.sleep(1)

            # التحقق مما إذا تم تحرير المنفذ
            if self.is_port_available("127.0.0.1", port):
                print(f"Successfully closed port {port}")
                return True
            else:
                print(f"Failed to close port {port}")
                return False

        except Exception as e:
            print(f"Error closing port: {str(e)}")
            return False

    def server_stopped(self):
        """تحديث واجهة المستخدم عند توقف الخادم"""
        print("Server stopped function called")

        # تعيين متغيرات الحالة
        self.is_server_running = False
        self.process_id = 0  # استخدام 0 بدلاً من None
        self.server_url = None

        # تحديث واجهة المستخدم
        self.status_label.setText("الخادم متوقف")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.update_server_type()  # لتحديث عنوان IP بناءً على نوع الخادم
        self.local_radio.setEnabled(True)
        self.network_radio.setEnabled(True)

        # إخفاء زر المشاركة إذا كان موجودًا
        if hasattr(self, 'share_button'):
            self.share_button.setVisible(False)

        # إغلاق نافذة المشاركة إذا كانت مفتوحة
        if hasattr(self, 'qr_dialog') and self.qr_dialog and self.qr_dialog.isVisible():
            try:
                self.qr_dialog.close()
            except:
                pass

        # محاولة تحرير المنفذ
        try:
            port = int(self.port_input.text())
            print(f"Attempting to close port {port} from server_stopped function")
            self.close_port(port)
        except Exception as e:
            print(f"Error closing port from server_stopped: {str(e)}")
            pass

        # إيقاف خادم HTTP إذا كان موجودًا
        if hasattr(self, 'http_server') and self.http_server:
            try:
                print("Cleaning up HTTP server reference")
                self.http_server = None
            except:
                pass



    def is_port_available(self, host, port):
        """التحقق مما إذا كان المنفذ متاحًا"""
        try:
            # محاولة ربط المنفذ للتحقق من توفره
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)

            # محاولة الاتصال بالمنفذ
            result = sock.connect_ex((host, port))
            sock.close()

            # إذا كان المنفذ غير متاح، نحاول طريقة أخرى للتأكد
            if result == 0:
                print(f"Port {port} is in use (connect_ex returned 0)")

                # محاولة ربط المنفذ مباشرة
                try:
                    test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    test_socket.settimeout(1)
                    test_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                    test_socket.bind((host, port))
                    test_socket.close()
                    print(f"Port {port} is actually available (bind test succeeded)")
                    return True
                except socket.error:
                    print(f"Port {port} is confirmed to be in use (bind test failed)")
                    return False

            # إذا كان المنفذ متاح
            print(f"Port {port} is available (connect_ex returned {result})")
            return True
        except Exception as e:
            print(f"Error checking port availability: {str(e)}")
            # في حالة حدوث خطأ، نفترض أن المنفذ غير متاح
            return False

    def get_local_ip(self):
        """الحصول على عنوان IP المحلي للجهاز"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"

    def create_qr_code(self, url):
        """إنشاء رمز QR للرابط"""
        try:
            # إنشاء رمز QR
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(url)
            qr.make(fit=True)

            # تحويل رمز QR إلى صورة
            img = qr.make_image(fill_color="black", back_color="white")

            # تحويل الصورة إلى QPixmap
            buffer = BytesIO()
            img.save(buffer, format="PNG")
            buffer.seek(0)

            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())

            return pixmap
        except Exception as e:
            print(f"خطأ في إنشاء رمز QR: {str(e)}")
            return None

    def show_share_dialog(self):
        """عرض نافذة مشاركة الرابط"""
        # التأكد من أن الخادم يعمل
        if not self.is_server_running:
            QMessageBox.warning(self, "تنبيه", "الخادم غير مشغل حالياً. يرجى تشغيل الخادم أولاً.")
            return

        # الحصول على عنوان IP المحلي للجهاز
        local_ip = self.get_local_ip()
        if not local_ip:
            QMessageBox.warning(self, "خطأ", "تعذر الحصول على عنوان IP للجهاز. تأكد من اتصالك بالشبكة.")
            return

        # إنشاء رابط الخادم
        port = self.port_input.text()
        self.server_url = f"http://{local_ip}:{port}"

        # إنشاء نافذة الحوار
        dialog = QDialog(self)
        dialog.setWindowTitle("مشاركة الخادم")
        dialog.setMinimumSize(450, 550)

        # تخطيط النافذة
        layout = QVBoxLayout()

        # عنوان
        title_label = QLabel("مشاركة الخادم على الشبكة")
        title_font = QFont("Arial", 14, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # رابط الخادم الداخلي (الشبكة المحلية)
        url_group = QGroupBox("رابط الخادم على الشبكة المحلية")
        url_layout = QVBoxLayout()

        url_label = QLabel(self.server_url)
        url_label.setTextInteractionFlags(Qt.TextSelectableByMouse | Qt.TextSelectableByKeyboard)
        url_label.setAlignment(Qt.AlignCenter)
        url_font = QFont("Arial", 12)
        url_label.setFont(url_font)
        url_layout.addWidget(url_label)

        # زر فتح الرابط في المتصفح
        open_button = QPushButton("فتح في المتصفح")
        open_button.setFixedHeight(35)
        open_button.clicked.connect(lambda: webbrowser.open(self.server_url))
        url_layout.addWidget(open_button)

        url_group.setLayout(url_layout)
        layout.addWidget(url_group)

        # رمز QR
        qr_group = QGroupBox("رمز QR للمشاركة")
        qr_layout = QVBoxLayout()

        qr_pixmap = self.create_qr_code(self.server_url)
        if qr_pixmap:
            qr_label = QLabel()
            qr_label.setPixmap(qr_pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            qr_label.setAlignment(Qt.AlignCenter)
            qr_layout.addWidget(qr_label)

            qr_info = QLabel("امسح رمز QR للوصول إلى الخادم من أي جهاز على نفس الشبكة")
            qr_info.setAlignment(Qt.AlignCenter)
            qr_layout.addWidget(qr_info)
        else:
            error_label = QLabel("تعذر إنشاء رمز QR")
            error_label.setAlignment(Qt.AlignCenter)
            qr_layout.addWidget(error_label)

        qr_group.setLayout(qr_layout)
        layout.addWidget(qr_group)

        # تعليمات
        instructions_group = QGroupBox("تعليمات المشاركة")
        instructions_layout = QVBoxLayout()

        instructions = QLabel(
            "1. تأكد من أن جميع الأجهزة متصلة بنفس الشبكة.\n"
            "2. قد تحتاج إلى السماح للتطبيق في جدار الحماية.\n"
            "3. يمكن الوصول إلى الخادم فقط عندما يكون قيد التشغيل.\n"
            "4. إذا واجهت مشكلة في الاتصال، تأكد من إعدادات الشبكة."
        )
        instructions.setAlignment(Qt.AlignRight)
        instructions_layout.addWidget(instructions)

        # إضافة معلومات الشبكة
        network_info = QLabel(f"معلومات الشبكة: عنوان IP المحلي: {local_ip}")
        network_info.setAlignment(Qt.AlignCenter)
        instructions_layout.addWidget(network_info)

        instructions_group.setLayout(instructions_layout)
        layout.addWidget(instructions_group)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.setFixedHeight(35)
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)

        dialog.setLayout(layout)

        # حفظ مرجع للنافذة
        self.qr_dialog = dialog

        # عرض النافذة
        dialog.exec_()

    def get_local_ip(self):
        """الحصول على عنوان IP المحلي للجهاز"""
        try:
            # إنشاء اتصال مؤقت للحصول على عنوان IP المحلي
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            # في حالة الفشل، محاولة طريقة بديلة
            try:
                # الحصول على اسم المضيف وعنوان IP المرتبط به
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                if local_ip.startswith("127."):
                    # البحث عن عنوان IP غير محلي
                    for ip in socket.gethostbyname_ex(hostname)[2]:
                        if not ip.startswith("127."):
                            return ip
                return local_ip
            except:
                return None

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        if self.is_server_running:
            reply = QMessageBox.question(
                self, 'تأكيد الخروج',
                "الخادم لا يزال قيد التشغيل. هل تريد إيقافه والخروج؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    # محاولة إيقاف الخادم
                    self.stop_server()

                    # انتظار لحظة للتأكد من إغلاق الخادم
                    import time
                    time.sleep(1)

                    # التأكد من إغلاق جميع المنافذ
                    try:
                        port = int(self.port_input.text())
                        self.close_port(port)
                    except:
                        pass

                    # قبول حدث الإغلاق
                    event.accept()
                except Exception as e:
                    print(f"Error during application close: {str(e)}")
                    # في حالة الفشل، نقبل الإغلاق على أي حال
                    event.accept()
            else:
                # المستخدم اختار عدم إيقاف الخادم
                event.ignore()
        else:
            # الخادم غير مشغل، يمكن الإغلاق مباشرة
            event.accept()


def reset_admin_password():
    """إعادة تعيين كلمة المرور الافتراضية للمستخدم الأدمن"""
    try:
        print("Resetting admin password...")

        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(application_path, 'instance', 'realestate.db')

        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            print(f"Database not found at: {db_path}")

            # إنشاء مجلد instance إذا لم يكن موجودًا
            instance_dir = os.path.dirname(db_path)
            if not os.path.exists(instance_dir):
                os.makedirs(instance_dir)
                print(f"Created directory: {instance_dir}")

            # البحث عن ملف schema.sql
            schema_path = os.path.join(application_path, 'schema.sql')
            if not os.path.exists(schema_path):
                print(f"Error: File {schema_path} not found!")
                return False

            # إنشاء قاعدة البيانات
            print(f"Creating new database at: {db_path}")
            import sqlite3
            conn = sqlite3.connect(db_path)

            # قراءة ملف schema.sql
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_sql = f.read()

            # تنفيذ استعلامات إنشاء قاعدة البيانات
            conn.executescript(schema_sql)
            conn.commit()
            print("Database created successfully")

        # الاتصال بقاعدة البيانات
        print(f"Connecting to database: {db_path}")
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("Error: Users table not found in database")
            return False

        # تحديد بيانات المستخدم الأدمن
        username = "admin"
        password = "admin123"
        name = "مدير النظام"
        email = "<EMAIL>"
        role = "admin"

        # تشفير كلمة المرور
        import bcrypt
        from datetime import datetime
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        # التحقق من وجود المستخدم الأدمن
        cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
        admin_user = cursor.fetchone()

        if admin_user:
            # تحديث كلمة المرور للمستخدم الموجود
            admin_id = admin_user[0]
            cursor.execute(
                "UPDATE users SET password = ?, name = ?, email = ?, role = ? WHERE id = ?",
                (hashed_password, name, email, role, admin_id)
            )
            conn.commit()
            print(f"Updated password for user: {username}")
        else:
            # إنشاء مستخدم أدمن جديد
            cursor.execute(
                "INSERT INTO users (username, password, name, email, role, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                (username, hashed_password, name, email, role, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            )
            conn.commit()
            print(f"Created new admin user: {username}")

        # إغلاق الاتصال بقاعدة البيانات
        conn.close()

        print("\nAdmin password reset successfully!")
        print(f"Username: {username}")
        print(f"Password: {password}")

        return True
    except Exception as e:
        print(f"Error resetting admin password: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == '__main__':
    # معالجة المعلمات في سطر الأوامر
    import argparse
    parser = argparse.ArgumentParser(description='Real Estate Management System')
    parser.add_argument('--reset-admin-password', action='store_true', help='Reset admin password to default')
    args = parser.parse_args()

    # إذا تم تمرير معلمة إعادة تعيين كلمة المرور
    if args.reset_admin_password:
        reset_admin_password()
        sys.exit(0)

    # تشغيل التطبيق الرئيسي
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار للغة العربية

    window = ServerManager()
    window.show()

    sys.exit(app.exec_())
