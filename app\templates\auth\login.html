{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة العقارات{% endblock %}

{% block styles %}
<style>
    .login-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        margin-top: 100px;
    }
    .login-logo {
        text-align: center;
        margin-bottom: 30px;
    }
    .login-logo i {
        font-size: 50px;
        color: #0d6efd;
    }
    .login-title {
        text-align: center;
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-logo">
        <i class="fas fa-building"></i>
    </div>
    <h2 class="login-title">تسجيل الدخول</h2>
    
    <form method="POST" action="{{ url_for('auth.login') }}">
        {{ form.hidden_tag() }}
        
        <div class="mb-3">
            {{ form.username.label(class="form-label") }}
            {% if form.username.errors %}
                {{ form.username(class="form-control is-invalid") }}
                <div class="invalid-feedback">
                    {% for error in form.username.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% else %}
                {{ form.username(class="form-control") }}
            {% endif %}
        </div>
        
        <div class="mb-3">
            {{ form.password.label(class="form-label") }}
            {% if form.password.errors %}
                {{ form.password(class="form-control is-invalid") }}
                <div class="invalid-feedback">
                    {% for error in form.password.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% else %}
                {{ form.password(class="form-control") }}
            {% endif %}
        </div>
        
        <div class="d-grid gap-2">
            {{ form.submit(class="btn btn-primary") }}
        </div>
    </form>
</div>
{% endblock %}
