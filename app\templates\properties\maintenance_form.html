{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.unit_id.label(class="form-label") }}
                        {% if form.unit_id.errors %}
                            {{ form.unit_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.unit_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.unit_id(class="form-select") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {% if form.description.errors %}
                            {{ form.description(class="form-control is-invalid", rows=4) }}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.description(class="form-control", rows=4) }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.priority.label(class="form-label") }}
                            {% if form.priority.errors %}
                                {{ form.priority(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.priority.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.priority(class="form-select") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {% if form.status.errors %}
                                {{ form.status(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.status(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.request_date.label(class="form-label") }}
                            {% if form.request_date.errors %}
                                {{ form.request_date(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.request_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.request_date(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.completion_date.label(class="form-label") }}
                            {% if form.completion_date.errors %}
                                {{ form.completion_date(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.completion_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.completion_date(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.cost.label(class="form-label") }}
                        {% if form.cost.errors %}
                            {{ form.cost(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.cost.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.cost(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('properties.units') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
