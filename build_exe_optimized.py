#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إنشاء ملف EXE محسن - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_exe.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('build_exe')

class EXEBuilder:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.dist_dir = self.base_dir / 'dist'
        self.build_dir = self.base_dir / 'build'
        
    def clean_previous_builds(self):
        """تنظيف البناءات السابقة"""
        logger.info("🧹 تنظيف البناءات السابقة...")
        
        # حذف مجلدات البناء
        for directory in [self.dist_dir, self.build_dir]:
            if directory.exists():
                shutil.rmtree(directory)
                logger.info(f"تم حذف: {directory}")
        
        # حذف ملفات spec
        for spec_file in self.base_dir.glob("*.spec"):
            spec_file.unlink()
            logger.info(f"تم حذف: {spec_file}")
    
    def check_requirements(self):
        """التحقق من المتطلبات"""
        logger.info("🔍 التحقق من المتطلبات...")
        
        # التحقق من Python
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            raise Exception("يتطلب Python 3.8 أو أحدث")
        logger.info(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # التحقق من PyInstaller
        try:
            import PyInstaller
            logger.info(f"✅ PyInstaller {PyInstaller.__version__}")
        except ImportError:
            logger.info("📦 تثبيت PyInstaller...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            logger.info("✅ تم تثبيت PyInstaller")
        
        # التحقق من المتطلبات الأخرى
        requirements_file = self.base_dir / 'requirements.txt'
        if requirements_file.exists():
            logger.info("📦 تثبيت المتطلبات...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
            ])
            logger.info("✅ تم تثبيت المتطلبات")
    
    def create_spec_file(self):
        """إنشاء ملف spec محسن"""
        logger.info("📝 إنشاء ملف spec...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# المسار الأساسي
base_dir = Path(r'{self.base_dir}')

block_cipher = None

# تحديد الملفات والمجلدات
a = Analysis(
    ['server_manager.py'],
    pathex=[str(base_dir)],
    binaries=[],
    datas=[
        (str(base_dir / 'app'), 'app'),
        (str(base_dir / 'backend'), 'backend'),
        (str(base_dir / 'config.py'), '.'),
        (str(base_dir / 'schema.sql'), '.'),
        (str(base_dir / 'file.ico'), '.'),
        (str(base_dir / 'file.jpeg'), '.'),
        (str(base_dir / 'requirements.txt'), '.'),
    ],
    hiddenimports=[
        # Flask والمكتبات الأساسية
        'flask', 'flask.app', 'flask.blueprints', 'flask.cli', 'flask.config',
        'flask.ctx', 'flask.globals', 'flask.helpers', 'flask.json', 'flask.logging',
        'flask.sessions', 'flask.signals', 'flask.templating', 'flask.testing',
        'flask.views', 'flask.wrappers',
        
        # Flask Extensions
        'flask_login', 'flask_bcrypt', 'flask_wtf', 'flask_wtf.csrf', 'flask_mail',
        
        # Werkzeug
        'werkzeug', 'werkzeug.serving', 'werkzeug.urls', 'werkzeug.utils',
        'werkzeug.wrappers', 'werkzeug.exceptions', 'werkzeug.routing',
        'werkzeug.security', 'werkzeug.datastructures', 'werkzeug.http',
        'werkzeug.local', 'werkzeug.middleware',
        
        # Jinja2
        'jinja2', 'jinja2.environment', 'jinja2.loaders', 'jinja2.runtime',
        'jinja2.utils', 'jinja2.filters', 'jinja2.tests', 'jinja2.ext',
        
        # WTForms
        'wtforms', 'wtforms.fields', 'wtforms.form', 'wtforms.validators',
        'wtforms.widgets', 'wtforms.csrf',
        
        # PyQt5
        'PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets',
        
        # مكتبات أخرى
        'email_validator', 'itsdangerous', 'markupsafe', 'bcrypt', 'sqlite3',
        'qrcode', 'PIL', 'PIL.Image', 'PIL.ImageDraw', 'PIL.ImageFont',
        
        # مكتبات التطبيق
        'app', 'app.auth', 'app.dashboard', 'app.owners', 'app.properties',
        'app.tenants', 'app.documents', 'app.finance', 'app.reports',
        'app.db', 'app.models', 'app.forms', 'app.utils', 'app.decorators',
        'backend', 'backend.app',
        
        # مكتبات النظام
        'datetime', 'os', 'sys', 'json', 'base64', 'hashlib', 'secrets',
        'uuid', 'pathlib', 'shutil', 'tempfile', 'io', 'collections',
        'functools', 'itertools', 're', 'string', 'time', 'calendar',
        'decimal', 'math', 'random', 'platform', 'signal', 'argparse',
        'configparser', 'csv', 'zipfile', 'codecs', 'locale', 'gettext',
        'unicodedata', 'encodings', 'encodings.utf_8', 'encodings.cp1256',
        'encodings.ascii', 'encodings.latin1', 'urllib', 'urllib.parse',
        'socket', 'threading', 'subprocess', 'webbrowser', 'logging',
        'traceback', 'importlib', 'importlib.util',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy', 'IPython',
        'jupyter', 'notebook', 'pytest', 'setuptools', 'distutils',
        'pip', 'wheel', 'pkg_resources', 'test', 'tests', 'unittest',
        'doctest', 'pdb', 'pydoc', 'xmlrpc', 'xml.etree', 'xml.dom',
        'xml.sax', 'html.parser', 'http.server', 'http.client',
        'urllib.robotparser', 'urllib.request', 'urllib.error',
        'ftplib', 'poplib', 'imaplib', 'nntplib', 'smtplib', 'telnetlib',
        'pickle', 'shelve', 'dbm', 'sqlite3.dump', 'multiprocessing',
        'concurrent', 'asyncio', 'queue', 'sched', 'dummy_threading',
        '_thread', 'pty', 'tty', 'pipes', 'posix', 'pwd', 'spwd',
        'grp', 'crypt', 'termios', 'resource', 'nis', 'syslog',
        'commands', 'dl', 'DLFCN', 'ossaudiodev', 'audioop', 'imageop',
        'aifc', 'sunau', 'wave', 'chunk', 'colorsys', 'imghdr',
        'sndhdr', 'turtle', 'cmd', 'shlex',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='مكتب_عصام_الفت',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(base_dir / 'file.ico') if (base_dir / 'file.ico').exists() else None,
    version='version_info.txt' if Path('version_info.txt').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=True,
    upx=False,
    upx_exclude=[],
    name='مكتب_عصام_الفت',
)
'''
        
        spec_file = self.base_dir / 'مكتب_عصام_الفت_محسن.spec'
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        logger.info(f"✅ تم إنشاء ملف spec: {spec_file}")
        return spec_file
    
    def create_version_info(self):
        """إنشاء ملف معلومات الإصدار"""
        logger.info("📋 إنشاء ملف معلومات الإصدار...")
        
        version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'شركة المبرمج المصري'),
        StringStruct(u'FileDescription', u'مكتب عصام الفت لإدارة الأملاك'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'مكتب_عصام_الفت'),
        StringStruct(u'LegalCopyright', u'جميع الحقوق محفوظة © 2025'),
        StringStruct(u'OriginalFilename', u'مكتب_عصام_الفت.exe'),
        StringStruct(u'ProductName', u'مكتب عصام الفت لإدارة الأملاك'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
        
        version_file = self.base_dir / 'version_info.txt'
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        logger.info(f"✅ تم إنشاء ملف معلومات الإصدار: {version_file}")
    
    def build_exe(self):
        """بناء الملف التنفيذي"""
        logger.info("🔨 بناء الملف التنفيذي...")
        
        # إنشاء ملف spec
        spec_file = self.create_spec_file()
        
        # إنشاء ملف معلومات الإصدار
        self.create_version_info()
        
        # تشغيل PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            '--log-level=WARN',
            str(spec_file)
        ]
        
        logger.info(f"تشغيل الأمر: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True)
        end_time = time.time()
        
        if result.returncode == 0:
            logger.info(f"✅ تم بناء الملف التنفيذي بنجاح في {end_time - start_time:.1f} ثانية")
            return True
        else:
            logger.error("❌ فشل في بناء الملف التنفيذي")
            logger.error(f"خطأ: {result.stderr}")
            return False
    
    def setup_exe_environment(self):
        """إعداد بيئة الملف التنفيذي"""
        logger.info("⚙️ إعداد بيئة الملف التنفيذي...")
        
        exe_dir = self.dist_dir / 'مكتب_عصام_الفت'
        if not exe_dir.exists():
            logger.error("❌ مجلد الملف التنفيذي غير موجود")
            return False
        
        # إنشاء المجلدات المطلوبة
        required_dirs = [
            'instance', 'uploads', 'logs',
            'uploads/buildings', 'uploads/contracts', 'uploads/documents',
            'uploads/owners', 'uploads/tenants', 'uploads/transactions'
        ]
        
        for dir_name in required_dirs:
            dir_path = exe_dir / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ تم إنشاء المجلد: {dir_name}")
        
        # إنشاء ملف تشغيل
        run_script = exe_dir / 'تشغيل_النظام.bat'
        with open(run_script, 'w', encoding='utf-8') as f:
            f.write('''@echo off
chcp 65001 >nul
title مكتب عصام الفت لإدارة الأملاك

echo ================================================================
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.
echo بدء تشغيل النظام...
echo.

cd /d "%~dp0"
"مكتب_عصام_الفت.exe"

if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ أثناء تشغيل النظام
    echo يرجى التأكد من:
    echo 1. عدم تشغيل نسخة أخرى من النظام
    echo 2. توفر المنفذ 5000
    echo 3. عدم حجب البرنامج من Windows Defender
    echo.
    pause
)
''')
        
        logger.info(f"✅ تم إنشاء ملف التشغيل: {run_script}")
        
        # إنشاء ملف README
        readme_file = exe_dir / 'اقرأني.txt'
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write('''مكتب عصام الفت لإدارة الأملاك
================================

طريقة التشغيل:
1. شغل ملف "تشغيل_النظام.bat"
2. أو شغل "مكتب_عصام_الفت.exe" مباشرة

متطلبات النظام:
- Windows 7 أو أحدث
- 4 جيجابايت رام على الأقل
- 500 ميجابايت مساحة فارغة

الدعم الفني:
- واتساب: 0201032540807
- فيسبوك: https://www.facebook.com/almbarmg

جميع الحقوق محفوظة © 2025 - شركة المبرمج المصري
''')
        
        logger.info(f"✅ تم إنشاء ملف README: {readme_file}")
        return True
    
    def build(self):
        """البناء الكامل"""
        try:
            logger.info("🚀 بدء عملية إنشاء الملف التنفيذي...")
            
            # تنظيف البناءات السابقة
            self.clean_previous_builds()
            
            # التحقق من المتطلبات
            self.check_requirements()
            
            # بناء الملف التنفيذي
            if not self.build_exe():
                return False
            
            # إعداد بيئة الملف التنفيذي
            if not self.setup_exe_environment():
                return False
            
            # النتيجة النهائية
            exe_path = self.dist_dir / 'مكتب_عصام_الفت' / 'مكتب_عصام_الفت.exe'
            if exe_path.exists():
                logger.info("🎉 تم إنشاء الملف التنفيذي بنجاح!")
                logger.info(f"📁 المجلد: {exe_path.parent}")
                logger.info(f"🚀 الملف التنفيذي: {exe_path}")
                logger.info("📋 يمكنك نسخ المجلد إلى أي جهاز Windows وتشغيله")
                return True
            else:
                logger.error("❌ لم يتم العثور على الملف التنفيذي")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ أثناء البناء: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("إنشاء ملف EXE محسن - مكتب عصام الفت لإدارة الأملاك")
    print("=" * 60)
    
    builder = EXEBuilder()
    success = builder.build()
    
    if success:
        print("\n🎉 تم إنشاء الملف التنفيذي بنجاح!")
        print("📁 يمكنك العثور على الملف في مجلد dist/")
    else:
        print("\n❌ فشل في إنشاء الملف التنفيذي")
        print("📋 راجع ملف build_exe.log للتفاصيل")
    
    input("\nاضغط Enter للخروج...")
    return success

if __name__ == '__main__':
    main()
