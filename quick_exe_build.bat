@echo off
chcp 65001 >nul
title إنشاء EXE سريع - مكتب عصام الفت

echo ================================================================
echo                    إنشاء EXE سريع ومحسن
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

cd /d "%~dp0"

echo 🚀 بدء العملية السريعة...
echo.

REM الخطوة 1: التحقق من Python
echo 1️⃣ التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM الخطوة 2: تثبيت المتطلبات
echo.
echo 2️⃣ تثبيت المتطلبات...
python -m pip install --upgrade pip >nul 2>&1
python -m pip install pyinstaller >nul 2>&1
python -m pip install -r requirements.txt >nul 2>&1
echo ✅ تم تثبيت المتطلبات

REM الخطوة 3: إعداد Windows Defender
echo.
echo 3️⃣ إعداد Windows Defender...
python windows_defender_config.py >nul 2>&1
echo ✅ تم إعداد Windows Defender

REM الخطوة 4: تنظيف البناءات السابقة
echo.
echo 4️⃣ تنظيف البناءات السابقة...
if exist dist rmdir /s /q dist >nul 2>&1
if exist build rmdir /s /q build >nul 2>&1
if exist *.spec del *.spec >nul 2>&1
echo ✅ تم التنظيف

REM الخطوة 5: إنشاء EXE
echo.
echo 5️⃣ إنشاء ملف EXE...
echo ⏳ جاري البناء... (قد يستغرق 3-5 دقائق)

python build_exe_optimized.py

if %errorlevel% equ 0 (
    echo.
    echo 🎉 تم إنشاء ملف EXE بنجاح!
    echo.
    echo 📁 المجلد: dist\مكتب_عصام_الفت\
    echo 🚀 الملف: مكتب_عصام_الفت.exe
    echo 📋 التشغيل: تشغيل_النظام.bat
    echo.
    echo 🎯 الميزات:
    echo   ✓ مستقل تماماً (لا يحتاج Python)
    echo   ✓ محمي من Windows Defender
    echo   ✓ دعم كامل للعربية
    echo   ✓ حجم محسن وأداء سريع
    echo.
    
    REM اختبار سريع
    echo 6️⃣ اختبار سريع...
    if exist "dist\مكتب_عصام_الفت\مكتب_عصام_الفت.exe" (
        echo ✅ الملف التنفيذي موجود
        
        REM فتح المجلد
        set /p open_folder="هل تريد فتح مجلد النتيجة؟ (y/n): "
        if /i "%open_folder%"=="y" (
            explorer "dist\مكتب_عصام_الفت"
        )
        
        REM تشغيل تجريبي
        set /p test_run="هل تريد تشغيل الملف التنفيذي للاختبار؟ (y/n): "
        if /i "%test_run%"=="y" (
            echo.
            echo 🧪 تشغيل تجريبي...
            cd "dist\مكتب_عصام_الفت"
            start "مكتب عصام الفت" "مكتب_عصام_الفت.exe"
            cd ..\..
            echo ✅ تم بدء التشغيل التجريبي
        )
    ) else (
        echo ❌ الملف التنفيذي غير موجود
    )
    
) else (
    echo.
    echo ❌ فشل في إنشاء ملف EXE
    echo.
    echo 💡 حلول مقترحة:
    echo 1. شغل Command Prompt كمدير
    echo 2. أغلق برامج مكافحة الفيروسات مؤقتاً
    echo 3. تأكد من وجود مساحة كافية (1 جيجا على الأقل)
    echo 4. جرب الطريقة اليدوية: build_exe_ultimate.bat
    echo.
    echo 📞 للدعم: واتساب 0201032540807
)

echo.
echo ================================================================
echo                        انتهت العملية
echo ================================================================
echo.
echo 📋 ملخص النتائج:
if exist "dist\مكتب_عصام_الفت\مكتب_عصام_الفت.exe" (
    echo ✅ تم إنشاء ملف EXE بنجاح
    echo 📁 المجلد: dist\مكتب_عصام_الفت\
    echo 🚀 جاهز للتوزيع والاستخدام
) else (
    echo ❌ لم يتم إنشاء ملف EXE
    echo 📞 تواصل مع الدعم الفني
)
echo.

pause
