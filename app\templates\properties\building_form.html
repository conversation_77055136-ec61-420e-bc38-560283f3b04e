{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            {{ form.name.label(class="form-label") }}
                            {% if form.name.errors %}
                                {{ form.name(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.name(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.building_code.label(class="form-label") }}
                            {% if form.building_code.errors %}
                                {{ form.building_code(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.building_code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.building_code(class="form-control", placeholder="1001") }}
                                <div class="form-text text-muted">
                                    رقم فريد للعقار (يبدأ من 1001)
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.owner_id.label(class="form-label") }}
                        {% if form.owner_id.errors %}
                            {{ form.owner_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.owner_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.owner_id(class="form-select", onchange="fetchOwnerDetails(this.value)") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.agent.label(class="form-label") }}
                        {% if form.agent.errors %}
                            {{ form.agent(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.agent.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.agent(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.type.label(class="form-label") }}
                            {% if form.type.errors %}
                                {{ form.type(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.type(class="form-control", list="buildingTypeOptions") }}
                                <datalist id="buildingTypeOptions">
                                    <option value="سكني">
                                    <option value="تجاري">
                                    <option value="صناعي">
                                    <option value="زراعي">
                                    <option value="مختلط">
                                </datalist>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.usage.label(class="form-label") }}
                            {% if form.usage.errors %}
                                {{ form.usage(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.usage.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.usage(class="form-control", list="buildingUsageOptions") }}
                                <datalist id="buildingUsageOptions">
                                    <option value="سكن">
                                    <option value="مكاتب">
                                    <option value="محلات">
                                    <option value="مستودعات">
                                    <option value="مصانع">
                                </datalist>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {% if form.address.errors %}
                            {{ form.address(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.address(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.district.label(class="form-label") }}
                            {% if form.district.errors %}
                                {{ form.district(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.district.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.district(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.city.label(class="form-label") }}
                            {% if form.city.errors %}
                                {{ form.city(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.city(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <h4 class="mt-4 mb-3">معلومات الصك</h4>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.deed_number.label(class="form-label") }}
                            {% if form.deed_number.errors %}
                                {{ form.deed_number(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.deed_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.deed_number(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.deed_date.label(class="form-label") }}
                            {% if form.deed_date.errors %}
                                {{ form.deed_date(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.deed_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.deed_date(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.deed_type.label(class="form-label") }}
                            {% if form.deed_type.errors %}
                                {{ form.deed_type(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.deed_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.deed_type(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.deed_issuer.label(class="form-label") }}
                            {% if form.deed_issuer.errors %}
                                {{ form.deed_issuer(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.deed_issuer.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.deed_issuer(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <h4 class="mt-4 mb-3">معلومات العنوان</h4>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.building_number.label(class="form-label") }}
                            {% if form.building_number.errors %}
                                {{ form.building_number(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.building_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.building_number(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.additional_number.label(class="form-label") }}
                            {% if form.additional_number.errors %}
                                {{ form.additional_number(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.additional_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.additional_number(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.postal_code.label(class="form-label") }}
                        {% if form.postal_code.errors %}
                            {{ form.postal_code(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.postal_code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.postal_code(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.old_file.label(class="form-label") }}
                        {% if form.old_file.errors %}
                            {{ form.old_file(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.old_file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.old_file(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.build_year.label(class="form-label") }}
                            {% if form.build_year.errors %}
                                {{ form.build_year(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.build_year.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.build_year(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.floors_count.label(class="form-label") }}
                            {% if form.floors_count.errors %}
                                {{ form.floors_count(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.floors_count.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.floors_count(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('properties.buildings') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function fetchOwnerDetails(ownerId) {
        if (!ownerId) return;

        fetch(`/owners/api/get_owner_details/${ownerId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل في جلب بيانات المالك');
                }
                return response.json();
            })
            .then(data => {
                // تعبئة رقم الهوية تلقائيًا
                if (data.id_number) {
                    document.getElementById('deed_issuer').value = data.id_number;
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
            });
    }
</script>
{% endblock %}