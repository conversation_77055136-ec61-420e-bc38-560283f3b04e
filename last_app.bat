@echo off
chcp 65001 >nul
title البناء النهائي الشامل - مكتب عصام الفت

echo ================================================================
echo                    البناء النهائي الشامل
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

cd /d "%~dp0"

echo 🚀 بدء البناء النهائي الشامل...
echo.

REM التحقق من وجود Python
echo 1️⃣ التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✅ Python %%i متوفر
)

REM التحقق من وجود ملف البناء النهائي
echo.
echo 2️⃣ التحقق من ملف البناء النهائي...
if not exist "last_app.py" (
    echo ❌ ملف last_app.py غير موجود!
    echo تأكد من وجود ملف البناء النهائي في نفس المجلد
    echo.
    pause
    exit /b 1
) else (
    echo ✅ ملف البناء النهائي موجود
)

REM التحقق من الملفات الأساسية
echo.
echo 3️⃣ التحقق من الملفات الأساسية...

set missing_files=0

if not exist "server_manager.py" (
    echo ❌ ملف server_manager.py غير موجود
    set missing_files=1
) else (
    echo ✅ server_manager.py موجود
)

if not exist "app" (
    echo ❌ مجلد app غير موجود
    set missing_files=1
) else (
    echo ✅ مجلد app موجود
)

if not exist "config.py" (
    echo ❌ ملف config.py غير موجود
    set missing_files=1
) else (
    echo ✅ config.py موجود
)

if not exist "schema.sql" (
    echo ❌ ملف schema.sql غير موجود
    set missing_files=1
) else (
    echo ✅ schema.sql موجود
)

if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    set missing_files=1
) else (
    echo ✅ requirements.txt موجود
)

if %missing_files%==1 (
    echo.
    echo ❌ ملفات أساسية مفقودة! لا يمكن المتابعة
    echo تأكد من وجود جميع ملفات المشروع
    echo.
    pause
    exit /b 1
)

echo ✅ جميع الملفات الأساسية موجودة

REM فحص المساحة المتاحة
echo.
echo 4️⃣ فحص المساحة المتاحة...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set free_space=%%a
echo ✅ مساحة متاحة كافية

REM عرض تحذير
echo.
echo ⚠️  تحذير مهم:
echo ════════════════════════════════════════════════════════════════
echo • هذا البناء النهائي الشامل سيستغرق وقتاً طويلاً (10-30 دقيقة)
echo • سيتم إنشاء ملف EXE مستقل مع جميع الملفات المطلوبة
echo • سيتم إنشاء حزمة ZIP جاهزة للتوزيع
echo • تأكد من عدم إغلاق النافذة أثناء البناء
echo • تأكد من وجود اتصال بالإنترنت لتحميل التبعيات
echo ════════════════════════════════════════════════════════════════
echo.

set /p confirm="هل تريد المتابعة؟ (y/n): "
if /i not "%confirm%"=="y" (
    echo تم إلغاء البناء
    pause
    exit /b 0
)

echo.
echo 🔨 بدء البناء النهائي الشامل...
echo ⏳ يرجى الانتظار... (قد يستغرق 10-30 دقيقة)
echo.

REM تسجيل وقت البداية
echo بدء البناء في: %date% %time%
echo.

REM تشغيل البناء النهائي
python last_app.py

REM فحص نتيجة البناء
if %errorlevel% equ 0 (
    echo.
    echo ================================================================
    echo                        نجح البناء! 🎉
    echo ================================================================
    echo.
    echo ✅ تم إنشاء التطبيق النهائي بنجاح!
    echo.
    echo 📁 ابحث عن مجلد يبدأ بـ "final_build_" في المجلد الحالي
    echo 🚀 ستجد بداخله:
    echo    • مجلد التطبيق الكامل
    echo    • ملف ZIP جاهز للتوزيع
    echo    • تقرير البناء المفصل
    echo    • ملفات السجل
    echo.
    echo 📋 محتويات التطبيق:
    echo    • مكتب_عصام_الفت.exe - الملف التنفيذي
    echo    • تشغيل_النظام.bat - ملف التشغيل المساعد
    echo    • اقرأني.txt - دليل الاستخدام
    echo    • معلومات_البناء.json - معلومات تقنية
    echo    • مجلدات البيانات والرفع
    echo.
    echo 🎯 الميزات المحققة:
    echo    ✓ ملف EXE مستقل تماماً
    echo    ✓ لا يحتاج Python أو أي مكتبة
    echo    ✓ حماية من Windows Defender
    echo    ✓ دعم كامل للأحرف العربية
    echo    ✓ إعداد تلقائي للمجلدات
    echo    ✓ ملفات مساعدة شاملة
    echo    ✓ حزمة ZIP جاهزة للتوزيع
    echo.
    
    REM البحث عن مجلد البناء النهائي
    for /d %%i in (final_build_*) do (
        echo 📂 مجلد البناء النهائي: %%i
        set final_dir=%%i
    )
    
    REM عرض خيار فتح المجلد
    set /p open_folder="هل تريد فتح مجلد البناء النهائي؟ (y/n): "
    if /i "%open_folder%"=="y" (
        if defined final_dir (
            explorer "%final_dir%"
        ) else (
            echo لم يتم العثور على مجلد البناء النهائي
        )
    )
    
    echo.
    echo 📞 للدعم الفني:
    echo    واتساب: 0201032540807
    echo    فيسبوك: https://www.facebook.com/almbarmg
    echo.
    
) else (
    echo.
    echo ================================================================
    echo                        فشل البناء! ❌
    echo ================================================================
    echo.
    echo ❌ حدث خطأ أثناء البناء النهائي
    echo.
    echo 💡 حلول مقترحة:
    echo 1. تأكد من تشغيل Command Prompt كمدير
    echo 2. أغلق جميع برامج مكافحة الفيروسات مؤقتاً
    echo 3. تأكد من وجود اتصال مستقر بالإنترنت
    echo 4. تأكد من وجود مساحة كافية (2 جيجا على الأقل)
    echo 5. تأكد من عدم استخدام ملفات المشروع من برامج أخرى
    echo.
    echo 📋 للمساعدة في حل المشكلة:
    echo • راجع ملفات السجل في مجلد build_logs
    echo • أرسل رسائل الخطأ للدعم الفني
    echo • تأكد من إصدار Python (يجب أن يكون 3.8 أو أحدث)
    echo.
    echo 📞 الدعم الفني:
    echo    واتساب: 0201032540807
    echo    فيسبوك: https://www.facebook.com/almbarmg
    echo.
    echo عند طلب الدعم، يرجى إرفاق:
    echo • ملفات السجل من مجلد build_logs
    echo • لقطة شاشة من رسائل الخطأ
    echo • معلومات نظام التشغيل
    echo.
)

echo انتهى البناء في: %date% %time%
echo.
echo ================================================================
echo                        انتهت العملية
echo ================================================================

pause
