{% extends "base.html" %}

{% block title %}إدارة الوحدات - نظام إدارة العقارات{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* تحسينات عامة */
    .card {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .card-header {
        border-top-left-radius: 0.5rem !important;
        border-top-right-radius: 0.5rem !important;
    }

    /* تنسيق الجدول */
    .table th {
        font-weight: 600;
        white-space: nowrap;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    /* تنسيق البطاقات */
    .bg-opacity-10 {
        --bs-bg-opacity: 0.1;
    }

    .rounded-circle {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 767.98px) {
        .card-title {
            font-size: 1.1rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .mobile-action-buttons .dropdown-menu {
            min-width: 200px;
        }

        .list-group-item {
            padding: 0.75rem;
        }
    }

    /* تنسيق للشاشات المتوسطة والكبيرة */
    @media (min-width: 768px) {
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table th, .table td {
            vertical-align: middle;
        }
    }

    /* تأثيرات متحركة */
    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.1);
    }

    /* تنسيق الأيقونات */
    .fa-eye, .fa-info {
        color: #17a2b8;
    }

    .fa-edit, .fa-pencil-alt {
        color: #007bff;
    }

    .fa-trash-alt {
        color: #dc3545;
    }

    /* تنسيق الروابط */
    a {
        text-decoration: none;
    }

    /* تنسيق الفورم */
    .form-control:focus, .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تنسيق الأزرار المجمعة */
    .btn-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
    }

    /* تنسيق خاص بالوحدات */
    .unit-card {
        transition: all 0.3s ease;
    }

    .unit-card:hover {
        transform: translateY(-5px);
    }

    .unit-icon {
        font-size: 2rem;
    }

    .unit-vacant {
        color: #28a745;
    }

    .unit-occupied {
        color: #dc3545;
    }

    .unit-maintenance {
        color: #ffc107;
    }

    .unit-reserved {
        color: #17a2b8;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة الوحدات</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('properties.add_unit') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة وحدة جديدة
        </a>
    </div>
</div>

<div class="card mb-4 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>بحث وفلترة
        </h5>
        <button class="btn btn-sm btn-outline-primary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
            <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
        </button>
    </div>
    <div class="card-body collapse show" id="searchCollapse">
        <form method="GET" action="{{ url_for('properties.units') }}" id="searchForm">
            <div class="row g-3">
                <!-- حقل البحث - يظهر دائماً بعرض كامل -->
                <div class="col-12">
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="ابحث في رقم الوحدة، العدادات..."
                               value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            بحث
                        </button>
                        <a href="{{ url_for('properties.units') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i>
                        </a>
                    </div>
                </div>

                <!-- الفلاتر الرئيسية -->
                <div class="col-12 col-md-6 col-lg-3">
                    <label for="building_id" class="form-label">المبنى</label>
                    <select class="form-select form-select-sm" id="building_id" name="building_id">
                        <option value="">الكل</option>
                        {% for building in buildings_list %}
                        <option value="{{ building.id }}" {% if building_id == building.id|string %}selected{% endif %}>{{ building.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="type" class="form-label">نوع الوحدة</label>
                    <select class="form-select form-select-sm" id="type" name="type">
                        <option value="">الكل</option>
                        <option value="apartment" {% if type == 'apartment' %}selected{% endif %}>شقة</option>
                        <option value="villa" {% if type == 'villa' %}selected{% endif %}>فيلا</option>
                        <option value="shop" {% if type == 'shop' %}selected{% endif %}>محل تجاري</option>
                        <option value="office" {% if type == 'office' %}selected{% endif %}>مكتب</option>
                        <option value="warehouse" {% if type == 'warehouse' %}selected{% endif %}>مستودع</option>
                        <option value="other" {% if type == 'other' %}selected{% endif %}>أخرى</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="status" class="form-label">حالة الوحدة</label>
                    <select class="form-select form-select-sm" id="status" name="status">
                        <option value="">الكل</option>
                        <option value="vacant" {% if status == 'vacant' %}selected{% endif %}>شاغرة</option>
                        <option value="occupied" {% if status == 'occupied' %}selected{% endif %}>مشغولة</option>
                        <option value="maintenance" {% if status == 'maintenance' %}selected{% endif %}>تحت الصيانة</option>
                        <option value="reserved" {% if status == 'reserved' %}selected{% endif %}>محجوزة</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-3">
                    <label for="min_rooms" class="form-label">عدد الغرف</label>
                    <select class="form-select form-select-sm" id="min_rooms" name="min_rooms">
                        <option value="">الكل</option>
                        <option value="1" {% if min_rooms == '1' %}selected{% endif %}>1+</option>
                        <option value="2" {% if min_rooms == '2' %}selected{% endif %}>2+</option>
                        <option value="3" {% if min_rooms == '3' %}selected{% endif %}>3+</option>
                        <option value="4" {% if min_rooms == '4' %}selected{% endif %}>4+</option>
                        <option value="5" {% if min_rooms == '5' %}selected{% endif %}>5+</option>
                    </select>
                </div>
            </div>

            <!-- فلاتر إضافية -->
            <div class="row g-3 mt-2">
                <div class="col-12">
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters" aria-expanded="false">
                        <i class="fas fa-sliders-h me-1"></i>خيارات متقدمة
                    </button>
                </div>

                <div class="col-12 collapse" id="advancedFilters">
                    <div class="card card-body bg-light">
                        <div class="row g-3">
                            <div class="col-12 col-md-6 col-lg-3">
                                <label for="min_rent" class="form-label">الإيجار من</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">SAR</span>
                                    <input type="number" class="form-control" id="min_rent" name="min_rent" placeholder="الحد الأدنى" value="{{ min_rent }}">
                                </div>
                            </div>

                            <div class="col-12 col-md-6 col-lg-3">
                                <label for="max_rent" class="form-label">الإيجار إلى</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">SAR</span>
                                    <input type="number" class="form-control" id="max_rent" name="max_rent" placeholder="الحد الأقصى" value="{{ max_rent }}">
                                </div>
                            </div>

                            <div class="col-12 col-md-6 col-lg-3">
                                <label for="sort_by" class="form-label">ترتيب حسب</label>
                                <select class="form-select form-select-sm" id="sort_by" name="sort_by">
                                    <option value="building" {% if sort_by == 'building' %}selected{% endif %}>المبنى</option>
                                    <option value="unit_number" {% if sort_by == 'unit_number' %}selected{% endif %}>رقم الوحدة</option>
                                    <option value="rent_asc" {% if sort_by == 'rent_asc' %}selected{% endif %}>الإيجار (تصاعدي)</option>
                                    <option value="rent_desc" {% if sort_by == 'rent_desc' %}selected{% endif %}>الإيجار (تنازلي)</option>
                                    <option value="rooms" {% if sort_by == 'rooms' %}selected{% endif %}>عدد الغرف</option>
                                </select>
                            </div>

                            <div class="col-12 col-md-6 col-lg-3">
                                <label for="display_mode" class="form-label">طريقة العرض</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="display_mode" id="table_mode" value="table" {% if display_mode == 'table' or not display_mode %}checked{% endif %}>
                                    <label class="btn btn-outline-primary" for="table_mode">
                                        <i class="fas fa-table me-1"></i>جدول
                                    </label>

                                    <input type="radio" class="btn-check" name="display_mode" id="cards_mode" value="cards" {% if display_mode == 'cards' %}checked{% endif %}>
                                    <label class="btn btn-outline-primary" for="cards_mode">
                                        <i class="fas fa-th-large me-1"></i>بطاقات
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار إضافية للشاشات المتوسطة والكبيرة -->
            <div class="d-none d-md-flex justify-content-end mt-3">
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>تطبيق الفلاتر
                    </button>
                    <a href="{{ url_for('properties.units') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين
                    </a>
                    <a href="{{ url_for('properties.add_unit') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة وحدة جديدة
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-home me-2"></i>قائمة الوحدات
        </h5>
        <span class="badge bg-primary">{{ units|length }} وحدة</span>
    </div>
    <div class="card-body">
        {% if units %}
            {% if display_mode == 'table' or not display_mode %}
            <!-- عرض الجدول -->
            <!-- جدول للشاشات المتوسطة والكبيرة -->
            <div class="table-responsive d-none d-md-block">
                <table class="table table-striped table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">#</th>
                            <th>رقم الوحدة</th>
                            <th>المبنى</th>
                            <th>النوع</th>
                            <th>قيمة الإيجار</th>
                            <th>الحالة</th>
                            <th>المستأجر</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for unit in units %}
                        <tr>
                            <td class="text-center">{{ unit.id }}</td>
                            <td><strong>{{ unit.unit_number }}</strong></td>
                            <td>
                                <a href="{{ url_for('properties.view_building', building_id=unit.building_id) }}" class="text-decoration-none">
                                    <i class="fas fa-building text-primary me-1"></i>{{ unit.building_name }}
                                </a>
                            </td>
                            <td>
                                {% if unit.type == 'apartment' %}
                                <i class="fas fa-home text-primary me-1"></i>شقة
                                {% elif unit.type == 'villa' %}
                                <i class="fas fa-home text-success me-1"></i>فيلا
                                {% elif unit.type == 'shop' %}
                                <i class="fas fa-store text-warning me-1"></i>محل تجاري
                                {% elif unit.type == 'office' %}
                                <i class="fas fa-briefcase text-info me-1"></i>مكتب
                                {% elif unit.type == 'warehouse' %}
                                <i class="fas fa-warehouse text-secondary me-1"></i>مستودع
                                {% else %}
                                <i class="fas fa-building text-muted me-1"></i>أخرى
                                {% endif %}
                            </td>
                            <td>
                                <span class="fw-bold">{{ unit.rent_amount|format_currency }}</span>
                            </td>
                            <td>
                                {% if unit.status == 'vacant' %}
                                <span class="badge bg-success">شاغرة</span>
                                {% elif unit.status == 'occupied' %}
                                <span class="badge bg-danger">مشغولة</span>
                                {% elif unit.status == 'maintenance' %}
                                <span class="badge bg-warning text-dark">تحت الصيانة</span>
                                {% elif unit.status == 'reserved' %}
                                <span class="badge bg-info">محجوزة</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if unit.tenant_name %}
                                <a href="{{ url_for('tenants.view', tenant_id=unit.tenant_id) if unit.tenant_id else '#' }}" class="text-decoration-none">
                                    <i class="fas fa-user text-primary me-1"></i>{{ unit.tenant_name }}
                                </a>
                                {% else %}
                                <span class="text-muted"><i class="fas fa-user-slash me-1"></i>لا يوجد</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('properties.view_unit', unit_id=unit.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل" data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('properties.edit_unit', unit_id=unit.id) }}" class="btn btn-sm btn-primary" title="تعديل" data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteModal{{ unit.id }}"
                                            title="حذف" data-bs-toggle="tooltip">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>

                                <!-- Modal for delete confirmation -->
                                <div class="modal fade" id="deleteModal{{ unit.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ unit.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ unit.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف الوحدة <strong>{{ unit.unit_number }}</strong> في المبنى <strong>{{ unit.building_name }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('properties.delete_unit', unit_id=unit.id) }}" method="POST" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- قائمة للشاشات الصغيرة (الهواتف) -->
            <div class="d-md-none">
                {% for unit in units %}
                <div class="card mb-3 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center
                                {% if unit.status == 'vacant' %}bg-success bg-opacity-10
                                {% elif unit.status == 'occupied' %}bg-danger bg-opacity-10
                                {% elif unit.status == 'maintenance' %}bg-warning bg-opacity-10
                                {% elif unit.status == 'reserved' %}bg-info bg-opacity-10{% endif %}">
                        <h5 class="card-title mb-0">
                            <strong>{{ unit.unit_number }}</strong>
                        </h5>
                        <div>
                            {% if unit.status == 'vacant' %}
                            <span class="badge bg-success">شاغرة</span>
                            {% elif unit.status == 'occupied' %}
                            <span class="badge bg-danger">مشغولة</span>
                            {% elif unit.status == 'maintenance' %}
                            <span class="badge bg-warning text-dark">تحت الصيانة</span>
                            {% elif unit.status == 'reserved' %}
                            <span class="badge bg-info">محجوزة</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-building text-primary me-2"></i>المبنى:</span>
                                <a href="{{ url_for('properties.view_building', building_id=unit.building_id) }}" class="text-decoration-none">{{ unit.building_name }}</a>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>
                                    {% if unit.type == 'apartment' %}
                                    <i class="fas fa-home text-primary me-2"></i>النوع:
                                    {% elif unit.type == 'villa' %}
                                    <i class="fas fa-home text-success me-2"></i>النوع:
                                    {% elif unit.type == 'shop' %}
                                    <i class="fas fa-store text-warning me-2"></i>النوع:
                                    {% elif unit.type == 'office' %}
                                    <i class="fas fa-briefcase text-info me-2"></i>النوع:
                                    {% elif unit.type == 'warehouse' %}
                                    <i class="fas fa-warehouse text-secondary me-2"></i>النوع:
                                    {% else %}
                                    <i class="fas fa-building text-muted me-2"></i>النوع:
                                    {% endif %}
                                </span>
                                <span>
                                    {% if unit.type == 'apartment' %}
                                    شقة
                                    {% elif unit.type == 'villa' %}
                                    فيلا
                                    {% elif unit.type == 'shop' %}
                                    محل تجاري
                                    {% elif unit.type == 'office' %}
                                    مكتب
                                    {% elif unit.type == 'warehouse' %}
                                    مستودع
                                    {% else %}
                                    أخرى
                                    {% endif %}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-money-bill-wave text-success me-2"></i>الإيجار:</span>
                                <strong>{{ unit.rent_amount|format_currency }}</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-user text-primary me-2"></i>المستأجر:</span>
                                {% if unit.tenant_name %}
                                <a href="{{ url_for('tenants.view', tenant_id=unit.tenant_id) if unit.tenant_id else '#' }}" class="text-decoration-none">{{ unit.tenant_name }}</a>
                                {% else %}
                                <span class="text-muted">لا يوجد</span>
                                {% endif %}
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ url_for('properties.view_unit', unit_id=unit.id) }}" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <a href="{{ url_for('properties.edit_unit', unit_id=unit.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalMobile{{ unit.id }}">
                            <i class="fas fa-trash-alt me-1"></i>حذف
                        </button>
                    </div>

                    <!-- Modal for mobile delete confirmation -->
                    <div class="modal fade" id="deleteModalMobile{{ unit.id }}" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">تأكيد الحذف</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    هل أنت متأكد من رغبتك في حذف الوحدة <strong>{{ unit.unit_number }}</strong> في المبنى <strong>{{ unit.building_name }}</strong>؟
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                    <form action="{{ url_for('properties.delete_unit', unit_id=unit.id) }}" method="POST">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger">حذف</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- عرض البطاقات -->
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                {% for unit in units %}
                <div class="col">
                    <div class="card h-100 shadow-sm unit-card">
                        <div class="card-header
                                {% if unit.status == 'vacant' %}bg-success bg-opacity-10
                                {% elif unit.status == 'occupied' %}bg-danger bg-opacity-10
                                {% elif unit.status == 'maintenance' %}bg-warning bg-opacity-10
                                {% elif unit.status == 'reserved' %}bg-info bg-opacity-10{% endif %}">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <strong>{{ unit.unit_number }}</strong>
                                </h5>
                                <div>
                                    {% if unit.status == 'vacant' %}
                                    <span class="badge bg-success">شاغرة</span>
                                    {% elif unit.status == 'occupied' %}
                                    <span class="badge bg-danger">مشغولة</span>
                                    {% elif unit.status == 'maintenance' %}
                                    <span class="badge bg-warning text-dark">تحت الصيانة</span>
                                    {% elif unit.status == 'reserved' %}
                                    <span class="badge bg-info">محجوزة</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                {% if unit.type == 'apartment' %}
                                <i class="fas fa-home unit-icon unit-vacant"></i>
                                {% elif unit.type == 'villa' %}
                                <i class="fas fa-home unit-icon unit-occupied"></i>
                                {% elif unit.type == 'shop' %}
                                <i class="fas fa-store unit-icon unit-maintenance"></i>
                                {% elif unit.type == 'office' %}
                                <i class="fas fa-briefcase unit-icon unit-reserved"></i>
                                {% elif unit.type == 'warehouse' %}
                                <i class="fas fa-warehouse unit-icon"></i>
                                {% else %}
                                <i class="fas fa-building unit-icon"></i>
                                {% endif %}
                            </div>
                            <ul class="list-group list-group-flush mb-3">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span><i class="fas fa-building text-primary me-2"></i>المبنى:</span>
                                    <a href="{{ url_for('properties.view_building', building_id=unit.building_id) }}" class="text-decoration-none">{{ unit.building_name }}</a>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span><i class="fas fa-money-bill-wave text-success me-2"></i>الإيجار:</span>
                                    <strong>{{ unit.rent_amount|format_currency }}</strong>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span><i class="fas fa-user text-primary me-2"></i>المستأجر:</span>
                                    {% if unit.tenant_name %}
                                    <a href="{{ url_for('tenants.view', tenant_id=unit.tenant_id) if unit.tenant_id else '#' }}" class="text-decoration-none">{{ unit.tenant_name }}</a>
                                    {% else %}
                                    <span class="text-muted">لا يوجد</span>
                                    {% endif %}
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('properties.view_unit', unit_id=unit.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="{{ url_for('properties.edit_unit', unit_id=unit.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalCard{{ unit.id }}">
                                    <i class="fas fa-trash-alt me-1"></i>حذف
                                </button>
                            </div>

                            <!-- Modal for card delete confirmation -->
                            <div class="modal fade" id="deleteModalCard{{ unit.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من رغبتك في حذف الوحدة <strong>{{ unit.unit_number }}</strong> في المبنى <strong>{{ unit.building_name }}</strong>؟
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('properties.delete_unit', unit_id=unit.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        {% else %}
        <div class="alert alert-info">
            لا توجد وحدات حتى الآن. <a href="{{ url_for('properties.add_unit') }}" class="btn btn-sm btn-primary ms-2">إضافة وحدة جديدة</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث النموذج عند تغيير أي حقل (باستثناء حقل البحث وحقول الإيجار)
        const autoSubmitElements = document.querySelectorAll('#searchForm select:not(#min_rooms), #searchForm input[type="radio"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', function() {
                // إظهار مؤشر التحميل
                const submitBtn = document.querySelector('#searchForm button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التحميل...';
                    submitBtn.disabled = true;
                }

                // تأخير التقديم قليلاً للسماح بتحديث القيم
                setTimeout(() => {
                    document.getElementById('searchForm').submit();
                }, 100);
            });
        });

        // إضافة تأثير تمييز للصفوف عند التمرير فوقها
        const tableRows = document.querySelectorAll('table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('table-active');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('table-active');
            });
        });

        // تفعيل البحث الفوري عند الكتابة في حقل البحث
        const searchInput = document.getElementById('search');
        let searchTimeout;
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        document.getElementById('searchForm').submit();
                    }
                }, 500);
            });
        }

        // تفعيل التلميحات (tooltips)
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تأثيرات للبطاقات
        const unitCards = document.querySelectorAll('.unit-card');
        unitCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('shadow');
            });
            card.addEventListener('mouseleave', function() {
                this.classList.remove('shadow');
            });
        });

        // تحديث حقل الإيجار الأقصى تلقائيًا عند تغيير الإيجار الأدنى
        const minRentInput = document.getElementById('min_rent');
        const maxRentInput = document.getElementById('max_rent');

        if (minRentInput && maxRentInput) {
            minRentInput.addEventListener('change', function() {
                if (this.value && maxRentInput.value && parseInt(this.value) > parseInt(maxRentInput.value)) {
                    maxRentInput.value = this.value;
                }
            });

            // تقديم النموذج عند تغيير قيم الإيجار بعد تأخير قصير
            const rentInputs = [minRentInput, maxRentInput];
            rentInputs.forEach(input => {
                input.addEventListener('change', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        document.getElementById('searchForm').submit();
                    }, 1000);
                });
            });
        }

        // تفعيل الفلاتر المتقدمة
        const advancedFiltersBtn = document.querySelector('[data-bs-target="#advancedFilters"]');
        if (advancedFiltersBtn) {
            // فحص إذا كانت هناك قيم في الفلاتر المتقدمة، إذا كان كذلك، افتح القسم تلقائيًا
            const minRentValue = minRentInput ? minRentInput.value : '';
            const maxRentValue = maxRentInput ? maxRentInput.value : '';
            const sortByValue = document.getElementById('sort_by') ? document.getElementById('sort_by').value : '';
            const displayModeValue = document.querySelector('input[name="display_mode"]:checked') ? document.querySelector('input[name="display_mode"]:checked').value : '';

            if (minRentValue || maxRentValue || sortByValue || (displayModeValue && displayModeValue !== 'table')) {
                const advancedFilters = document.getElementById('advancedFilters');
                if (advancedFilters) {
                    advancedFilters.classList.add('show');
                }
            }
        }
    });
</script>
{% endblock %}