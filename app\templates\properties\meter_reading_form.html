{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.unit_id.label(class="form-label") }}
                        {% if form.unit_id.errors %}
                            {{ form.unit_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.unit_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.unit_id(class="form-select") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.reading_date.label(class="form-label") }}
                        {% if form.reading_date.errors %}
                            {{ form.reading_date(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.reading_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.reading_date(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.electricity_reading.label(class="form-label") }}
                            {% if form.electricity_reading.errors %}
                                {{ form.electricity_reading(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.electricity_reading.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.electricity_reading(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.water_reading.label(class="form-label") }}
                            {% if form.water_reading.errors %}
                                {{ form.water_reading(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.water_reading.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.water_reading(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.gas_reading.label(class="form-label") }}
                            {% if form.gas_reading.errors %}
                                {{ form.gas_reading(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.gas_reading.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.gas_reading(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('properties.view_unit', unit_id=request.args.get('unit_id', 0)) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
