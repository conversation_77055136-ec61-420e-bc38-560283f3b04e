from app import create_app
import click
from flask.cli import with_appcontext
import sqlite3
import os

app = create_app()

@click.command('init-db')
@with_appcontext
def init_db_command():
    """تهيئة قاعدة البيانات."""
    # التأكد من وجود مجلد instance
    os.makedirs('instance', exist_ok=True)

    # إنشاء اتصال بقاعدة البيانات
    conn = sqlite3.connect(app.config['DATABASE_PATH'])

    # تنفيذ سكريبت إنشاء قاعدة البيانات
    with open('schema.sql', 'r', encoding='utf-8') as f:
        conn.executescript(f.read())

    conn.close()

    # إنشاء مجلد التحميلات
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    click.echo('تم تهيئة قاعدة البيانات بنجاح.')

app.cli.add_command(init_db_command)

if __name__ == '__main__':
    import argparse

    # إعداد محلل المعلمات
    parser = argparse.ArgumentParser(description='تشغيل خادم مكتب عصام الفت')
    parser.add_argument('--host', default='127.0.0.1', help='عنوان IP للخادم')
    parser.add_argument('--port', type=int, default=5000, help='رقم المنفذ')
    parser.add_argument('--production', action='store_true', help='تشغيل في وضع الإنتاج')

    # تحليل المعلمات
    args = parser.parse_args()

    # تشغيل الخادم
    app.run(
        host=args.host,
        port=args.port,
        debug=not args.production
    )
