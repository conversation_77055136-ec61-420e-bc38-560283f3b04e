{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">{{ building.name }}</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('properties.edit_building', building_id=building.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('properties.buildings') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>بيانات المبنى
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>اسم المبنى</th>
                            <td>{{ building.name }}</td>
                        </tr>
                        <tr>
                            <th>رقم العقار</th>
                            <td>
                                {% if building.building_code %}
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary me-2">{{ building.building_code }}</span>
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" data-clipboard-text="{{ building.building_code }}" title="نسخ">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                {% else %}
                                غير متوفر
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>المالك</th>
                            <td>
                                <a href="{{ url_for('owners.view', owner_id=building.owner_id) }}">
                                    {{ building.owner_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>الوكيل</th>
                            <td>{{ building.agent or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>النوع</th>
                            <td>{{ building.type or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>الاستخدام</th>
                            <td>{{ building.usage or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>العنوان</th>
                            <td>{{ building.address }}</td>
                        </tr>
                        <tr>
                            <th>الحي</th>
                            <td>{{ building.district or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>المدينة</th>
                            <td>{{ building.city }}</td>
                        </tr>
                        <tr>
                            <th>رقم المبنى</th>
                            <td>{{ building.building_number or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>الرقم الإضافي</th>
                            <td>{{ building.additional_number or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>الرمز البريدي</th>
                            <td>{{ building.postal_code or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>رقم الصك</th>
                            <td>{{ building.deed_number or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الصك</th>
                            <td>{{ building.deed_date|format_date if building.deed_date else 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>نوع الصك</th>
                            <td>{{ building.deed_type or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>جهة إصدار الصك</th>
                            <td>{{ building.deed_issuer or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>الملف القديم</th>
                            <td>{{ building.old_file or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>سنة البناء</th>
                            <td>{{ building.build_year or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>عدد الطوابق</th>
                            <td>{{ building.floors_count or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ building.notes or 'لا توجد ملاحظات' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-home me-2"></i>الوحدات
                </h5>
            </div>
            <div class="card-body">
                {% if units %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم الوحدة</th>
                                <th>الطابق</th>
                                <th>النوع</th>
                                <th>قيمة الإيجار</th>
                                <th>الحالة</th>
                                <th>المستأجر</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for unit in units %}
                            <tr>
                                <td>{{ unit.unit_number }}</td>
                                <td>{{ unit.floor_number or 'غير محدد' }}</td>
                                <td>
                                    {% if unit.type == 'apartment' %}
                                    شقة
                                    {% elif unit.type == 'villa' %}
                                    فيلا
                                    {% elif unit.type == 'shop' %}
                                    محل تجاري
                                    {% elif unit.type == 'office' %}
                                    مكتب
                                    {% elif unit.type == 'warehouse' %}
                                    مستودع
                                    {% else %}
                                    أخرى
                                    {% endif %}
                                </td>
                                <td>{{ unit.rent_amount|format_currency }}</td>
                                <td>
                                    {% if unit.status == 'vacant' %}
                                    <span class="badge bg-success">شاغرة</span>
                                    {% elif unit.status == 'occupied' %}
                                    <span class="badge bg-danger">مشغولة</span>
                                    {% elif unit.status == 'maintenance' %}
                                    <span class="badge bg-warning">تحت الصيانة</span>
                                    {% elif unit.status == 'reserved' %}
                                    <span class="badge bg-info">محجوزة</span>
                                    {% endif %}
                                </td>
                                <td>{{ unit.tenant_name or 'لا يوجد' }}</td>
                                <td>
                                    <a href="{{ url_for('properties.view_unit', unit_id=unit.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد وحدات مسجلة لهذا المبنى.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('properties.add_unit') }}?building_id={{ building.id }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>إضافة وحدة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>المستندات
                </h5>
            </div>
            <div class="card-body">
                {% if documents %}
                <div class="row">
                    {% for document in documents %}
                    <div class="col-md-6 mb-3">
                        <div class="card document-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ document.title }}</h5>
                                <p class="card-text text-muted">
                                    <small>
                                        <i class="fas fa-calendar-alt me-1"></i>{{ document.upload_date|format_date }}
                                    </small>
                                </p>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>تنزيل
                                    </a>
                                    <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مستندات مرفقة لهذا المبنى.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('documents.add') }}?related_to=building&related_id={{ building.id }}" class="btn btn-info">
                        <i class="fas fa-plus-circle me-1"></i>إضافة مستند جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>طلبات الصيانة
                </h5>
            </div>
            <div class="card-body">
                {% if maintenance_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوحدة</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in maintenance_requests %}
                            <tr>
                                <td>{{ request.request_date|format_date }}</td>
                                <td>{{ request.unit_number }}</td>
                                <td>{{ request.description|truncate(30) }}</td>
                                <td>
                                    {% if request.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif request.status == 'in_progress' %}
                                    <span class="badge bg-info">قيد التنفيذ</span>
                                    {% elif request.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif request.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('properties.view_maintenance', maintenance_id=request.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد طلبات صيانة لهذا المبنى.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة زر النسخ
        const copyButtons = document.querySelectorAll('.copy-btn');
        copyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const textToCopy = this.getAttribute('data-clipboard-text');

                // نسخ النص إلى الحافظة
                navigator.clipboard.writeText(textToCopy).then(() => {
                    // تغيير شكل الزر مؤقتًا للإشارة إلى نجاح النسخ
                    const originalHTML = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    this.classList.remove('btn-outline-secondary');
                    this.classList.add('btn-success');

                    // إعادة الزر إلى حالته الأصلية بعد ثانيتين
                    setTimeout(() => {
                        this.innerHTML = originalHTML;
                        this.classList.remove('btn-success');
                        this.classList.add('btn-outline-secondary');
                    }, 2000);
                }).catch(err => {
                    console.error('فشل في نسخ النص: ', err);
                    alert('فشل في نسخ النص. يرجى المحاولة مرة أخرى.');
                });
            });
        });
    });
</script>
{% endblock %}