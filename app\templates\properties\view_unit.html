{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">{{ unit.unit_number }} - {{ unit.building_name }}</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('properties.edit_unit', unit_id=unit.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('properties.units') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-home me-2"></i>بيانات الوحدة
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>رقم الوحدة</th>
                            <td>{{ unit.unit_number }}</td>
                        </tr>
                        <tr>
                            <th>المبنى</th>
                            <td>
                                <a href="{{ url_for('properties.view_building', building_id=unit.building_id) }}">
                                    {{ unit.building_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>العنوان</th>
                            <td>{{ unit.building_address }}</td>
                        </tr>
                        <tr>
                            <th>الطابق</th>
                            <td>{{ unit.floor_number or 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <th>النوع</th>
                            <td>
                                {% if unit.type == 'apartment' %}
                                شقة
                                {% elif unit.type == 'villa' %}
                                فيلا
                                {% elif unit.type == 'shop' %}
                                محل تجاري
                                {% elif unit.type == 'office' %}
                                مكتب
                                {% elif unit.type == 'warehouse' %}
                                مستودع
                                {% else %}
                                أخرى
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>المساحة</th>
                            <td>{{ unit.area or 'غير محدد' }} متر مربع</td>
                        </tr>
                        <tr>
                            <th>عدد الغرف</th>
                            <td>{{ unit.rooms_count or 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <th>عدد الحمامات</th>
                            <td>{{ unit.bathrooms_count or 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <th>قيمة الإيجار</th>
                            <td>{{ unit.rent_amount|format_currency }}</td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                {% if unit.status == 'vacant' %}
                                <span class="badge bg-success">شاغرة</span>
                                {% elif unit.status == 'occupied' %}
                                <span class="badge bg-danger">مشغولة</span>
                                {% elif unit.status == 'maintenance' %}
                                <span class="badge bg-warning">تحت الصيانة</span>
                                {% elif unit.status == 'reserved' %}
                                <span class="badge bg-info">محجوزة</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ unit.notes or 'لا توجد ملاحظات' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-contract me-2"></i>العقد الحالي
                </h5>
            </div>
            <div class="card-body">
                {% if active_contract %}
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th>رقم العقد</th>
                                    <td>{{ active_contract.contract_number or active_contract.id }}</td>
                                </tr>
                                <tr>
                                    <th>المستأجر</th>
                                    <td>
                                        <a href="{{ url_for('tenants.view', tenant_id=active_contract.tenant_id) }}">
                                            {{ active_contract.tenant_name }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>رقم الهاتف</th>
                                    <td>{{ active_contract.tenant_phone }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ البداية</th>
                                    <td>{{ active_contract.start_date|format_date }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ النهاية</th>
                                    <td>{{ active_contract.end_date|format_date }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <th>قيمة الإيجار</th>
                                    <td>{{ active_contract.rent_amount|format_currency }}</td>
                                </tr>
                                <tr>
                                    <th>دورة الدفع</th>
                                    <td>
                                        {% if active_contract.payment_frequency == 'monthly' %}
                                        شهري
                                        {% elif active_contract.payment_frequency == 'quarterly' %}
                                        ربع سنوي
                                        {% elif active_contract.payment_frequency == 'biannual' %}
                                        نصف سنوي
                                        {% elif active_contract.payment_frequency == 'annual' %}
                                        سنوي
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>قيمة التأمين</th>
                                    <td>{{ active_contract.deposit_amount|format_currency if active_contract.deposit_amount else 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <th>الحالة</th>
                                    <td>
                                        {% if active_contract.status == 'active' %}
                                        <span class="badge bg-success">نشط</span>
                                        {% elif active_contract.status == 'expired' %}
                                        <span class="badge bg-danger">منتهي</span>
                                        {% elif active_contract.status == 'terminated' %}
                                        <span class="badge bg-warning">ملغي</span>
                                        {% elif active_contract.status == 'renewed' %}
                                        <span class="badge bg-info">مجدد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>ملاحظات</th>
                                    <td>{{ active_contract.notes or 'لا توجد ملاحظات' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('tenants.view_contract', contract_id=active_contract.id) }}" class="btn btn-info">
                        <i class="fas fa-eye me-1"></i>عرض تفاصيل العقد
                    </a>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا يوجد عقد نشط لهذه الوحدة.
                    {% if unit.status == 'vacant' or unit.status == 'reserved' %}
                    <a href="{{ url_for('tenants.add_contract') }}?unit_id={{ unit.id }}" class="alert-link">إضافة عقد جديد</a>
                    {% endif %}
                </div>
                {% if unit.status == 'vacant' or unit.status == 'reserved' %}
                <div class="mt-3">
                    <a href="{{ url_for('tenants.add_contract') }}?unit_id={{ unit.id }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>إضافة عقد جديد
                    </a>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
        
        {% if contracts_history %}
        <div class="card mt-3">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>سجل العقود السابقة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم العقد</th>
                                <th>المستأجر</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>قيمة الإيجار</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contract in contracts_history %}
                            <tr>
                                <td>{{ contract.contract_number or contract.id }}</td>
                                <td>{{ contract.tenant_name }}</td>
                                <td>{{ contract.start_date|format_date }}</td>
                                <td>{{ contract.end_date|format_date }}</td>
                                <td>{{ contract.rent_amount|format_currency }}</td>
                                <td>
                                    {% if contract.status == 'active' %}
                                    <span class="badge bg-success">نشط</span>
                                    {% elif contract.status == 'expired' %}
                                    <span class="badge bg-danger">منتهي</span>
                                    {% elif contract.status == 'terminated' %}
                                    <span class="badge bg-warning">ملغي</span>
                                    {% elif contract.status == 'renewed' %}
                                    <span class="badge bg-info">مجدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>قراءات العدادات
                </h5>
            </div>
            <div class="card-body">
                {% if meter_readings %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>قراءة الكهرباء</th>
                                <th>قراءة المياه</th>
                                <th>قراءة الغاز</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for reading in meter_readings %}
                            <tr>
                                <td>{{ reading.reading_date|format_date }}</td>
                                <td>{{ reading.electricity_reading or 'غير متوفر' }}</td>
                                <td>{{ reading.water_reading or 'غير متوفر' }}</td>
                                <td>{{ reading.gas_reading or 'غير متوفر' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد قراءات عدادات مسجلة لهذه الوحدة.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('properties.add_meter_reading') }}?unit_id={{ unit.id }}" class="btn btn-info">
                        <i class="fas fa-plus-circle me-1"></i>إضافة قراءة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>طلبات الصيانة
                </h5>
            </div>
            <div class="card-body">
                {% if maintenance_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>الأولوية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in maintenance_requests %}
                            <tr>
                                <td>{{ request.request_date|format_date }}</td>
                                <td>{{ request.description|truncate(30) }}</td>
                                <td>
                                    {% if request.priority == 'low' %}
                                    <span class="badge bg-success">منخفضة</span>
                                    {% elif request.priority == 'medium' %}
                                    <span class="badge bg-info">متوسطة</span>
                                    {% elif request.priority == 'high' %}
                                    <span class="badge bg-warning">عالية</span>
                                    {% elif request.priority == 'urgent' %}
                                    <span class="badge bg-danger">عاجلة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if request.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif request.status == 'in_progress' %}
                                    <span class="badge bg-info">قيد التنفيذ</span>
                                    {% elif request.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif request.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('properties.view_maintenance', maintenance_id=request.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد طلبات صيانة لهذه الوحدة.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('properties.add_maintenance') }}?unit_id={{ unit.id }}" class="btn btn-warning">
                        <i class="fas fa-plus-circle me-1"></i>إضافة طلب صيانة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>المستندات
                </h5>
            </div>
            <div class="card-body">
                {% if documents %}
                <div class="row">
                    {% for document in documents %}
                    <div class="col-md-3 mb-3">
                        <div class="card document-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ document.title }}</h5>
                                <p class="card-text text-muted">
                                    <small>
                                        <i class="fas fa-calendar-alt me-1"></i>{{ document.upload_date|format_date }}
                                    </small>
                                </p>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>تنزيل
                                    </a>
                                    <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مستندات مرفقة لهذه الوحدة.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('documents.add') }}?related_to=unit&related_id={{ unit.id }}" class="btn btn-info">
                        <i class="fas fa-plus-circle me-1"></i>إضافة مستند جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
