{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    #pdf-container {
        width: 100%;
        height: 800px;
        overflow: hidden;
        background-color: #525659;
        text-align: center;
        border-radius: 5px;
        position: relative;
    }

    #pdf-viewer {
        width: 100%;
        height: 100%;
        border: none;
    }

    #pdf-controls {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10;
    }

    .loading-spinner {
        color: white;
        font-size: 24px;
    }

    .btn-toolbar {
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-3">
        <div class="col-md-6">
            <h1 class="mb-0">{{ document.title }}</h1>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="{{ url_for('documents.view', document_id=document.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة
            </a>
            <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-success">
                <i class="fas fa-download me-1"></i>تنزيل
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <i class="fas fa-file-pdf me-2"></i>عرض المستند
        </div>
        <div class="card-body p-3">
            <div class="btn-toolbar">
                <div class="btn-group me-2">
                    <button id="open-external" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt"></i> فتح في نافذة جديدة
                    </button>
                </div>
            </div>

            <div id="pdf-container">
                <div class="loading-overlay" id="loading-overlay">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل المستند...
                    </div>
                </div>
                <iframe id="pdf-viewer" src="{{ file_url }}" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const pdfViewer = document.getElementById('pdf-viewer');
        const loadingOverlay = document.getElementById('loading-overlay');
        const openExternalBtn = document.getElementById('open-external');

        // إخفاء شاشة التحميل عند اكتمال تحميل الإطار
        pdfViewer.onload = function() {
            loadingOverlay.style.display = 'none';
        };

        // فتح المستند في نافذة جديدة
        openExternalBtn.addEventListener('click', function() {
            window.open('{{ file_url }}', '_blank');
        });

        // إعادة تحميل الإطار إذا فشل التحميل
        pdfViewer.onerror = function() {
            console.error('Failed to load PDF in iframe');
            loadingOverlay.innerHTML = `
                <div class="text-center text-white">
                    <p>فشل تحميل المستند في الإطار.</p>
                    <button class="btn btn-light mt-2" id="retry-load">إعادة المحاولة</button>
                </div>
            `;

            document.getElementById('retry-load').addEventListener('click', function() {
                pdfViewer.src = '{{ file_url }}';
                loadingOverlay.innerHTML = `
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل المستند...
                    </div>
                `;
            });
        };
    });
</script>
{% endblock %}
