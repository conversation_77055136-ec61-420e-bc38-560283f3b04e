"""
نظام إدارة مكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

from flask import current_app, g
import sqlite3
import os
import time
import logging
import traceback
from datetime import datetime

# إعداد التسجيل
logger = logging.getLogger('db')

def adapt_datetime(val):
    """تحويل كائن datetime إلى نص"""
    return val.isoformat()

def convert_datetime(val):
    """تحويل نص إلى كائن datetime"""
    try:
        return datetime.fromisoformat(val.decode())
    except ValueError:
        # إذا كان التنسيق غير صالح، إرجاع النص كما هو
        return val.decode()

def get_db():
    """الحصول على اتصال بقاعدة البيانات"""
    if 'db' not in g:
        try:
            # التأكد من وجود مجلد instance
            db_path = current_app.config['DATABASE_PATH']
            db_dir = os.path.dirname(db_path)

            logger.info(f"Database path: {db_path}")
            logger.info(f"Database directory: {db_dir}")

            os.makedirs(db_dir, exist_ok=True)
            logger.info(f"Ensured database directory exists: {db_dir}")

            # تسجيل محولات التاريخ والوقت
            sqlite3.register_adapter(datetime, adapt_datetime)
            sqlite3.register_converter("timestamp", convert_datetime)
            logger.info("Registered datetime adapters and converters")

            # محاولة الاتصال بقاعدة البيانات
            logger.info(f"Connecting to database: {db_path}")
            g.db = sqlite3.connect(
                db_path,
                detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES,
                timeout=30  # زيادة مهلة الانتظار للاتصال
            )
            g.db.row_factory = sqlite3.Row
            logger.info("Database connection established")

            # تكوين قاعدة البيانات لاستخدام التاريخ والوقت بشكل صحيح
            g.db.execute("PRAGMA foreign_keys = ON")
            # تكوين قاعدة البيانات للتعامل مع الاتصالات المتزامنة
            g.db.execute("PRAGMA busy_timeout = 15000")  # 15 ثوانٍ
            logger.info("Database configuration applied")

            # التحقق من وجود قاعدة البيانات وإنشائها إذا لم تكن موجودة
            if not os.path.exists(db_path) or os.path.getsize(db_path) == 0:
                logger.warning(f"Database file does not exist or is empty: {db_path}")
                init_db()
                logger.info("Initialized new database")
        except Exception as e:
            logger.error(f"Error connecting to database: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    return g.db

def close_db(e=None):
    """إغلاق اتصال قاعدة البيانات"""
    db = g.pop('db', None)

    if db is not None:
        db.close()

def init_db():
    """تهيئة قاعدة البيانات باستخدام ملف schema.sql"""
    try:
        logger.info("Initializing database...")

        # الحصول على اتصال بقاعدة البيانات
        db = get_db()

        # البحث عن ملف schema.sql
        schema_paths = [
            os.path.join(current_app.root_path, '..', 'schema.sql'),
            os.path.join(current_app.root_path, 'schema.sql'),
            os.path.join(os.path.dirname(current_app.root_path), 'schema.sql'),
            os.path.join(os.path.dirname(os.path.dirname(current_app.root_path)), 'schema.sql')
        ]

        schema_found = False
        for schema_path in schema_paths:
            logger.info(f"Looking for schema at: {schema_path}")
            if os.path.exists(schema_path):
                logger.info(f"Found schema at: {schema_path}")
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                    db.executescript(schema_sql)
                    schema_found = True
                    logger.info("Schema executed successfully")
                    break

        if not schema_found:
            # محاولة استخدام open_resource
            try:
                logger.info("Trying to use open_resource to find schema...")
                with current_app.open_resource('../schema.sql') as f:
                    db.executescript(f.read().decode('utf8'))
                    logger.info("Schema executed successfully using open_resource")
            except Exception as e:
                logger.error(f"Error loading schema with open_resource: {str(e)}")
                raise Exception("Could not find schema.sql file")

        # إنشاء مجلد التحميلات إذا لم يكن موجودًا
        upload_folder = current_app.config['UPLOAD_FOLDER']
        logger.info(f"Creating upload folder: {upload_folder}")
        os.makedirs(upload_folder, exist_ok=True)

        # إنشاء المجلدات الفرعية للتحميلات
        subdirs = ['buildings', 'contracts', 'documents', 'owners', 'tenants', 'transactions']
        for subdir in subdirs:
            subdir_path = os.path.join(upload_folder, subdir)
            os.makedirs(subdir_path, exist_ok=True)
            logger.info(f"Created upload subfolder: {subdir_path}")

        # إنشاء مستخدم أدمن افتراضي إذا لم يكن موجودًا
        create_default_admin(db)

        logger.info("Database initialization completed successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def create_default_admin(db):
    """إنشاء مستخدم أدمن افتراضي إذا لم يكن موجودًا"""
    try:
        logger.info("Checking for default admin user...")

        # التحقق من وجود جدول المستخدمين
        cursor = db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            logger.warning("Users table not found in database")
            return

        # التحقق من وجود مستخدم أدمن
        cursor = db.execute("SELECT COUNT(*) as count FROM users WHERE role = 'admin'")
        result = cursor.fetchone()

        if result and result['count'] > 0:
            logger.info("Admin user already exists")
            return

        # إنشاء مستخدم أدمن افتراضي
        logger.info("Creating default admin user...")

        # تشفير كلمة المرور
        import bcrypt
        password = "admin123"
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        # إدراج المستخدم الأدمن
        db.execute(
            "INSERT INTO users (username, password, name, email, role, created_at) VALUES (?, ?, ?, ?, ?, ?)",
            ("admin", hashed_password, "مدير النظام", "<EMAIL>", "admin", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        )
        db.commit()

        logger.info("Default admin user created successfully")
    except Exception as e:
        logger.error(f"Error creating default admin user: {str(e)}")
        logger.error(traceback.format_exc())

def init_app(app):
    """تسجيل وظائف قاعدة البيانات مع التطبيق"""
    app.teardown_appcontext(close_db)

def query_db(query, args=(), one=False, max_retries=5, retry_delay=0.5):
    """تنفيذ استعلام واسترجاع النتائج"""
    retries = 0
    last_error = None

    # تسجيل الاستعلام للتشخيص (مع إخفاء البيانات الحساسة)
    safe_query = query
    safe_args = ["***" if isinstance(arg, str) and len(arg) > 20 else arg for arg in args]
    logger.debug(f"Executing query: {safe_query}")
    logger.debug(f"Query args: {safe_args}")

    while retries < max_retries:
        try:
            db = get_db()
            cur = db.execute(query, args)
            rv = cur.fetchall()
            row_count = len(rv)
            cur.close()

            logger.debug(f"Query returned {row_count} rows")

            if one:
                result = rv[0] if rv else None
                logger.debug(f"Returning single row: {'Found' if result else 'None'}")
                return result
            else:
                return rv
        except sqlite3.OperationalError as e:
            error_msg = str(e)
            if "database is locked" in error_msg:
                # إذا كانت قاعدة البيانات مقفلة، ننتظر قليلاً ثم نحاول مرة أخرى
                last_error = e
                retries += 1
                logger.warning(f"Database locked, retrying ({retries}/{max_retries}): {error_msg}")
                time.sleep(retry_delay)
            else:
                # إذا كان الخطأ من نوع آخر، نرفعه مباشرة
                logger.error(f"SQLite operational error: {error_msg}")
                logger.error(f"Query: {query}")
                logger.error(f"Args: {safe_args}")
                raise
        except Exception as e:
            # أي خطأ آخر، نرفعه مباشرة
            logger.error(f"Error executing query: {str(e)}")
            logger.error(f"Query: {query}")
            logger.error(f"Args: {safe_args}")
            logger.error(traceback.format_exc())
            raise

    # إذا وصلنا إلى هنا، فقد فشلت جميع المحاولات
    if last_error:
        logger.error(f"All retries failed. Last error: {str(last_error)}")
        raise last_error

    logger.debug("Query returned no results")
    return [] if not one else None

def insert_db(table, fields=None, max_retries=5, retry_delay=0.5):
    """إدراج سجل جديد في الجدول المحدد"""
    if fields is None:
        return None

    # إضافة حقول التاريخ تلقائيًا إذا كانت موجودة في الجدول
    if 'created_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings', 'units']:
        fields['created_at'] = datetime.now()
    if 'updated_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings', 'units']:
        fields['updated_at'] = datetime.now()

    placeholders = ', '.join('?' * len(fields))
    columns = ', '.join(fields.keys())

    query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"

    retries = 0
    last_error = None

    while retries < max_retries:
        try:
            db = get_db()
            cursor = db.execute(query, list(fields.values()))
            db.commit()
            return cursor.lastrowid
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                # إذا كانت قاعدة البيانات مقفلة، ننتظر قليلاً ثم نحاول مرة أخرى
                last_error = e
                retries += 1
                time.sleep(retry_delay)
            else:
                # إذا كان الخطأ من نوع آخر، نرفعه مباشرة
                raise
        except Exception as e:
            # أي خطأ آخر، نرفعه مباشرة
            raise

    # إذا وصلنا إلى هنا، فقد فشلت جميع المحاولات
    if last_error:
        raise last_error

    return None

def update_db(table, id, fields=None, max_retries=5, retry_delay=0.5):
    """تحديث سجل موجود في الجدول المحدد"""
    if fields is None:
        return False

    # إضافة حقل updated_at تلقائيًا إذا كان موجودًا في الجدول
    if 'updated_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings', 'units']:
        fields['updated_at'] = datetime.now()

    set_clause = ', '.join([f"{key} = ?" for key in fields.keys()])

    query = f"UPDATE {table} SET {set_clause} WHERE id = ?"

    values = list(fields.values())
    values.append(id)

    retries = 0
    last_error = None

    while retries < max_retries:
        try:
            db = get_db()
            db.execute(query, values)
            db.commit()
            return True
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                # إذا كانت قاعدة البيانات مقفلة، ننتظر قليلاً ثم نحاول مرة أخرى
                last_error = e
                retries += 1
                time.sleep(retry_delay)
            else:
                # إذا كان الخطأ من نوع آخر، نرفعه مباشرة
                raise
        except Exception as e:
            # أي خطأ آخر، نرفعه مباشرة
            raise

    # إذا وصلنا إلى هنا، فقد فشلت جميع المحاولات
    if last_error:
        raise last_error

    return False

def delete_db(table, id, max_retries=5, retry_delay=0.5):
    """حذف سجل من الجدول المحدد"""
    query = f"DELETE FROM {table} WHERE id = ?"

    retries = 0
    last_error = None

    while retries < max_retries:
        try:
            db = get_db()
            db.execute(query, (id,))
            db.commit()
            return True
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                # إذا كانت قاعدة البيانات مقفلة، ننتظر قليلاً ثم نحاول مرة أخرى
                last_error = e
                retries += 1
                time.sleep(retry_delay)
            else:
                # إذا كان الخطأ من نوع آخر، نرفعه مباشرة
                raise
        except Exception as e:
            # أي خطأ آخر، نرفعه مباشرة
            raise

    # إذا وصلنا إلى هنا، فقد فشلت جميع المحاولات
    if last_error:
        raise last_error

    return False
