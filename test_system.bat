@echo off
chcp 65001 >nul
title اختبار النظام - مكتب عصام الفت

echo ================================================================
echo                    اختبار النظام
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

REM تحديد مجلد العمل الحالي
cd /d "%~dp0"

REM التحقق من وجود Python
echo 🐍 التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر
)

echo.
echo 🧪 بدء اختبار النظام...
echo.

REM تشغيل اختبار النظام
python test_system.py

echo.
echo ================================================================
echo                        انتهى الاختبار
echo ================================================================

pause
