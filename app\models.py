from flask_login import UserMixin
from app import login_manager, bcrypt
from app.db import query_db, insert_db, update_db
from datetime import datetime

class User(UserMixin):
    """نموذج المستخدم"""

    def __init__(self, id, username, password, name, email, role, created_at=None, last_login=None):
        self.id = id
        self.username = username
        self.password = password
        self.name = name
        self.email = email
        self.role = role
        self.created_at = created_at
        self.last_login = last_login

    @classmethod
    def get_by_id(cls, user_id):
        """الحصول على مستخدم بواسطة المعرف"""
        user = query_db('SELECT * FROM users WHERE id = ?', (user_id,), one=True)
        if user is None:
            return None
        return cls(**dict(user))

    @classmethod
    def get_by_username(cls, username):
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        user = query_db('SELECT * FROM users WHERE username = ?', (username,), one=True)
        if user is None:
            return None
        return cls(**dict(user))

    @classmethod
    def get_by_email(cls, email):
        """الحصول على مستخدم بواسطة البريد الإلكتروني"""
        user = query_db('SELECT * FROM users WHERE email = ?', (email,), one=True)
        if user is None:
            return None
        return cls(**dict(user))

    @classmethod
    def create(cls, username, password, name, email, role):
        """إنشاء مستخدم جديد"""
        hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')
        user_id = insert_db('users', {
            'username': username,
            'password': hashed_password,
            'name': name,
            'email': email,
            'role': role,
            'created_at': datetime.now()
        })
        return cls.get_by_id(user_id)

    def update_last_login(self):
        """تحديث وقت آخر تسجيل دخول"""
        update_db('users', self.id, {'last_login': datetime.now()})

    def check_password(self, password):
        """التحقق من كلمة المرور"""
        # استخدام bcrypt للتحقق من كلمة المرور
        return bcrypt.check_password_hash(self.password, password)

    def is_admin(self):
        """التحقق مما إذا كان المستخدم مديرًا"""
        return self.role == 'admin'

    def is_accountant(self):
        """التحقق مما إذا كان المستخدم محاسبًا"""
        return self.role == 'accountant' or self.role == 'admin'

    def is_data_entry(self):
        """التحقق مما إذا كان المستخدم مدخل بيانات"""
        return self.role == 'data_entry' or self.role == 'admin'

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم لـ Flask-Login"""
    return User.get_by_id(user_id)
