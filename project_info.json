{"project": {"name": "مكتب عصام الفت لإدارة الأملاك", "name_english": "Essam Al-Fat Real Estate Management Office", "version": "1.0.0", "description": "نظام شامل لإدارة العقارات والمستأجرين والمعاملات المالية", "description_english": "Comprehensive system for managing real estate, tenants, and financial transactions"}, "developer": {"company": "شركة المبرمج المصري", "company_english": "Egyptian Programmer Company", "facebook": "https://www.facebook.com/almbarmg", "whatsapp": "0201032540807", "copyright": "جميع الحقوق محفوظة © 2025"}, "technical_specs": {"framework": "Flask", "language": "Python", "database": "SQLite", "frontend": "HTML, CSS, JavaScript, Bootstrap", "gui": "PyQt5", "min_python_version": "3.8", "encoding": "UTF-8"}, "features": ["إدارة الملاك والمباني والوحدات", "إدارة المستأجرين والعقود", "نظام مالي متكامل", "إدارة المستندات والملفات", "تقارير مالية وإحصائية", "واجهة باللغة العربية", "نظام تنبيهات", "إدارة طلبات الصيانة", "قراءات العدادات", "رموز QR للمشاركة"], "modules": [{"name": "المصادقة والمستخدمين", "path": "app/auth/", "description": "تسجيل الدخول وإدارة المستخدمين"}, {"name": "لوحة التحكم", "path": "app/dashboard/", "description": "إحصائيات ورسوم بيانية"}, {"name": "إدارة الملاك", "path": "app/owners/", "description": "إدارة بيانات الملاك"}, {"name": "إدارة العقارات", "path": "app/properties/", "description": "إدارة المباني والوحدات"}, {"name": "إدارة المستأجرين", "path": "app/tenants/", "description": "إدارة المستأجرين والعقود"}, {"name": "الإدارة المالية", "path": "app/finance/", "description": "المعاملات المالية والتقارير"}, {"name": "إدارة المستندات", "path": "app/documents/", "description": "رفع وإدارة الملفات"}, {"name": "التقارير", "path": "app/reports/", "description": "تقارير شاملة وإيصالات"}], "database_tables": ["users", "owners", "buildings", "units", "tenants", "contracts", "documents", "transactions", "meter_readings", "notifications", "maintenance_requests"], "user_roles": [{"role": "admin", "name": "مدير", "permissions": "صلاحيات كاملة"}, {"role": "accountant", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permissions": "إدارة مالية ومحاسبية"}, {"role": "data_entry", "name": "مدخل بيانات", "permissions": "إدخال وتعديل البيانات"}], "default_credentials": {"username": "admin", "password": "admin123", "note": "يُنصح بتغيير كلمة المرور بعد التشغيل الأول"}, "system_requirements": {"minimum": {"os": "Windows 7/8/10/11", "python": "3.8+", "ram": "2 GB", "storage": "500 MB"}, "recommended": {"os": "Windows 10/11", "python": "3.11+", "ram": "4 GB", "storage": "1 GB"}}, "deployment_options": [{"type": "portable", "name": "النسخة المحمولة", "requires_python": true, "file": "portable_version/", "description": "تتطلب تثبيت Python"}, {"type": "executable", "name": "النسخة التنفيذية", "requires_python": false, "file": "dist/مكتب_عصام_الفت/", "description": "لا تتطلب تثبيت Python"}, {"type": "source", "name": "الكود المصدري", "requires_python": true, "file": "./", "description": "للمطورين والتخصيص"}], "startup_files": [{"file": "start_server.bat", "description": "تشغيل الخادم المحلي (وحدة التحكم)"}, {"file": "start_server_gui.bat", "description": "تشغيل الواجهة الرسومية"}, {"file": "start_server_network.bat", "description": "تشغيل خادم الشبكة"}, {"file": "run_portable.bat", "description": "تشغيل النسخة المحمولة"}, {"file": "quick_start.bat", "description": "قائمة تشغيل سريع"}], "build_files": [{"file": "create_portable_version.py", "description": "إنشاء النسخة المحمولة"}, {"file": "build_executable.py", "description": "إنشاء النسخة التنفيذية"}, {"file": "create_all_versions.bat", "description": "إنشاء جميع النسخ"}], "configuration_files": [{"file": "config.py", "description": "إعدادات التطبيق الرئيسية"}, {"file": "portable_config.py", "description": "إعدادات النسخة المحمولة"}, {"file": "pyinstaller_config.py", "description": "إعدادات PyInstaller"}], "data_directories": [{"path": "instance/", "description": "قاعدة البيانات والإعدادات"}, {"path": "uploads/", "description": "الملفات المرفوعة"}, {"path": "logs/", "description": "ملفات السجلات"}, {"path": "temp/", "description": "الملفات المؤقتة"}], "network_settings": {"default_host": "127.0.0.1", "default_port": 5000, "network_host": "0.0.0.0", "supported_protocols": ["HTTP"]}, "security_features": ["تشفير كلمات المرور باستخدام bcrypt", "حماية CSRF", "جلسات آمنة", "تحكم في الصلاحيات حسب الدور", "تسجيل العمليات"], "backup_recommendations": ["نسخ احتياطي دوري لمجلد instance/", "نسخ احتياطي لمجلد uploads/", "تصدير قاعدة البيانات بانتظام", "حفظ ملفات الإعدادات"], "support": {"whatsapp": "0201032540807", "facebook": "https://www.facebook.com/almbarmg", "hours": "ا<PERSON><PERSON><PERSON><PERSON> الخميس: 9:00 ص - 6:00 م", "language": "العربية"}}