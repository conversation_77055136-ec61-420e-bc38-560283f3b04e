#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات PyInstaller لمكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

# الوحدات المخفية المطلوبة
HIDDEN_IMPORTS = [
    # Flask والوحدات المرتبطة
    'flask',
    'flask.app',
    'flask.blueprints',
    'flask.cli',
    'flask.config',
    'flask.ctx',
    'flask.globals',
    'flask.helpers',
    'flask.json',
    'flask.logging',
    'flask.sessions',
    'flask.signals',
    'flask.templating',
    'flask.testing',
    'flask.views',
    'flask.wrappers',
    
    # Flask Extensions
    'flask_login',
    'flask_login.config',
    'flask_login.login_manager',
    'flask_login.mixins',
    'flask_login.signals',
    'flask_login.utils',
    'flask_bcrypt',
    'flask_wtf',
    'flask_wtf.csrf',
    'flask_wtf.form',
    'flask_wtf.file',
    'flask_mail',
    
    # Werkzeug
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.urls',
    'werkzeug.utils',
    'werkzeug.wrappers',
    'werkzeug.exceptions',
    'werkzeug.routing',
    'werkzeug.security',
    'werkzeug.datastructures',
    'werkzeug.http',
    'werkzeug.local',
    'werkzeug.middleware',
    
    # Jinja2
    'jinja2',
    'jinja2.environment',
    'jinja2.loaders',
    'jinja2.runtime',
    'jinja2.utils',
    'jinja2.filters',
    'jinja2.tests',
    'jinja2.ext',
    
    # WTForms
    'wtforms',
    'wtforms.fields',
    'wtforms.form',
    'wtforms.validators',
    'wtforms.widgets',
    'wtforms.csrf',
    
    # PyQt5
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    
    # مكتبات أخرى
    'bcrypt',
    'qrcode',
    'qrcode.image',
    'qrcode.image.pil',
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'email_validator',
    'itsdangerous',
    'markupsafe',
    'python_dotenv',
    
    # مكتبات Python الأساسية
    'sqlite3',
    'datetime',
    'os',
    'sys',
    'subprocess',
    'socket',
    'webbrowser',
    'traceback',
    'logging',
    'threading',
    'importlib',
    'importlib.util',
    'urllib',
    'urllib.parse',
    'json',
    'base64',
    'hashlib',
    'secrets',
    'uuid',
    'pathlib',
    'shutil',
    'tempfile',
    'io',
    'collections',
    'functools',
    'itertools',
    're',
    'string',
    'time',
    'calendar',
    'decimal',
    'math',
    'random',
    'platform',
    'signal',
    'argparse',
    'configparser',
    'csv',
    'xml',
    'xml.etree',
    'xml.etree.ElementTree',
    'zipfile',
    'tarfile',
    'gzip',
    'bz2',
    'lzma',
    'codecs',
    'locale',
    'gettext',
    'unicodedata',
    'encodings',
    'encodings.utf_8',
    'encodings.cp1256',
    'encodings.ascii',
    'encodings.latin1',
]

# البيانات المطلوبة (ملفات وأدلة)
DATAS = [
    ('app', 'app'),
    ('app/templates', 'app/templates'),
    ('app/static', 'app/static'),
    ('config.py', '.'),
    ('schema.sql', '.'),
    ('fix_paths.py', '.'),
    ('monkey_patch.py', '.'),
    ('file.ico', '.'),
    ('file.jpeg', '.'),
    ('requirements.txt', '.'),
]

# الملفات الثنائية (إذا كانت مطلوبة)
BINARIES = []

# الوحدات المستبعدة
EXCLUDES = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'setuptools',
    'distutils',
    'pip',
    'wheel',
    'pkg_resources',
]

# إعدادات UPX
UPX_EXCLUDE = [
    'vcruntime140.dll',
    'msvcp140.dll',
    'python38.dll',
    'python39.dll',
    'python310.dll',
    'python311.dll',
    'python312.dll',
    'python313.dll',
]

def get_spec_content(app_name="مكتب_عصام_الفت", console=True, icon_path="file.ico"):
    """إنشاء محتوى ملف spec"""
    
    spec_template = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['server_manager.py'],
    pathex=[],
    binaries={BINARIES},
    datas={DATAS},
    hiddenimports={HIDDEN_IMPORTS},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes={EXCLUDES},
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console={str(console).lower()},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{icon_path}',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude={UPX_EXCLUDE},
    name='{app_name}',
)
'''
    
    return spec_template

if __name__ == "__main__":
    # طباعة الإعدادات للمراجعة
    print("إعدادات PyInstaller:")
    print(f"عدد الوحدات المخفية: {len(HIDDEN_IMPORTS)}")
    print(f"عدد ملفات البيانات: {len(DATAS)}")
    print(f"عدد الوحدات المستبعدة: {len(EXCLUDES)}")
    
    # إنشاء ملف spec تجريبي
    spec_content = get_spec_content()
    with open("test_spec.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("تم إنشاء ملف test_spec.spec للمراجعة")
