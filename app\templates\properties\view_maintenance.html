{% extends "base.html" %}

{% block title %}تفاصيل طلب الصيانة - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">تفاصيل طلب الصيانة #{{ maintenance.id }}</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('properties.edit_maintenance', maintenance_id=maintenance.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('properties.view_unit', unit_id=maintenance.unit_id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع للوحدة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>بيانات طلب الصيانة
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>رقم الطلب</th>
                            <td>{{ maintenance.id }}</td>
                        </tr>
                        <tr>
                            <th>الوحدة</th>
                            <td>
                                <a href="{{ url_for('properties.view_unit', unit_id=maintenance.unit_id) }}">
                                    {{ maintenance.unit_number }} - {{ maintenance.building_name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>المستأجر</th>
                            <td>
                                {% if maintenance.tenant_name %}
                                <a href="{{ url_for('tenants.view', tenant_id=maintenance.tenant_id) }}">
                                    {{ maintenance.tenant_name }}
                                </a>
                                {% else %}
                                غير محدد
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الطلب</th>
                            <td>{{ maintenance.request_date|format_date }}</td>
                        </tr>
                        <tr>
                            <th>الأولوية</th>
                            <td>
                                {% if maintenance.priority == 'low' %}
                                <span class="badge bg-success">منخفضة</span>
                                {% elif maintenance.priority == 'medium' %}
                                <span class="badge bg-info">متوسطة</span>
                                {% elif maintenance.priority == 'high' %}
                                <span class="badge bg-warning">عالية</span>
                                {% elif maintenance.priority == 'urgent' %}
                                <span class="badge bg-danger">عاجلة</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                {% if maintenance.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif maintenance.status == 'in_progress' %}
                                <span class="badge bg-info">قيد التنفيذ</span>
                                {% elif maintenance.status == 'completed' %}
                                <span class="badge bg-success">مكتمل</span>
                                {% elif maintenance.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإكمال</th>
                            <td>{{ maintenance.completion_date|format_date if maintenance.completion_date else 'غير مكتمل بعد' }}</td>
                        </tr>
                        <tr>
                            <th>التكلفة</th>
                            <td>{{ maintenance.cost|format_currency if maintenance.cost else 'غير محدد' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>وصف المشكلة
                </h5>
            </div>
            <div class="card-body">
                <p>{{ maintenance.description }}</p>
                
                {% if maintenance.notes %}
                <div class="mt-4">
                    <h6>ملاحظات إضافية:</h6>
                    <p>{{ maintenance.notes }}</p>
                </div>
                {% endif %}
                
                {% if maintenance.status == 'pending' %}
                <div class="mt-4">
                    <a href="{{ url_for('properties.edit_maintenance', maintenance_id=maintenance.id) }}?status=in_progress" class="btn btn-info">
                        <i class="fas fa-play me-1"></i>بدء العمل
                    </a>
                </div>
                {% elif maintenance.status == 'in_progress' %}
                <div class="mt-4">
                    <a href="{{ url_for('properties.edit_maintenance', maintenance_id=maintenance.id) }}?status=completed" class="btn btn-success">
                        <i class="fas fa-check me-1"></i>إكمال العمل
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        {% if maintenance.status == 'completed' and maintenance.cost %}
        <div class="card mt-3">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>تسجيل مصروف
                </h5>
            </div>
            <div class="card-body">
                <p>يمكنك تسجيل تكلفة الصيانة كمصروف في النظام المالي.</p>
                <a href="{{ url_for('finance.add_transaction') }}?type=expense&category=maintenance&amount={{ maintenance.cost }}&related_to=unit&related_id={{ maintenance.unit_id }}" class="btn btn-success">
                    <i class="fas fa-plus-circle me-1"></i>تسجيل مصروف
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
