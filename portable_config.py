#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات النسخة المحمولة لمكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
from pathlib import Path

class PortableConfig:
    """إعدادات النسخة المحمولة"""
    
    def __init__(self):
        # تحديد المجلد الأساسي للتطبيق
        if getattr(sys, 'frozen', False):
            # في حالة التشغيل كملف تنفيذي
            self.app_dir = Path(sys.executable).parent
        else:
            # في حالة التشغيل كسكريبت
            self.app_dir = Path(__file__).parent
        
        # إعداد المسارات
        self.setup_paths()
        
        # إعداد متغيرات البيئة
        self.setup_environment()
    
    def setup_paths(self):
        """إعداد المسارات المطلوبة"""
        
        # مسارات البيانات
        self.data_dir = self.app_dir / "data"
        self.instance_dir = self.app_dir / "instance"
        self.uploads_dir = self.app_dir / "uploads"
        self.logs_dir = self.app_dir / "logs"
        self.temp_dir = self.app_dir / "temp"
        
        # مسارات التطبيق
        self.app_source_dir = self.app_dir / "app"
        self.templates_dir = self.app_source_dir / "templates"
        self.static_dir = self.app_source_dir / "static"
        
        # مسارات الملفات
        self.config_file = self.app_dir / "config.py"
        self.schema_file = self.app_dir / "schema.sql"
        self.database_file = self.instance_dir / "realestate.db"
        
        # إنشاء المجلدات المطلوبة
        self.create_directories()
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        
        directories = [
            self.data_dir,
            self.instance_dir,
            self.uploads_dir,
            self.logs_dir,
            self.temp_dir,
            self.uploads_dir / "buildings",
            self.uploads_dir / "contracts",
            self.uploads_dir / "documents",
            self.uploads_dir / "owners",
            self.uploads_dir / "tenants",
            self.uploads_dir / "transactions"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def setup_environment(self):
        """إعداد متغيرات البيئة"""
        
        # إعداد ترميز النصوص
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # إعداد مسارات التطبيق
        os.environ['APP_DIR'] = str(self.app_dir)
        os.environ['DATA_DIR'] = str(self.data_dir)
        os.environ['INSTANCE_DIR'] = str(self.instance_dir)
        os.environ['UPLOADS_DIR'] = str(self.uploads_dir)
        os.environ['LOGS_DIR'] = str(self.logs_dir)
        
        # إعداد قاعدة البيانات
        os.environ['DATABASE_PATH'] = str(self.database_file)
        
        # إضافة مجلد التطبيق إلى مسار البحث
        if str(self.app_dir) not in sys.path:
            sys.path.insert(0, str(self.app_dir))
    
    def get_config_dict(self):
        """الحصول على قاموس الإعدادات"""
        
        return {
            'APP_DIR': str(self.app_dir),
            'DATA_DIR': str(self.data_dir),
            'INSTANCE_DIR': str(self.instance_dir),
            'UPLOADS_DIR': str(self.uploads_dir),
            'LOGS_DIR': str(self.logs_dir),
            'TEMP_DIR': str(self.temp_dir),
            'DATABASE_PATH': str(self.database_file),
            'TEMPLATES_DIR': str(self.templates_dir),
            'STATIC_DIR': str(self.static_dir),
            'CONFIG_FILE': str(self.config_file),
            'SCHEMA_FILE': str(self.schema_file),
        }
    
    def is_first_run(self):
        """التحقق مما إذا كان هذا أول تشغيل"""
        return not self.database_file.exists()
    
    def create_config_file(self):
        """إنشاء ملف الإعدادات للنسخة المحمولة"""
        
        config_content = f'''"""
إعدادات النسخة المحمولة لمكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
from pathlib import Path

# المجلد الأساسي للتطبيق
APP_DIR = Path(__file__).parent

class Config:
    """فئة الإعدادات للنسخة المحمولة"""
    
    # الإعدادات الأساسية
    SECRET_KEY = 'مفتاح_سري_للنسخة_المحمولة_2025'
    
    # مسارات البيانات
    DATABASE_PATH = str(APP_DIR / "instance" / "realestate.db")
    UPLOAD_FOLDER = str(APP_DIR / "uploads")
    
    # إعدادات الملفات
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 ميجابايت
    
    # إعدادات التطبيق
    CURRENCY = 'ريال سعودي'
    CURRENCY_SYMBOL = 'ر.س'
    DATE_FORMAT = '%Y-%m-%d'
    
    # إعدادات التنبيهات
    CONTRACT_EXPIRY_ALERT_DAYS = 7
    
    # إعدادات النسخة المحمولة
    PORTABLE_MODE = True
    APP_DIR = str(APP_DIR)
    LOGS_DIR = str(APP_DIR / "logs")
    TEMP_DIR = str(APP_DIR / "temp")
    
    # إعدادات الخادم
    DEFAULT_HOST = '127.0.0.1'
    DEFAULT_PORT = 5000
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق للنسخة المحمولة"""
        
        # إنشاء المجلدات المطلوبة
        directories = [
            APP_DIR / "instance",
            APP_DIR / "uploads",
            APP_DIR / "logs",
            APP_DIR / "temp",
            APP_DIR / "uploads" / "buildings",
            APP_DIR / "uploads" / "contracts",
            APP_DIR / "uploads" / "documents",
            APP_DIR / "uploads" / "owners",
            APP_DIR / "uploads" / "tenants",
            APP_DIR / "uploads" / "transactions"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        # إعداد التسجيل
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            log_file = APP_DIR / "logs" / "app.log"
            file_handler = RotatingFileHandler(
                str(log_file), 
                maxBytes=10240000, 
                backupCount=10,
                encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('تم بدء تشغيل النسخة المحمولة')
'''
        
        portable_config_file = self.app_dir / "portable_config_generated.py"
        with open(portable_config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        return portable_config_file
    
    def get_startup_info(self):
        """الحصول على معلومات بدء التشغيل"""
        
        info = {
            'app_dir': str(self.app_dir),
            'is_first_run': self.is_first_run(),
            'database_exists': self.database_file.exists(),
            'config_exists': self.config_file.exists(),
            'schema_exists': self.schema_file.exists(),
            'directories_created': all([
                self.instance_dir.exists(),
                self.uploads_dir.exists(),
                self.logs_dir.exists()
            ])
        }
        
        return info

def initialize_portable_mode():
    """تهيئة النسخة المحمولة"""
    
    print("تهيئة النسخة المحمولة...")
    
    # إنشاء كائن الإعدادات
    config = PortableConfig()
    
    # عرض معلومات بدء التشغيل
    info = config.get_startup_info()
    
    print(f"مجلد التطبيق: {info['app_dir']}")
    print(f"أول تشغيل: {'نعم' if info['is_first_run'] else 'لا'}")
    print(f"قاعدة البيانات موجودة: {'نعم' if info['database_exists'] else 'لا'}")
    print(f"المجلدات منشأة: {'نعم' if info['directories_created'] else 'لا'}")
    
    # إنشاء ملف الإعدادات إذا لم يكن موجوداً
    if not config.config_file.exists():
        print("إنشاء ملف الإعدادات...")
        config.create_config_file()
    
    print("تم تهيئة النسخة المحمولة بنجاح!")
    
    return config

if __name__ == "__main__":
    # اختبار النسخة المحمولة
    config = initialize_portable_mode()
    
    print("\nإعدادات النسخة المحمولة:")
    for key, value in config.get_config_dict().items():
        print(f"  {key}: {value}")
