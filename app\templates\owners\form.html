{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {% if form.name.errors %}
                            {{ form.name(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.name(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.id_number.label(class="form-label") }}
                        {% if form.id_number.errors %}
                            {{ form.id_number(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.id_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.id_number(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.phone.label(class="form-label") }}
                        {% if form.phone.errors %}
                            {{ form.phone(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.phone(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {% if form.email.errors %}
                            {{ form.email(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.birth_date.label(class="form-label") }}
                        {% if form.birth_date.errors %}
                            {{ form.birth_date(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.birth_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.birth_date(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {% if form.address.errors %}
                            {{ form.address(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.address(class="form-control", rows=3) }}
                        {% endif %}
                    </div>

                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-university me-2"></i>معلومات البنك
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                {{ form.bank_name.label(class="form-label") }}
                                {% if form.bank_name.errors %}
                                    {{ form.bank_name(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.bank_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.bank_name(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                {{ form.bank_iban.label(class="form-label") }}
                                {% if form.bank_iban.errors %}
                                    {{ form.bank_iban(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.bank_iban.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.bank_iban(class="form-control", placeholder="************************") }}
                                {% endif %}
                                <div class="form-text text-muted">
                                    أدخل رقم الآيبان بدون مسافات أو رموز خاصة
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('owners.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
