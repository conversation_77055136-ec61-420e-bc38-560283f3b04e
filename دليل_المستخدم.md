# دليل المستخدم - مكتب عصام الفت لإدارة الأملاك

## تم التطوير والبرمجة بواسطة شركة المبرمج المصري
- **فيسبوك**: https://www.facebook.com/almbarmg
- **واتساب**: 0201032540807
- **جميع الحقوق محفوظة © 2025**

---

## فهرس المحتويات

1. [نظرة عامة](#نظرة-عامة)
2. [متطلبات النظام](#متطلبات-النظام)
3. [طرق التشغيل](#طرق-التشغيل)
4. [إنشاء النسخ المختلفة](#إنشاء-النسخ-المختلفة)
5. [بيانات الدخول](#بيانات-الدخول)
6. [استخدام النظام](#استخدام-النظام)
7. [الدعم الفني](#الدعم-الفني)

---

## نظرة عامة

**مكتب عصام الفت لإدارة الأملاك** هو نظام شامل لإدارة العقارات والمستأجرين والمعاملات المالية. يوفر النظام:

- ✅ إدارة الملاك والمباني والوحدات
- ✅ إدارة المستأجرين والعقود
- ✅ نظام مالي متكامل
- ✅ إدارة المستندات والملفات
- ✅ تقارير مالية وإحصائية
- ✅ واجهة باللغة العربية
- ✅ نظام تنبيهات
- ✅ إدارة طلبات الصيانة

---

## متطلبات النظام

### للنسخة العادية (تتطلب Python):
- **نظام التشغيل**: Windows 7/8/10/11
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 2 جيجابايت RAM كحد أدنى
- **مساحة القرص**: 500 ميجابايت

### للنسخة التنفيذية (لا تتطلب Python):
- **نظام التشغيل**: Windows 7/8/10/11
- **الذاكرة**: 2 جيجابايت RAM كحد أدنى
- **مساحة القرص**: 200 ميجابايت

---

## طرق التشغيل

### 1. التشغيل العادي (مع Python)

#### أ. تشغيل الخادم المحلي (وحدة التحكم)
```bash
# انقر نقراً مزدوجاً على:
start_server.bat

# أو من سطر الأوامر:
python server_manager.py --console
```

#### ب. تشغيل الواجهة الرسومية
```bash
# انقر نقراً مزدوجاً على:
start_server_gui.bat

# أو من سطر الأوامر:
python server_manager.py
```

#### ج. تشغيل خادم الشبكة
```bash
# انقر نقراً مزدوجاً على:
start_server_network.bat

# أو من سطر الأوامر:
python server_manager.py --network
```

### 2. التشغيل من النسخة التنفيذية

```bash
# انقر نقراً مزدوجاً على:
مكتب_عصام_الفت.exe

# أو استخدم ملفات التشغيل المرفقة
```

### 3. خيارات سطر الأوامر

```bash
# تشغيل على منفذ مخصص
python server_manager.py --port 8080

# تشغيل على عنوان IP مخصص
python server_manager.py --host *************

# تشغيل في وضع التطوير
python server_manager.py --debug

# إعادة تعيين كلمة مرور المدير
python server_manager.py --reset-admin-password
```

---

## إنشاء النسخ المختلفة

### 1. إنشاء جميع النسخ تلقائياً
```bash
# انقر نقراً مزدوجاً على:
create_all_versions.bat
```

### 2. إنشاء النسخة المحمولة فقط
```bash
python create_portable_version.py
```

### 3. إنشاء النسخة التنفيذية فقط
```bash
python build_executable.py
```

### النتائج:
- **النسخة المحمولة**: `portable_version/`
- **ملف ZIP المحمول**: `مكتب_عصام_الفت_النسخة_المحمولة.zip`
- **النسخة التنفيذية**: `dist/مكتب_عصام_الفت/`

---

## بيانات الدخول

### المستخدم الافتراضي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### أنواع المستخدمين:
1. **مدير** (admin): صلاحيات كاملة
2. **محاسب** (accountant): إدارة مالية ومحاسبية
3. **مدخل بيانات** (data_entry): إدخال وتعديل البيانات

---

## استخدام النظام

### 1. الوصول إلى النظام
- افتح المتصفح واذهب إلى: `http://localhost:5000`
- أو انقر على الرابط الذي يظهر عند تشغيل الخادم

### 2. الوحدات الرئيسية

#### أ. لوحة التحكم
- عرض الإحصائيات العامة
- الرسوم البيانية للإيرادات والمصروفات
- العقود المنتهية الصلاحية قريباً

#### ب. إدارة الملاك
- إضافة وتعديل بيانات الملاك
- ربط الملاك بالمباني

#### ج. إدارة العقارات
- إدارة المباني والوحدات
- طلبات الصيانة
- قراءات العدادات

#### د. إدارة المستأجرين
- بيانات المستأجرين
- العقود وإدارتها
- تجديد وإنهاء العقود

#### هـ. الإدارة المالية
- المعاملات المالية
- التقارير المالية
- متابعة المدفوعات

#### و. المستندات
- رفع وإدارة الملفات
- ربط المستندات بالعقود

#### ز. التقارير
- تقارير شاملة
- إيصالات الدفع

### 3. النسخ الاحتياطي
- جميع البيانات محفوظة في مجلد `instance/`
- الملفات المرفوعة في مجلد `uploads/`
- يُنصح بعمل نسخة احتياطية دورية لهذين المجلدين

---

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ "المنفذ مشغول"
```bash
# إعادة تشغيل التطبيق أو تغيير المنفذ
python server_manager.py --port 8080
```

#### 2. خطأ في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
# احذف ملف: instance/realestate.db
# ثم أعد تشغيل التطبيق
```

#### 3. مشاكل في الترميز العربي
- تأكد من أن النظام يدعم UTF-8
- استخدم متصفح حديث

#### 4. مشاكل في الواجهة الرسومية
```bash
# تثبيت PyQt5
pip install PyQt5

# أو استخدم وضع وحدة التحكم
python server_manager.py --console
```

---

## الدعم الفني

### للحصول على الدعم الفني:

#### 📱 واتساب
**0201032540807**

#### 📘 فيسبوك
**https://www.facebook.com/almbarmg**

#### 📧 البريد الإلكتروني
يرجى التواصل عبر الواتساب أو فيسبوك

### ساعات الدعم:
- **الأحد إلى الخميس**: 9:00 ص - 6:00 م
- **الجمعة والسبت**: حسب الحاجة

### معلومات مطلوبة عند طلب الدعم:
1. وصف المشكلة بالتفصيل
2. رسائل الخطأ (إن وجدت)
3. نظام التشغيل المستخدم
4. إصدار Python (إن أمكن)
5. خطوات إعادة إنتاج المشكلة

---

## حقوق الملكية

**جميع الحقوق محفوظة © 2025 - شركة المبرمج المصري**

هذا البرنامج محمي بحقوق الطبع والنشر. يُمنع نسخه أو توزيعه أو تعديله بدون إذن كتابي من المطور.

---

*تم إنشاء هذا الدليل بواسطة شركة المبرمج المصري*
