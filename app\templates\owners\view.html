{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">{{ owner.name }}</h1>
        {% if contract_debts %}
        <div class="mt-2">
            <span class="badge bg-danger p-2">
                <i class="fas fa-exclamation-triangle me-1"></i>
                مديونية: {{ total_debt|format_currency }}
            </span>
        </div>
        {% endif %}
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('owners.edit', owner_id=owner.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('owners.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        {% if contract_debts %}
        <div class="card mb-4 border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>المديونية
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h3 class="text-danger">{{ total_debt|format_currency }}</h3>
                    <p class="text-muted">إجمالي المديونية من رسوم العقود</p>
                </div>
                <div class="list-group">
                    {% for debt in contract_debts %}
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ debt.building_name }} - {{ debt.unit_number }}</h6>
                            <span class="badge {% if debt.contract_fee_status == 'partially_paid' %}bg-warning{% else %}bg-danger{% endif %}">
                                {{ debt.remaining_fee|format_currency }}
                            </span>
                        </div>
                        <p class="mb-1">
                            <small>
                                <strong>المستأجر:</strong> {{ debt.tenant_name }} |
                                <strong>العقد:</strong> {{ debt.contract_number or debt.id }} |
                                <strong>المدفوع:</strong> {{ debt.contract_fee_paid|format_currency if debt.contract_fee_paid else '0' }} من {{ debt.contract_fee|format_currency }}
                            </small>
                        </p>
                        <div class="mt-2">
                            <a href="{{ url_for('tenants.view_contract', contract_id=debt.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>عرض العقد
                            </a>
                            <a href="{{ url_for('finance.add_transaction') }}?related_to=owner&related_id={{ owner.id }}&amount={{ debt.remaining_fee }}&category=contract_fee&description=سداد رسوم عقد {{ debt.building_name }} - {{ debt.unit_number }}" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-money-bill-wave me-1"></i>تسجيل دفعة
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-tie me-2"></i>بيانات المالك
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>الاسم</th>
                            <td>{{ owner.name }}</td>
                        </tr>
                        <tr>
                            <th>رقم الهوية</th>
                            <td>{{ owner.id_number or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>رقم الهاتف</th>
                            <td>{{ owner.phone or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>البريد الإلكتروني</th>
                            <td>{{ owner.email or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الميلاد</th>
                            <td>{{ owner.birth_date|format_date if owner.birth_date else 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>العنوان</th>
                            <td>{{ owner.address or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>اسم البنك</th>
                            <td>{{ owner.bank_name or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>رقم الآيبان (IBAN)</th>
                            <td>
                                {% if owner.bank_iban %}
                                <div class="d-flex align-items-center">
                                    <span class="me-2">{{ owner.bank_iban }}</span>
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" data-clipboard-text="{{ owner.bank_iban }}" title="نسخ">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                {% else %}
                                غير متوفر
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ owner.notes or 'لا توجد ملاحظات' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>المباني
                </h5>
            </div>
            <div class="card-body">
                {% if buildings %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>اسم المبنى</th>
                                <th>العنوان</th>
                                <th>المدينة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for building in buildings %}
                            <tr>
                                <td>{{ building.name }}</td>
                                <td>{{ building.address }}</td>
                                <td>{{ building.city }}</td>
                                <td>
                                    <a href="{{ url_for('properties.view_building', building_id=building.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مباني مسجلة لهذا المالك.
                    <a href="{{ url_for('properties.add_building') }}?owner_id={{ owner.id }}" class="alert-link">إضافة مبنى جديد</a>
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('properties.add_building') }}?owner_id={{ owner.id }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>إضافة مبنى جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>المستندات
                </h5>
            </div>
            <div class="card-body">
                {% if documents %}
                <div class="row">
                    {% for document in documents %}
                    <div class="col-md-4 mb-3">
                        <div class="card document-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ document.title }}</h5>
                                <p class="card-text text-muted">
                                    <small>
                                        <i class="fas fa-calendar-alt me-1"></i>{{ document.upload_date|format_date }}
                                    </small>
                                </p>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('documents.display_file', document_id=document.id) }}" class="btn btn-sm btn-primary" target="_blank">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </a>
                                    <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-sm btn-success">
                                        <i class="fas fa-download me-1"></i>تنزيل
                                    </a>
                                    <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مستندات مرفقة لهذا المالك.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('documents.add') }}?related_to=owner&related_id={{ owner.id }}" class="btn btn-info">
                        <i class="fas fa-plus-circle me-1"></i>إضافة مستند جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>المعاملات المالية
                </h5>
            </div>
            <div class="card-body">
                {% if transactions %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الفئة</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.id }}</td>
                                <td>{{ transaction.transaction_date|format_date }}</td>
                                <td>
                                    {% if transaction.type == 'income' %}
                                    <span class="badge bg-success">إيراد</span>
                                    {% else %}
                                    <span class="badge bg-danger">مصروف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if transaction.category == 'rent' %}
                                    إيجار
                                    {% elif transaction.category == 'deposit' %}
                                    تأمين
                                    {% elif transaction.category == 'maintenance' %}
                                    صيانة
                                    {% elif transaction.category == 'utilities' %}
                                    مرافق
                                    {% elif transaction.category == 'taxes' %}
                                    ضرائب
                                    {% elif transaction.category == 'insurance' %}
                                    تأمين
                                    {% elif transaction.category == 'salary' %}
                                    رواتب
                                    {% elif transaction.category == 'commission' %}
                                    عمولة
                                    {% elif transaction.category == 'contract_fee' %}
                                    رسوم عقد
                                    {% else %}
                                    أخرى
                                    {% endif %}
                                </td>
                                <td>{{ transaction.description|truncate(30) }}</td>
                                <td>{{ transaction.amount|format_currency }}</td>
                                <td>
                                    {% if transaction.payment_method == 'cash' %}
                                    نقدي
                                    {% elif transaction.payment_method == 'bank_transfer' %}
                                    تحويل بنكي
                                    {% elif transaction.payment_method == 'check' %}
                                    شيك
                                    {% elif transaction.payment_method == 'credit_card' %}
                                    بطاقة ائتمان
                                    {% else %}
                                    {{ transaction.payment_method }}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('finance.view_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('finance.edit_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد معاملات مالية مرتبطة بهذا المالك.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('finance.add_transaction') }}?related_to=owner&related_id={{ owner.id }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>إضافة معاملة مالية جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة زر النسخ
        const copyButtons = document.querySelectorAll('.copy-btn');
        copyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const textToCopy = this.getAttribute('data-clipboard-text');

                // نسخ النص إلى الحافظة
                navigator.clipboard.writeText(textToCopy).then(() => {
                    // تغيير شكل الزر مؤقتًا للإشارة إلى نجاح النسخ
                    const originalHTML = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    this.classList.remove('btn-outline-secondary');
                    this.classList.add('btn-success');

                    // إعادة الزر إلى حالته الأصلية بعد ثانيتين
                    setTimeout(() => {
                        this.innerHTML = originalHTML;
                        this.classList.remove('btn-success');
                        this.classList.add('btn-outline-secondary');
                    }, 2000);
                }).catch(err => {
                    console.error('فشل في نسخ النص: ', err);
                    alert('فشل في نسخ النص. يرجى المحاولة مرة أخرى.');
                });
            });
        });
    });
</script>
{% endblock %}