{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">الملف الشخصي</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                <h4>{{ current_user.name }}</h4>
                <p class="text-muted">
                    {% if current_user.role == 'admin' %}
                    <span class="badge bg-danger">مدير</span>
                    {% elif current_user.role == 'accountant' %}
                    <span class="badge bg-success">محاسب</span>
                    {% elif current_user.role == 'data_entry' %}
                    <span class="badge bg-info">مدخل بيانات</span>
                    {% endif %}
                </p>
                <p>{{ current_user.email }}</p>
                <p class="text-muted">آخر تسجيل دخول: {{ current_user.last_login|format_date if current_user.last_login else 'غير معروف' }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">تعديل البيانات الشخصية</h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {% if form.username.errors %}
                            {{ form.username(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.username(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {% if form.name.errors %}
                            {{ form.name(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.name(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {% if form.email.errors %}
                            {{ form.email(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {% if form.password.errors %}
                            {{ form.password(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.password(class="form-control") }}
                        {% endif %}
                        <small class="form-text text-muted">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير كلمة المرور.</small>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.confirm_password.label(class="form-label") }}
                        {% if form.confirm_password.errors %}
                            {{ form.confirm_password(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.confirm_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.confirm_password(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    {{ form.submit(class="btn btn-primary") }}
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
