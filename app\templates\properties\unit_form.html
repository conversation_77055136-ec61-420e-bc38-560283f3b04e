{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.unit_number.label(class="form-label") }}
                        {% if form.unit_number.errors %}
                            {{ form.unit_number(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.unit_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.unit_number(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.building_id.label(class="form-label") }}
                        {% if form.building_id.errors %}
                            {{ form.building_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.building_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.building_id(class="form-select") }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.floor_number.label(class="form-label") }}
                            {% if form.floor_number.errors %}
                                {{ form.floor_number(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.floor_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.floor_number(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.type.label(class="form-label") }}
                            {% if form.type.errors %}
                                {{ form.type(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.type(class="form-control", list="unitTypeOptions") }}
                                <datalist id="unitTypeOptions">
                                    <option value="apartment">شقة</option>
                                    <option value="villa">فيلا</option>
                                    <option value="shop">محل تجاري</option>
                                    <option value="office">مكتب</option>
                                    <option value="warehouse">مستودع</option>
                                    <option value="studio">استديو</option>
                                    <option value="floor">دور كامل</option>
                                    <option value="penthouse">بنتهاوس</option>
                                    <option value="duplex">دوبلكس</option>
                                    <option value="chalet">شاليه</option>
                                    <option value="farm">مزرعة</option>
                                    <option value="land">أرض</option>
                                    <option value="other">أخرى</option>
                                </datalist>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.area.label(class="form-label") }}
                            {% if form.area.errors %}
                                {{ form.area(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.area.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.area(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.rooms_count.label(class="form-label") }}
                            {% if form.rooms_count.errors %}
                                {{ form.rooms_count(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.rooms_count.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.rooms_count(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.bathrooms_count.label(class="form-label") }}
                            {% if form.bathrooms_count.errors %}
                                {{ form.bathrooms_count(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.bathrooms_count.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.bathrooms_count(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.rent_amount.label(class="form-label") }}
                            {% if form.rent_amount.errors %}
                                {{ form.rent_amount(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.rent_amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.rent_amount(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {% if form.status.errors %}
                                {{ form.status(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.status(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>

                    <h4 class="mt-4 mb-3">بيانات العدادات</h4>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.site_number_electricity.label(class="form-label") }}
                            {% if form.site_number_electricity.errors %}
                                {{ form.site_number_electricity(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.site_number_electricity.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.site_number_electricity(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.meter_number_electricity.label(class="form-label") }}
                            {% if form.meter_number_electricity.errors %}
                                {{ form.meter_number_electricity(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.meter_number_electricity.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.meter_number_electricity(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.water_meter_number.label(class="form-label") }}
                            {% if form.water_meter_number.errors %}
                                {{ form.water_meter_number(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.water_meter_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.water_meter_number(class="form-control") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.water_meter_account.label(class="form-label") }}
                            {% if form.water_meter_account.errors %}
                                {{ form.water_meter_account(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.water_meter_account.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.water_meter_account(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.electricity_meter_type.label(class="form-label") }}
                            {% if form.electricity_meter_type.errors %}
                                {{ form.electricity_meter_type(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.electricity_meter_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.electricity_meter_type(class="form-select") }}
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.water_meter_type.label(class="form-label") }}
                            {% if form.water_meter_type.errors %}
                                {{ form.water_meter_type(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.water_meter_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.water_meter_type(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('properties.units') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
