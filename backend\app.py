#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نقطة دخول تطبيق Flask - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import logging
from pathlib import Path

# إعداد التسجيل
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backend_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('backend_app')

def setup_paths():
    """إعداد المسارات للتطبيق"""
    # تحديد المسار الأساسي للمشروع
    if getattr(sys, 'frozen', False):
        # في حالة التشغيل كملف تنفيذي
        base_path = Path(sys.executable).parent
        logger.info(f"Running as frozen application from: {base_path}")
    else:
        # في حالة التشغيل كسكريبت
        base_path = Path(__file__).parent.parent
        logger.info(f"Running as script from: {base_path}")
    
    # إضافة المسار الأساسي إلى sys.path
    if str(base_path) not in sys.path:
        sys.path.insert(0, str(base_path))
        logger.info(f"Added {base_path} to sys.path")
    
    # إضافة مجلد backend إلى sys.path
    backend_path = base_path / "backend"
    if str(backend_path) not in sys.path:
        sys.path.insert(0, str(backend_path))
        logger.info(f"Added {backend_path} to sys.path")
    
    # إنشاء المجلدات الضرورية
    required_dirs = [
        base_path / 'instance',
        base_path / 'uploads',
        base_path / 'uploads' / 'buildings',
        base_path / 'uploads' / 'contracts',
        base_path / 'uploads' / 'documents',
        base_path / 'uploads' / 'owners',
        base_path / 'uploads' / 'tenants',
        base_path / 'uploads' / 'transactions',
        base_path / 'logs'
    ]
    
    for directory in required_dirs:
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured directory exists: {directory}")
    
    return base_path

def apply_patches():
    """تطبيق الإصلاحات المطلوبة"""
    try:
        logger.info("Applying patches...")
        
        # تطبيق إصلاح Werkzeug
        import werkzeug.urls
        from urllib.parse import parse_qs, quote, quote_plus, unquote, unquote_plus
        
        if not hasattr(werkzeug.urls, 'url_decode'):
            logger.info("Adding url_decode to werkzeug.urls")
            
            def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
                """تنفيذ بديل لـ url_decode"""
                try:
                    from werkzeug.datastructures import MultiDict
                    result_cls = MultiDict
                except ImportError:
                    result_cls = dict
                
                if cls is not None:
                    result_cls = cls
                
                result = result_cls()
                parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)
                
                for key, values in parsed.items():
                    for value in values:
                        if hasattr(result, 'add'):
                            result.add(key, value)
                        else:
                            result[key] = value
                
                return result
            
            werkzeug.urls.url_decode = url_decode
            logger.info("Added url_decode to werkzeug.urls")
        
        # إضافة دوال أخرى مفقودة
        missing_functions = []
        for func_name in ['url_quote', 'url_quote_plus', 'url_unquote', 'url_unquote_plus']:
            if not hasattr(werkzeug.urls, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            logger.info(f"Adding missing functions to werkzeug.urls: {', '.join(missing_functions)}")
            
            if 'url_quote' in missing_functions:
                werkzeug.urls.url_quote = quote
            if 'url_quote_plus' in missing_functions:
                werkzeug.urls.url_quote_plus = quote_plus
            if 'url_unquote' in missing_functions:
                werkzeug.urls.url_unquote = unquote
            if 'url_unquote_plus' in missing_functions:
                werkzeug.urls.url_unquote_plus = unquote_plus
        
        logger.info("Patches applied successfully")
        
    except Exception as e:
        logger.error(f"Error applying patches: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def create_flask_app():
    """إنشاء تطبيق Flask"""
    try:
        # إعداد المسارات
        base_path = setup_paths()
        
        # تطبيق الإصلاحات
        apply_patches()
        
        # استيراد وإنشاء التطبيق
        from app import create_app
        
        # إنشاء التطبيق مع تحديد المسارات
        app = create_app()
        
        # تحديث إعدادات المسارات
        app.config['DATABASE_PATH'] = str(base_path / 'instance' / 'realestate.db')
        app.config['UPLOAD_FOLDER'] = str(base_path / 'uploads')
        
        logger.info("Flask app created successfully")
        return app
        
    except Exception as e:
        logger.error(f"Error creating Flask app: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise

def run_server(host='127.0.0.1', port=5000, debug=False):
    """تشغيل خادم Flask"""
    try:
        app = create_flask_app()
        
        logger.info(f"Starting Flask server on {host}:{port}")
        app.run(host=host, port=port, debug=debug, threaded=True)
        
    except Exception as e:
        logger.error(f"Error running server: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Flask Backend Server')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    run_server(host=args.host, port=args.port, debug=args.debug)
