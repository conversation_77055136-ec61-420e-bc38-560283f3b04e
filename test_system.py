#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار النظام - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path

def print_header():
    """طباعة رأس الصفحة"""
    print("=" * 60)
    print("اختبار النظام - مكتب عصام الفت لإدارة الأملاك")
    print("=" * 60)
    print("تم التطوير والبرمجة بواسطة شركة المبرمج المصري")
    print("فيسبوك: https://www.facebook.com/almbarmg")
    print("واتساب: 0201032540807")
    print("جميع الحقوق محفوظة © 2025")
    print("=" * 60)
    print()

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من إصدار Python...")
    
    version = sys.version_info
    print(f"   الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 8):
        print("   ✅ إصدار Python مناسب")
        return True
    else:
        print("   ❌ يتطلب Python 3.8 أو أحدث")
        return False

def check_required_modules():
    """التحقق من الوحدات المطلوبة"""
    print("\n📦 التحقق من الوحدات المطلوبة...")
    
    required_modules = [
        ('flask', 'Flask'),
        ('flask_login', 'Flask-Login'),
        ('flask_bcrypt', 'Flask-Bcrypt'),
        ('flask_wtf', 'Flask-WTF'),
        ('flask_mail', 'Flask-Mail'),
        ('werkzeug', 'Werkzeug'),
        ('jinja2', 'Jinja2'),
        ('wtforms', 'WTForms'),
        ('email_validator', 'Email-Validator'),
        ('itsdangerous', 'ItsDangerous'),
        ('markupsafe', 'MarkupSafe'),
        ('bcrypt', 'bcrypt'),
        ('sqlite3', 'SQLite3'),
        ('qrcode', 'QRCode'),
        ('PIL', 'Pillow')
    ]
    
    optional_modules = [
        ('PyQt5', 'PyQt5'),
        ('pdfkit', 'PDFKit')
    ]
    
    missing_required = []
    missing_optional = []
    
    # التحقق من الوحدات المطلوبة
    for module_name, display_name in required_modules:
        try:
            importlib.import_module(module_name)
            print(f"   ✅ {display_name}")
        except ImportError:
            print(f"   ❌ {display_name} - مفقود")
            missing_required.append(display_name)
    
    # التحقق من الوحدات الاختيارية
    for module_name, display_name in optional_modules:
        try:
            importlib.import_module(module_name)
            print(f"   ✅ {display_name} (اختياري)")
        except ImportError:
            print(f"   ⚠️  {display_name} (اختياري) - مفقود")
            missing_optional.append(display_name)
    
    return missing_required, missing_optional

def check_project_structure():
    """التحقق من هيكل المشروع"""
    print("\n📁 التحقق من هيكل المشروع...")
    
    required_files = [
        'app/__init__.py',
        'app/models.py',
        'app/db.py',
        'config.py',
        'schema.sql',
        'server_manager.py',
        'requirements.txt'
    ]
    
    required_dirs = [
        'app',
        'app/auth',
        'app/dashboard',
        'app/owners',
        'app/properties',
        'app/tenants',
        'app/finance',
        'app/documents',
        'app/reports',
        'app/templates',
        'app/static'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # التحقق من الملفات
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    # التحقق من المجلدات
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"   ✅ {dir_path}/")
        else:
            print(f"   ❌ {dir_path}/ - مفقود")
            missing_dirs.append(dir_path)
    
    return missing_files, missing_dirs

def check_database():
    """التحقق من قاعدة البيانات"""
    print("\n🗄️ التحقق من قاعدة البيانات...")
    
    db_path = Path("instance/realestate.db")
    
    if db_path.exists():
        print(f"   ✅ قاعدة البيانات موجودة: {db_path}")
        
        # التحقق من حجم قاعدة البيانات
        size = db_path.stat().st_size
        print(f"   📊 حجم قاعدة البيانات: {size:,} بايت")
        
        return True
    else:
        print(f"   ⚠️  قاعدة البيانات غير موجودة: {db_path}")
        print("   💡 سيتم إنشاؤها تلقائياً عند التشغيل الأول")
        return False

def check_uploads_directory():
    """التحقق من مجلد التحميلات"""
    print("\n📤 التحقق من مجلد التحميلات...")
    
    uploads_dir = Path("uploads")
    
    if uploads_dir.exists():
        print(f"   ✅ مجلد التحميلات موجود: {uploads_dir}")
        
        # عد الملفات
        file_count = sum(1 for _ in uploads_dir.rglob('*') if _.is_file())
        print(f"   📁 عدد الملفات: {file_count}")
        
        return True
    else:
        print(f"   ⚠️  مجلد التحميلات غير موجود: {uploads_dir}")
        print("   💡 سيتم إنشاؤه تلقائياً عند التشغيل الأول")
        return False

def test_import_app():
    """اختبار استيراد التطبيق"""
    print("\n🧪 اختبار استيراد التطبيق...")
    
    try:
        # محاولة استيراد التطبيق
        from app import create_app
        print("   ✅ تم استيراد create_app بنجاح")
        
        # محاولة إنشاء التطبيق
        app = create_app()
        print("   ✅ تم إنشاء التطبيق بنجاح")
        
        # التحقق من الإعدادات
        print(f"   📋 اسم التطبيق: {app.name}")
        print(f"   🔧 وضع التطوير: {app.debug}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استيراد التطبيق: {str(e)}")
        return False

def run_quick_test():
    """تشغيل اختبار سريع للخادم"""
    print("\n🚀 اختبار سريع للخادم...")
    
    try:
        # محاولة تشغيل الخادم لثوانٍ قليلة
        import threading
        import time
        import socket
        
        from app import create_app
        
        app = create_app()
        
        # التحقق من توفر المنفذ
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("   ⚠️  المنفذ 5000 مشغول")
            return False
        
        print("   ✅ المنفذ 5000 متاح")
        print("   💡 الخادم جاهز للتشغيل")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الخادم: {str(e)}")
        return False

def generate_report(results):
    """إنشاء تقرير الاختبار"""
    print("\n📋 تقرير الاختبار:")
    print("-" * 40)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"إجمالي الاختبارات: {total_tests}")
    print(f"الاختبارات الناجحة: {passed_tests}")
    print(f"الاختبارات الفاشلة: {total_tests - passed_tests}")
    print(f"معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nتفاصيل الاختبارات:")
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للتشغيل.")
    else:
        print("\n⚠️  بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    results = {}
    
    # تشغيل الاختبارات
    results['Python Version'] = check_python_version()
    
    missing_req, missing_opt = check_required_modules()
    results['Required Modules'] = len(missing_req) == 0
    
    missing_files, missing_dirs = check_project_structure()
    results['Project Structure'] = len(missing_files) == 0 and len(missing_dirs) == 0
    
    results['Database'] = check_database()
    results['Uploads Directory'] = check_uploads_directory()
    results['App Import'] = test_import_app()
    results['Server Test'] = run_quick_test()
    
    # إنشاء التقرير
    generate_report(results)
    
    # اقتراحات للإصلاح
    if not all(results.values()):
        print("\n🔧 اقتراحات للإصلاح:")
        
        if missing_req:
            print("   📦 لتثبيت الوحدات المفقودة:")
            print("      pip install -r requirements.txt")
        
        if missing_files or missing_dirs:
            print("   📁 تأكد من وجود جميع ملفات المشروع")
        
        if not results['App Import']:
            print("   🐍 تحقق من أخطاء Python في الكود")
    
    print("\n" + "=" * 60)
    print("انتهى الاختبار")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
