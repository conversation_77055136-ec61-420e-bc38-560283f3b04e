{% extends "base.html" %}

{% block title %}إدارة المباني - نظام إدارة العقارات{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* تحسينات عامة */
    .card {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .card-header {
        border-top-left-radius: 0.5rem !important;
        border-top-right-radius: 0.5rem !important;
    }

    /* تنسيق الجدول */
    .table th {
        font-weight: 600;
        white-space: nowrap;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    /* تنسيق البطاقات */
    .bg-opacity-10 {
        --bs-bg-opacity: 0.1;
    }

    .rounded-circle {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 767.98px) {
        .card-title {
            font-size: 1.1rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .mobile-action-buttons .dropdown-menu {
            min-width: 200px;
        }

        .list-group-item {
            padding: 0.75rem;
        }
    }

    /* تنسيق للشاشات المتوسطة والكبيرة */
    @media (min-width: 768px) {
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table th, .table td {
            vertical-align: middle;
        }
    }

    /* تأثيرات متحركة */
    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.1);
    }

    /* تنسيق الأيقونات */
    .fa-eye, .fa-info {
        color: #17a2b8;
    }

    .fa-edit, .fa-pencil-alt {
        color: #007bff;
    }

    .fa-trash-alt {
        color: #dc3545;
    }

    /* تنسيق الروابط */
    a {
        text-decoration: none;
    }

    /* تنسيق الفورم */
    .form-control:focus, .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تنسيق الأزرار المجمعة */
    .btn-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
    }

    /* تنسيق خاص بالمباني */
    .building-card {
        transition: all 0.3s ease;
    }

    .building-card:hover {
        transform: translateY(-5px);
    }

    .building-icon {
        font-size: 2rem;
        color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة المباني</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('properties.add_building') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة مبنى جديد
        </a>
    </div>
</div>

<div class="card mb-4 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-search me-2"></i>بحث وفلترة
        </h5>
        <button class="btn btn-sm btn-outline-primary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse">
            <i class="fas fa-filter me-1"></i>عرض/إخفاء الفلاتر
        </button>
    </div>
    <div class="card-body collapse show" id="searchCollapse">
        <form method="GET" action="{{ url_for('properties.buildings') }}" id="searchForm">
            <div class="row g-3">
                <!-- حقل البحث - يظهر دائماً بعرض كامل -->
                <div class="col-12">
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="ابحث في الاسم، العنوان، رقم العقار..."
                               value="{{ search_query }}">
                        <button type="submit" class="btn btn-primary">
                            بحث
                        </button>
                        <a href="{{ url_for('properties.buildings') }}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i>
                        </a>
                    </div>
                </div>

                <!-- الفلاتر -->
                <div class="col-12 col-md-6 col-lg-4">
                    <label for="owner_id" class="form-label">المالك</label>
                    <select class="form-select form-select-sm" id="owner_id" name="owner_id">
                        <option value="">الكل</option>
                        {% for owner in owners_list %}
                        <option value="{{ owner.id }}" {% if owner_id == owner.id|string %}selected{% endif %}>{{ owner.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-4">
                    <label for="city" class="form-label">المدينة</label>
                    <select class="form-select form-select-sm" id="city" name="city">
                        <option value="">الكل</option>
                        {% for city_option in cities %}
                        <option value="{{ city_option }}" {% if city == city_option %}selected{% endif %}>{{ city_option }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-4">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select form-select-sm" id="sort_by" name="sort_by">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>الاسم</option>
                        <option value="building_code" {% if sort_by == 'building_code' %}selected{% endif %}>رقم العقار</option>
                        <option value="city" {% if sort_by == 'city' %}selected{% endif %}>المدينة</option>
                        <option value="owner" {% if sort_by == 'owner' %}selected{% endif %}>المالك</option>
                    </select>
                </div>

                <div class="col-12 col-md-6 col-lg-4">
                    <label for="display_mode" class="form-label">طريقة العرض</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="display_mode" id="table_mode" value="table" {% if display_mode == 'table' or not display_mode %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="table_mode">
                            <i class="fas fa-table me-1"></i>جدول
                        </label>

                        <input type="radio" class="btn-check" name="display_mode" id="cards_mode" value="cards" {% if display_mode == 'cards' %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="cards_mode">
                            <i class="fas fa-th-large me-1"></i>بطاقات
                        </label>
                    </div>
                </div>
            </div>

            <!-- أزرار إضافية للشاشات المتوسطة والكبيرة -->
            <div class="d-none d-md-flex justify-content-end mt-3">
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>تطبيق الفلاتر
                    </button>
                    <a href="{{ url_for('properties.buildings') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i>إعادة تعيين
                    </a>
                    <a href="{{ url_for('properties.add_building') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة مبنى جديد
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-building me-2"></i>قائمة المباني
        </h5>
        <span class="badge bg-primary">{{ buildings|length }} مبنى</span>
    </div>
    <div class="card-body">
        {% if buildings %}
            {% if display_mode == 'table' or not display_mode %}
            <!-- عرض الجدول -->
            <!-- جدول للشاشات المتوسطة والكبيرة -->
            <div class="table-responsive d-none d-md-block">
                <table class="table table-striped table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">#</th>
                            <th>رقم العقار</th>
                            <th>اسم المبنى</th>
                            <th>المالك</th>
                            <th>العنوان</th>
                            <th>المدينة</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for building in buildings %}
                        <tr>
                            <td class="text-center">{{ building.id }}</td>
                            <td>
                                {% if building.building_code %}
                                <span class="badge bg-primary">{{ building.building_code }}</span>
                                {% else %}
                                <span class="badge bg-secondary">غير محدد</span>
                                {% endif %}
                            </td>
                            <td><strong>{{ building.name }}</strong></td>
                            <td>
                                <a href="{{ url_for('owners.view', owner_id=building.owner_id) }}" class="text-decoration-none">
                                    <i class="fas fa-user text-primary me-1"></i>{{ building.owner_name }}
                                </a>
                            </td>
                            <td>
                                <span title="{{ building.address }}">{{ building.address|truncate(30) }}</span>
                            </td>
                            <td>
                                <i class="fas fa-map-marker-alt text-danger me-1"></i>{{ building.city }}
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('properties.view_building', building_id=building.id) }}" class="btn btn-sm btn-info" title="عرض التفاصيل" data-bs-toggle="tooltip">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('properties.edit_building', building_id=building.id) }}" class="btn btn-sm btn-primary" title="تعديل" data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteModal{{ building.id }}"
                                            title="حذف" data-bs-toggle="tooltip">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>

                                <!-- Modal for delete confirmation -->
                                <div class="modal fade" id="deleteModal{{ building.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ building.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ building.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف المبنى <strong>{{ building.name }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('properties.delete_building', building_id=building.id) }}" method="POST" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- قائمة للشاشات الصغيرة (الهواتف) -->
            <div class="d-md-none">
                {% for building in buildings %}
                <div class="card mb-3 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            {% if building.building_code %}
                            <span class="badge bg-primary me-1">{{ building.building_code }}</span>
                            {% endif %}
                            {{ building.name }}
                        </h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('properties.view_building', building_id=building.id) }}">
                                        <i class="fas fa-eye text-info me-2"></i>عرض التفاصيل
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url_for('properties.edit_building', building_id=building.id) }}">
                                        <i class="fas fa-edit text-primary me-2"></i>تعديل
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-danger" type="button" data-bs-toggle="modal" data-bs-target="#deleteModalMobile{{ building.id }}">
                                        <i class="fas fa-trash-alt me-2"></i>حذف
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-user text-primary me-2"></i>المالك:</span>
                                <a href="{{ url_for('owners.view', owner_id=building.owner_id) }}" class="text-decoration-none">{{ building.owner_name }}</a>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-map-marker-alt text-danger me-2"></i>المدينة:</span>
                                <span>{{ building.city }}</span>
                            </li>
                            <li class="list-group-item">
                                <span><i class="fas fa-map text-success me-2"></i>العنوان:</span>
                                <div class="mt-1 text-muted">{{ building.address }}</div>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ url_for('properties.view_building', building_id=building.id) }}" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>عرض
                        </a>
                        <a href="{{ url_for('properties.edit_building', building_id=building.id) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                    </div>

                    <!-- Modal for mobile delete confirmation -->
                    <div class="modal fade" id="deleteModalMobile{{ building.id }}" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">تأكيد الحذف</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    هل أنت متأكد من رغبتك في حذف المبنى <strong>{{ building.name }}</strong>؟
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                    <form action="{{ url_for('properties.delete_building', building_id=building.id) }}" method="POST">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger">حذف</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <!-- عرض البطاقات -->
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                {% for building in buildings %}
                <div class="col">
                    <div class="card h-100 shadow-sm building-card">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                {% if building.building_code %}
                                <span class="badge bg-primary me-1">{{ building.building_code }}</span>
                                {% endif %}
                                {{ building.name }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-building building-icon"></i>
                            </div>
                            <ul class="list-group list-group-flush mb-3">
                                <li class="list-group-item d-flex justify-content-between">
                                    <span><i class="fas fa-user text-primary me-2"></i>المالك:</span>
                                    <a href="{{ url_for('owners.view', owner_id=building.owner_id) }}" class="text-decoration-none">{{ building.owner_name }}</a>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    <span><i class="fas fa-map-marker-alt text-danger me-2"></i>المدينة:</span>
                                    <span>{{ building.city }}</span>
                                </li>
                                <li class="list-group-item">
                                    <span><i class="fas fa-map text-success me-2"></i>العنوان:</span>
                                    <div class="mt-1 text-muted">{{ building.address }}</div>
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('properties.view_building', building_id=building.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                <a href="{{ url_for('properties.edit_building', building_id=building.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModalCard{{ building.id }}">
                                    <i class="fas fa-trash-alt me-1"></i>حذف
                                </button>
                            </div>

                            <!-- Modal for card delete confirmation -->
                            <div class="modal fade" id="deleteModalCard{{ building.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من رغبتك في حذف المبنى <strong>{{ building.name }}</strong>؟
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('properties.delete_building', building_id=building.id) }}" method="POST">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        {% else %}
        <div class="alert alert-info">
            لا توجد مباني حتى الآن. <a href="{{ url_for('properties.add_building') }}" class="btn btn-sm btn-primary ms-2">إضافة مبنى جديد</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث النموذج عند تغيير أي حقل (باستثناء حقل البحث)
        const autoSubmitElements = document.querySelectorAll('#searchForm select, #searchForm input[type="radio"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', function() {
                // إظهار مؤشر التحميل
                const submitBtn = document.querySelector('#searchForm button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التحميل...';
                    submitBtn.disabled = true;
                }

                // تأخير التقديم قليلاً للسماح بتحديث القيم
                setTimeout(() => {
                    document.getElementById('searchForm').submit();
                }, 100);
            });
        });

        // إضافة تأثير تمييز للصفوف عند التمرير فوقها
        const tableRows = document.querySelectorAll('table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('table-active');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('table-active');
            });
        });

        // تفعيل البحث الفوري عند الكتابة في حقل البحث
        const searchInput = document.getElementById('search');
        let searchTimeout;
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 3 || this.value.length === 0) {
                        document.getElementById('searchForm').submit();
                    }
                }, 500);
            });
        }

        // تفعيل التلميحات (tooltips)
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // تأثيرات للبطاقات
        const buildingCards = document.querySelectorAll('.building-card');
        buildingCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('shadow');
            });
            card.addEventListener('mouseleave', function() {
                this.classList.remove('shadow');
            });
        });

        // تفعيل مودال التأكيد للحذف
        const deleteButtons = document.querySelectorAll('.confirm-delete');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm(this.getAttribute('data-confirm-message'))) {
                    e.preventDefault();
                }
            });
        });
    });
</script>
{% endblock %}