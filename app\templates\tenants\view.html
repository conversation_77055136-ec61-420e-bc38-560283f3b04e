{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">{{ tenant.name }}</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('tenants.edit', tenant_id=tenant.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        <a href="{{ url_for('tenants.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>رجوع
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>بيانات المستأجر
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <th>الاسم</th>
                            <td>{{ tenant.name }}</td>
                        </tr>
                        <tr>
                            <th>رقم الهوية</th>
                            <td>{{ tenant.id_number or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>رقم الهاتف</th>
                            <td>{{ tenant.phone }}</td>
                        </tr>
                        <tr>
                            <th>البريد الإلكتروني</th>
                            <td>{{ tenant.email or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>المهنة</th>
                            <td>{{ tenant.occupation or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>جهة العمل</th>
                            <td>{{ tenant.employer or 'غير متوفر' }}</td>
                        </tr>

                        <tr>
                            <th>جهة اتصال للطوارئ</th>
                            <td>{{ tenant.emergency_contact or 'غير متوفر' }}</td>
                        </tr>
                        <tr>
                            <th>ملاحظات</th>
                            <td>{{ tenant.notes or 'لا توجد ملاحظات' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-contract me-2"></i>العقود النشطة
                </h5>
            </div>
            <div class="card-body">
                {% if active_contracts %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم العقد</th>
                                <th>الوحدة</th>
                                <th>المبنى</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>قيمة الإيجار</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contract in active_contracts %}
                            <tr>
                                <td>{{ contract.contract_number or contract.id }}</td>
                                <td>{{ contract.unit_number }}</td>
                                <td>{{ contract.building_name }}</td>
                                <td>{{ contract.start_date|format_date }}</td>
                                <td>{{ contract.end_date|format_date }}</td>
                                <td>{{ contract.rent_amount|format_currency }}</td>
                                <td>
                                    <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد عقود نشطة لهذا المستأجر.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('tenants.add_contract') }}?tenant_id={{ tenant.id }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>إضافة عقد جديد
                    </a>
                </div>
            </div>
        </div>

        {% if contracts_history %}
        <div class="card mt-3">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>سجل العقود السابقة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>رقم العقد</th>
                                <th>الوحدة</th>
                                <th>المبنى</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>قيمة الإيجار</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contract in contracts_history %}
                            <tr>
                                <td>{{ contract.contract_number or contract.id }}</td>
                                <td>{{ contract.unit_number }}</td>
                                <td>{{ contract.building_name }}</td>
                                <td>{{ contract.start_date|format_date }}</td>
                                <td>{{ contract.end_date|format_date }}</td>
                                <td>{{ contract.rent_amount|format_currency }}</td>
                                <td>
                                    {% if contract.status == 'active' %}
                                    <span class="badge bg-success">نشط</span>
                                    {% elif contract.status == 'expired' %}
                                    <span class="badge bg-danger">منتهي</span>
                                    {% elif contract.status == 'terminated' %}
                                    <span class="badge bg-warning">ملغي</span>
                                    {% elif contract.status == 'renewed' %}
                                    <span class="badge bg-info">مجدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('tenants.view_contract', contract_id=contract.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>المستندات
                </h5>
            </div>
            <div class="card-body">
                {% if documents %}
                <div class="row">
                    {% for document in documents %}
                    <div class="col-md-3 mb-3">
                        <div class="card document-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ document.title }}</h5>
                                <p class="card-text text-muted">
                                    <small>
                                        <i class="fas fa-calendar-alt me-1"></i>{{ document.upload_date|format_date }}
                                    </small>
                                </p>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>تنزيل
                                    </a>
                                    <form action="{{ url_for('documents.delete', document_id=document.id) }}" method="POST" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger confirm-delete" data-confirm-message="هل أنت متأكد من رغبتك في حذف هذا المستند؟">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد مستندات مرفقة لهذا المستأجر.
                </div>
                {% endif %}
                <div class="mt-3">
                    <a href="{{ url_for('documents.add') }}?related_to=tenant&related_id={{ tenant.id }}" class="btn btn-info">
                        <i class="fas fa-plus-circle me-1"></i>إضافة مستند جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
