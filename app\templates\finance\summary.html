{% extends "base.html" %}

{% block title %}الملخص المالي - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">الملخص المالي</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('finance.transactions') }}" class="btn btn-primary">
            <i class="fas fa-list me-1"></i>عرض المعاملات
        </a>
        <a href="{{ url_for('reports.financial_report') }}" class="btn btn-info">
            <i class="fas fa-file-export me-1"></i>تصدير التقرير
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>تصفية النتائج
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('finance.summary') }}">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="period" class="form-label">الفترة</label>
                    <select name="period" id="period" class="form-select">
                        <option value="month" {% if request.args.get('period') == 'month' %}selected{% endif %}>شهري</option>
                        <option value="quarter" {% if request.args.get('period') == 'quarter' %}selected{% endif %}>ربع سنوي</option>
                        <option value="year" {% if request.args.get('period') == 'year' %}selected{% endif %}>سنوي</option>
                        <option value="custom" {% if request.args.get('period') == 'custom' %}selected{% endif %}>مخصص</option>
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request.args.get('date_from', '') }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request.args.get('date_to', '') }}">
                </div>
            </div>
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{{ url_for('finance.summary') }}" class="btn btn-secondary ms-2">
                    <i class="fas fa-redo me-1"></i>إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<!-- ملخص الإيرادات والمصروفات -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>إجمالي الإيرادات
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="text-success">{{ total_income|format_currency }}</h3>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>إجمالي المصروفات
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="text-danger">{{ total_expense|format_currency }}</h3>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>صافي الربح
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="{% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ net_profit|format_currency }}
                </h3>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- الرسم البياني للإيرادات والمصروفات -->
    <div class="col-md-8 mb-3">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>الإيرادات والمصروفات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="incomeExpenseChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- توزيع الإيرادات حسب المباني -->
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>توزيع الإيرادات حسب المباني
                </h5>
            </div>
            <div class="card-body">
                {% if income_by_building %}
                <canvas id="buildingIncomeChart" height="300"></canvas>
                {% else %}
                <div class="alert alert-info">
                    لا توجد بيانات إيرادات للمباني في هذه الفترة.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- توزيع الإيرادات حسب الفئة -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>توزيع الإيرادات حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                {% if income_by_category %}
                <canvas id="incomeCategoryChart" height="300"></canvas>
                {% else %}
                <div class="alert alert-info">
                    لا توجد بيانات إيرادات في هذه الفترة.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- توزيع المصروفات حسب الفئة -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>توزيع المصروفات حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                {% if expense_by_category %}
                <canvas id="expenseCategoryChart" height="300"></canvas>
                {% else %}
                <div class="alert alert-info">
                    لا توجد بيانات مصروفات في هذه الفترة.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- جدول الإيرادات حسب الفئة -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>الإيرادات حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                {% if income_by_category %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الفئة</th>
                                <th>المبلغ</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in income_by_category %}
                            <tr>
                                <td>
                                    {% if item.category == 'rent' %}
                                    إيجار
                                    {% elif item.category == 'deposit' %}
                                    تأمين
                                    {% elif item.category == 'other' %}
                                    أخرى
                                    {% else %}
                                    {{ item.category }}
                                    {% endif %}
                                </td>
                                <td>{{ item.total|format_currency }}</td>
                                <td>{{ ((item.total / total_income) * 100)|round(1) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-success">
                                <th>الإجمالي</th>
                                <th>{{ total_income|format_currency }}</th>
                                <th>100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد بيانات إيرادات في هذه الفترة.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- جدول المصروفات حسب الفئة -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>المصروفات حسب الفئة
                </h5>
            </div>
            <div class="card-body">
                {% if expense_by_category %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الفئة</th>
                                <th>المبلغ</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in expense_by_category %}
                            <tr>
                                <td>
                                    {% if item.category == 'maintenance' %}
                                    صيانة
                                    {% elif item.category == 'utilities' %}
                                    مرافق
                                    {% elif item.category == 'taxes' %}
                                    ضرائب
                                    {% elif item.category == 'insurance' %}
                                    تأمين
                                    {% elif item.category == 'salary' %}
                                    رواتب
                                    {% elif item.category == 'commission' %}
                                    عمولة
                                    {% elif item.category == 'other' %}
                                    أخرى
                                    {% else %}
                                    {{ item.category }}
                                    {% endif %}
                                </td>
                                <td>{{ item.total|format_currency }}</td>
                                <td>{{ ((item.total / total_expense) * 100)|round(1) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-danger">
                                <th>الإجمالي</th>
                                <th>{{ total_expense|format_currency }}</th>
                                <th>100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد بيانات مصروفات في هذه الفترة.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // رسم بياني للإيرادات والمصروفات
        var incomeExpenseCtx = document.getElementById('incomeExpenseChart').getContext('2d');
        var incomeExpenseChart = new Chart(incomeExpenseCtx, {
            type: 'bar',
            data: {
                labels: {{ time_labels|tojson }},
                datasets: [
                    {
                        label: 'الإيرادات',
                        data: {{ income_data|tojson }},
                        backgroundColor: 'rgba(40, 167, 69, 0.7)',
                        borderColor: 'rgb(40, 167, 69)',
                        borderWidth: 1
                    },
                    {
                        label: 'المصروفات',
                        data: {{ expense_data|tojson }},
                        backgroundColor: 'rgba(220, 53, 69, 0.7)',
                        borderColor: 'rgb(220, 53, 69)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        {% if income_by_building %}
        // رسم بياني لتوزيع الإيرادات حسب المباني
        var buildingIncomeCtx = document.getElementById('buildingIncomeChart').getContext('2d');
        var buildingIncomeChart = new Chart(buildingIncomeCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for item in income_by_building %}
                    '{{ item.building_name }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in income_by_building %}
                        {{ item.total }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderColor: [
                        'rgb(40, 167, 69)',
                        'rgb(0, 123, 255)',
                        'rgb(255, 193, 7)',
                        'rgb(23, 162, 184)',
                        'rgb(111, 66, 193)',
                        'rgb(220, 53, 69)',
                        'rgb(108, 117, 125)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
        {% endif %}
        
        {% if income_by_category %}
        // رسم بياني لتوزيع الإيرادات حسب الفئة
        var incomeCategoryCtx = document.getElementById('incomeCategoryChart').getContext('2d');
        var incomeCategoryChart = new Chart(incomeCategoryCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for item in income_by_category %}
                    {% if item.category == 'rent' %}
                    'إيجار',
                    {% elif item.category == 'deposit' %}
                    'تأمين',
                    {% elif item.category == 'other' %}
                    'أخرى',
                    {% else %}
                    '{{ item.category }}',
                    {% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in income_by_category %}
                        {{ item.total }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderColor: [
                        'rgb(40, 167, 69)',
                        'rgb(0, 123, 255)',
                        'rgb(255, 193, 7)',
                        'rgb(23, 162, 184)',
                        'rgb(111, 66, 193)',
                        'rgb(220, 53, 69)',
                        'rgb(108, 117, 125)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
        {% endif %}
        
        {% if expense_by_category %}
        // رسم بياني لتوزيع المصروفات حسب الفئة
        var expenseCategoryCtx = document.getElementById('expenseCategoryChart').getContext('2d');
        var expenseCategoryChart = new Chart(expenseCategoryCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for item in expense_by_category %}
                    {% if item.category == 'maintenance' %}
                    'صيانة',
                    {% elif item.category == 'utilities' %}
                    'مرافق',
                    {% elif item.category == 'taxes' %}
                    'ضرائب',
                    {% elif item.category == 'insurance' %}
                    'تأمين',
                    {% elif item.category == 'salary' %}
                    'رواتب',
                    {% elif item.category == 'commission' %}
                    'عمولة',
                    {% elif item.category == 'other' %}
                    'أخرى',
                    {% else %}
                    '{{ item.category }}',
                    {% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in expense_by_category %}
                        {{ item.total }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderColor: [
                        'rgb(220, 53, 69)',
                        'rgb(255, 193, 7)',
                        'rgb(23, 162, 184)',
                        'rgb(0, 123, 255)',
                        'rgb(111, 66, 193)',
                        'rgb(40, 167, 69)',
                        'rgb(108, 117, 125)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
        {% endif %}
        
        // تفعيل حقول التاريخ المخصصة عند اختيار "مخصص"
        const periodSelect = document.getElementById('period');
        const dateFromInput = document.getElementById('date_from');
        const dateToInput = document.getElementById('date_to');
        
        function toggleDateFields() {
            const isCustom = periodSelect.value === 'custom';
            dateFromInput.disabled = !isCustom;
            dateToInput.disabled = !isCustom;
        }
        
        periodSelect.addEventListener('change', toggleDateFields);
        toggleDateFields();
    });
</script>
{% endblock %}
