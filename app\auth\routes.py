from flask import render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from app.auth import auth_bp
from app.forms import LoginForm, UserForm, EditUserForm
from app.models import User
from app.db import query_db, insert_db, update_db, delete_db
from app.decorators import admin_required
from datetime import datetime

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.get_by_username(form.username.data)
        if user and user.check_password(form.password.data):
            login_user(user)
            user.update_last_login()
            flash('تم تسجيل الدخول بنجاح!', 'success')
            next_page = request.args.get('next')
            return redirect(next_page or url_for('dashboard.index'))
        else:
            flash('فشل تسجيل الدخول. يرجى التحقق من اسم المستخدم وكلمة المرور.', 'danger')

    return render_template('auth/login.html', form=form, title='تسجيل الدخول')

@auth_bp.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'success')
    return redirect(url_for('auth.login'))

@auth_bp.route('/users')
@login_required
@admin_required
def users():
    """قائمة المستخدمين"""
    users_list = query_db('SELECT * FROM users ORDER BY username')
    return render_template('auth/users.html', users=users_list, title='إدارة المستخدمين')

@auth_bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    form = UserForm()

    if form.validate_on_submit():
        User.create(
            username=form.username.data,
            password=form.password.data,
            name=form.name.data,
            email=form.email.data,
            role=form.role.data
        )
        flash('تم إضافة المستخدم بنجاح!', 'success')
        return redirect(url_for('auth.users'))

    return render_template('auth/user_form.html', form=form, title='إضافة مستخدم جديد')

@auth_bp.route('/users/edit/<int:user_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """تعديل مستخدم"""
    user_data = query_db('SELECT * FROM users WHERE id = ?', (user_id,), one=True)

    if not user_data:
        flash('المستخدم غير موجود.', 'danger')
        return redirect(url_for('auth.users'))

    form = EditUserForm()

    if request.method == 'GET':
        form.id.data = user_data['id']
        form.username.data = user_data['username']
        form.name.data = user_data['name']
        form.email.data = user_data['email']
        form.role.data = user_data['role']

    if form.validate_on_submit():
        data = {
            'username': form.username.data,
            'name': form.name.data,
            'email': form.email.data,
            'role': form.role.data
        }

        # تحديث كلمة المرور فقط إذا تم إدخالها
        if form.password.data:
            from app import bcrypt
            data['password'] = bcrypt.generate_password_hash(form.password.data).decode('utf-8')

        # تحديث البيانات في قاعدة البيانات
        update_db('users', user_id, data)

        # إذا كان المستخدم الذي يتم تعديله هو المستخدم الحالي، قم بتحديث بياناته في الجلسة
        if user_id == current_user.id:
            from flask_login import logout_user, login_user
            updated_user = User.get_by_id(user_id)
            if updated_user:
                logout_user()
                login_user(updated_user)

        flash('تم تحديث المستخدم بنجاح!', 'success')
        return redirect(url_for('auth.users'))

    return render_template('auth/user_form.html', form=form, title='تعديل المستخدم', user=user_data)

@auth_bp.route('/users/delete/<int:user_id>', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """حذف مستخدم"""
    user_data = query_db('SELECT * FROM users WHERE id = ?', (user_id,), one=True)

    if not user_data:
        flash('المستخدم غير موجود.', 'danger')
        return redirect(url_for('auth.users'))

    # لا يمكن حذف المستخدم الحالي
    if user_id == current_user.id:
        flash('لا يمكنك حذف حسابك الحالي.', 'danger')
        return redirect(url_for('auth.users'))

    delete_db('users', user_id)
    flash('تم حذف المستخدم بنجاح!', 'success')
    return redirect(url_for('auth.users'))

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """الملف الشخصي للمستخدم الحالي"""
    form = EditUserForm()

    if request.method == 'GET':
        form.id.data = current_user.id
        form.username.data = current_user.username
        form.name.data = current_user.name
        form.email.data = current_user.email
        form.role.data = current_user.role

    if form.validate_on_submit():
        data = {
            'username': form.username.data,
            'name': form.name.data,
            'email': form.email.data
        }

        # تحديث كلمة المرور فقط إذا تم إدخالها
        if form.password.data:
            from app import bcrypt
            data['password'] = bcrypt.generate_password_hash(form.password.data).decode('utf-8')

        # تحديث البيانات في قاعدة البيانات
        update_db('users', current_user.id, data)

        # تحديث بيانات المستخدم الحالي في الجلسة
        from flask_login import logout_user, login_user
        updated_user = User.get_by_id(current_user.id)
        if updated_user:
            logout_user()
            login_user(updated_user)

        flash('تم تحديث الملف الشخصي بنجاح!', 'success')
        return redirect(url_for('dashboard.index'))

    return render_template('auth/profile.html', form=form, title='الملف الشخصي')
