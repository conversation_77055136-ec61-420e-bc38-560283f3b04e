@echo off
chcp 65001 >nul
title مكتب عصام الفت - تشغيل سريع

echo ================================================================
echo                    مكتب عصام الفت لإدارة الأملاك
echo                           تشغيل سريع
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

REM تحديد مجلد العمل الحالي
cd /d "%~dp0"

echo اختر طريقة التشغيل:
echo.
echo 1. تشغيل الخادم المحلي (وحدة التحكم)
echo 2. تشغيل الواجهة الرسومية
echo 3. تشغيل خادم الشبكة
echo 4. تشغيل النسخة المحمولة
echo 5. إنشاء جميع النسخ
echo 6. تثبيت المتطلبات فقط
echo 7. إعادة تعيين كلمة مرور المدير
echo 8. خروج
echo.

set /p choice="اختر رقم (1-8): "

if "%choice%"=="1" (
    echo تشغيل الخادم المحلي...
    python server_manager.py --console
    goto end
)

if "%choice%"=="2" (
    echo تشغيل الواجهة الرسومية...
    python server_manager.py
    goto end
)

if "%choice%"=="3" (
    echo تشغيل خادم الشبكة...
    python server_manager.py --network
    goto end
)

if "%choice%"=="4" (
    echo تشغيل النسخة المحمولة...
    python run_portable.py
    goto end
)

if "%choice%"=="5" (
    echo إنشاء جميع النسخ...
    python create_all_versions.bat
    goto end
)

if "%choice%"=="6" (
    echo تثبيت المتطلبات...
    pip install -r requirements.txt
    echo تم تثبيت المتطلبات بنجاح!
    goto end
)

if "%choice%"=="7" (
    echo إعادة تعيين كلمة مرور المدير...
    python server_manager.py --reset-admin-password
    goto end
)

if "%choice%"=="8" (
    echo وداعاً!
    exit /b 0
)

echo اختيار غير صالح!

:end
echo.
pause
