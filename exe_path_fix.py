#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إصلاح مسارات الملف التنفيذي - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import logging
from pathlib import Path

def setup_exe_paths():
    """إعداد المسارات للملف التنفيذي"""
    
    # إعداد التسجيل
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger('exe_path_fix')
    
    try:
        logger.info("Setting up EXE paths...")
        
        # تحديد المسارات
        if getattr(sys, 'frozen', False):
            # في حالة التشغيل كملف تنفيذي
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller onefile mode
                base_path = sys._MEIPASS
                application_path = os.path.dirname(sys.executable)
                logger.info(f"Running in onefile mode")
                logger.info(f"Base path (temp): {base_path}")
                logger.info(f"Application path: {application_path}")
            else:
                # PyInstaller onedir mode
                application_path = os.path.dirname(sys.executable)
                base_path = application_path
                logger.info(f"Running in onedir mode")
                logger.info(f"Application path: {application_path}")
        else:
            # في حالة التشغيل كسكريبت
            application_path = os.path.dirname(os.path.abspath(__file__))
            base_path = application_path
            logger.info(f"Running as script from: {application_path}")
        
        # إنشاء المجلدات الضرورية في مجلد التطبيق
        required_dirs = [
            'instance',
            'uploads',
            'logs',
            'uploads/buildings',
            'uploads/contracts',
            'uploads/documents',
            'uploads/owners',
            'uploads/tenants',
            'uploads/transactions'
        ]
        
        for dir_name in required_dirs:
            dir_path = os.path.join(application_path, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                logger.info(f"Created directory: {dir_path}")
        
        # إضافة المسارات إلى sys.path
        paths_to_add = [application_path]
        
        if getattr(sys, 'frozen', False):
            # إضافة مسارات إضافية للملف التنفيذي
            additional_paths = [
                base_path,
                os.path.join(base_path, 'app'),
                os.path.join(base_path, 'backend'),
                os.path.join(application_path, 'app'),
                os.path.join(application_path, 'backend')
            ]
            paths_to_add.extend(additional_paths)
        
        for path in paths_to_add:
            if path and os.path.exists(path) and path not in sys.path:
                sys.path.insert(0, path)
                logger.info(f"Added to sys.path: {path}")
        
        # تغيير مجلد العمل إلى مجلد التطبيق
        os.chdir(application_path)
        logger.info(f"Changed working directory to: {application_path}")
        
        # إعداد متغيرات البيئة
        os.environ['FLASK_APP_PATH'] = application_path
        os.environ['FLASK_BASE_PATH'] = base_path
        
        logger.info("EXE paths setup completed successfully")
        
        return {
            'application_path': application_path,
            'base_path': base_path,
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error setting up EXE paths: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        return {
            'application_path': None,
            'base_path': None,
            'success': False,
            'error': str(e)
        }

def apply_werkzeug_patches():
    """تطبيق إصلاحات Werkzeug"""
    
    logger = logging.getLogger('werkzeug_patch')
    
    try:
        logger.info("Applying Werkzeug patches...")
        
        import werkzeug.urls
        from urllib.parse import parse_qs, quote, quote_plus, unquote, unquote_plus
        
        # إضافة url_decode إذا لم تكن موجودة
        if not hasattr(werkzeug.urls, 'url_decode'):
            logger.info("Adding url_decode to werkzeug.urls")
            
            def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
                """تنفيذ بديل لـ url_decode"""
                try:
                    from werkzeug.datastructures import MultiDict
                    result_cls = MultiDict
                except ImportError:
                    result_cls = dict
                
                if cls is not None:
                    result_cls = cls
                
                result = result_cls()
                parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)
                
                for key, values in parsed.items():
                    for value in values:
                        if hasattr(result, 'add'):
                            result.add(key, value)
                        else:
                            result[key] = value
                
                return result
            
            werkzeug.urls.url_decode = url_decode
            logger.info("Added url_decode to werkzeug.urls")
        
        # إضافة دوال أخرى مفقودة
        missing_functions = []
        function_mappings = {
            'url_quote': quote,
            'url_quote_plus': quote_plus,
            'url_unquote': unquote,
            'url_unquote_plus': unquote_plus
        }
        
        for func_name, func_impl in function_mappings.items():
            if not hasattr(werkzeug.urls, func_name):
                missing_functions.append(func_name)
                setattr(werkzeug.urls, func_name, func_impl)
        
        if missing_functions:
            logger.info(f"Added missing functions to werkzeug.urls: {', '.join(missing_functions)}")
        
        logger.info("Werkzeug patches applied successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error applying Werkzeug patches: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def setup_flask_config():
    """إعداد تكوين Flask للملف التنفيذي"""
    
    logger = logging.getLogger('flask_config')
    
    try:
        logger.info("Setting up Flask config for EXE...")
        
        # الحصول على مسارات التطبيق
        application_path = os.environ.get('FLASK_APP_PATH')
        base_path = os.environ.get('FLASK_BASE_PATH')
        
        if not application_path:
            if getattr(sys, 'frozen', False):
                application_path = os.path.dirname(sys.executable)
            else:
                application_path = os.path.dirname(os.path.abspath(__file__))
        
        # إعداد مسارات قاعدة البيانات والملفات
        config_updates = {
            'DATABASE_PATH': os.path.join(application_path, 'instance', 'realestate.db'),
            'UPLOAD_FOLDER': os.path.join(application_path, 'uploads'),
            'INSTANCE_PATH': os.path.join(application_path, 'instance'),
            'LOGS_PATH': os.path.join(application_path, 'logs')
        }
        
        # تحديث متغيرات البيئة
        for key, value in config_updates.items():
            os.environ[key] = value
            logger.info(f"Set {key} = {value}")
        
        logger.info("Flask config setup completed")
        return True
        
    except Exception as e:
        logger.error(f"Error setting up Flask config: {str(e)}")
        return False

def initialize_exe_environment():
    """تهيئة بيئة الملف التنفيذي الكاملة"""
    
    logger = logging.getLogger('exe_init')
    
    try:
        logger.info("Initializing EXE environment...")
        
        # إعداد المسارات
        path_result = setup_exe_paths()
        if not path_result['success']:
            logger.error("Failed to setup paths")
            return False
        
        # تطبيق إصلاحات Werkzeug
        if not apply_werkzeug_patches():
            logger.warning("Failed to apply Werkzeug patches")
        
        # إعداد تكوين Flask
        if not setup_flask_config():
            logger.warning("Failed to setup Flask config")
        
        logger.info("EXE environment initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing EXE environment: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

# تشغيل التهيئة تلقائياً عند استيراد الملف
if __name__ == '__main__' or getattr(sys, 'frozen', False):
    initialize_exe_environment()
