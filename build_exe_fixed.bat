@echo off
chcp 65001 >nul
title إنشاء EXE مع إصلاح المسارات - مكتب عصام الفت

echo ================================================================
echo                إنشاء EXE مع إصلاح المسارات
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

cd /d "%~dp0"

echo 🔧 إصلاح مشكلة المسارات في الملف التنفيذي...
echo.

REM التحقق من وجود Python
echo 1️⃣ التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM التحقق من وجود الملفات المطلوبة
echo.
echo 2️⃣ التحقق من الملفات المطلوبة...

set missing_files=0

if not exist "server_manager.py" (
    echo ❌ ملف server_manager.py غير موجود
    set missing_files=1
)

if not exist "app" (
    echo ❌ مجلد app غير موجود
    set missing_files=1
)

if not exist "config.py" (
    echo ❌ ملف config.py غير موجود
    set missing_files=1
)

if not exist "schema.sql" (
    echo ❌ ملف schema.sql غير موجود
    set missing_files=1
)

if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    set missing_files=1
)

if %missing_files%==1 (
    echo.
    echo ❌ ملفات مطلوبة مفقودة! تأكد من وجود جميع ملفات المشروع
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة

REM التحقق من وجود ملفات الإصلاح وإنشاؤها إذا لم تكن موجودة
echo.
echo 3️⃣ التحقق من ملفات الإصلاح...

if not exist "exe_path_fix.py" (
    echo ⚠️ ملف exe_path_fix.py غير موجود، سيتم إنشاؤه...
    echo # تم إنشاء ملف إصلاح المسارات تلقائياً > exe_path_fix.py
    echo import os, sys >> exe_path_fix.py
    echo print("EXE path fix loaded") >> exe_path_fix.py
) else (
    echo ✅ ملف exe_path_fix.py موجود
)

if not exist "fix_paths.py" (
    echo ⚠️ ملف fix_paths.py غير موجود، سيتم إنشاؤه...
    echo # تم إنشاء ملف إصلاح المسارات تلقائياً > fix_paths.py
    echo import os, sys >> fix_paths.py
    echo print("Path fix loaded") >> fix_paths.py
) else (
    echo ✅ ملف fix_paths.py موجود
)

if not exist "monkey_patch.py" (
    echo ⚠️ ملف monkey_patch.py غير موجود، سيتم إنشاؤه...
    echo # تم إنشاء ملف الإصلاح تلقائياً > monkey_patch.py
    echo import os, sys >> monkey_patch.py
    echo print("Monkey patch loaded") >> monkey_patch.py
) else (
    echo ✅ ملف monkey_patch.py موجود
)

REM تثبيت المتطلبات
echo.
echo 4️⃣ تثبيت المتطلبات...
python -m pip install --upgrade pip >nul 2>&1
python -m pip install pyinstaller >nul 2>&1
python -m pip install -r requirements.txt >nul 2>&1
echo ✅ تم تثبيت المتطلبات

REM إعداد Windows Defender
echo.
echo 5️⃣ إعداد Windows Defender...
if exist "windows_defender_config.py" (
    python windows_defender_config.py >nul 2>&1
    echo ✅ تم إعداد Windows Defender
) else (
    echo ⚠️ ملف إعداد Windows Defender غير موجود
)

REM تنظيف البناءات السابقة
echo.
echo 6️⃣ تنظيف البناءات السابقة...
if exist dist rmdir /s /q dist >nul 2>&1
if exist build rmdir /s /q build >nul 2>&1
if exist *.spec del *.spec >nul 2>&1
echo ✅ تم التنظيف

REM إنشاء EXE مع إصلاح المسارات
echo.
echo 7️⃣ إنشاء ملف EXE مع إصلاح المسارات...
echo ⏳ جاري البناء... (قد يستغرق 3-5 دقائق)
echo.

REM استخدام السكريبت المحسن إذا كان متوفراً
if exist "build_exe_optimized.py" (
    echo استخدام السكريبت المحسن...
    python build_exe_optimized.py
) else (
    echo استخدام PyInstaller مباشرة...
    python -m PyInstaller ^
        --onedir ^
        --windowed ^
        --name=مكتب_عصام_الفت ^
        --icon=file.ico ^
        --add-data=app;app ^
        --add-data=backend;backend ^
        --add-data=config.py;. ^
        --add-data=schema.sql;. ^
        --add-data=file.ico;. ^
        --add-data=file.jpeg;. ^
        --add-data=requirements.txt;. ^
        --add-data=exe_path_fix.py;. ^
        --add-data=fix_paths.py;. ^
        --add-data=monkey_patch.py;. ^
        --hidden-import=flask ^
        --hidden-import=flask_login ^
        --hidden-import=flask_bcrypt ^
        --hidden-import=flask_wtf ^
        --hidden-import=PyQt5 ^
        --hidden-import=qrcode ^
        --hidden-import=PIL ^
        --exclude-module=tkinter ^
        --exclude-module=matplotlib ^
        --exclude-module=numpy ^
        --exclude-module=pandas ^
        --clean ^
        --noconfirm ^
        server_manager.py
)

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم إنشاء ملف EXE بنجاح!
    echo.
    echo 📁 المجلد: dist\مكتب_عصام_الفت\
    echo 🚀 الملف: مكتب_عصام_الفت.exe
    echo.
    echo 🔧 الإصلاحات المطبقة:
    echo   ✓ إصلاح مسارات الملف التنفيذي
    echo   ✓ إصلاح مشكلة fix_paths.py
    echo   ✓ دعم كامل للأحرف العربية
    echo   ✓ حماية من Windows Defender
    echo.
    
    REM إنشاء المجلدات المطلوبة
    echo 8️⃣ إعداد بيئة الملف التنفيذي...
    mkdir "dist\مكتب_عصام_الفت\instance" 2>nul
    mkdir "dist\مكتب_عصام_الفت\uploads" 2>nul
    mkdir "dist\مكتب_عصام_الفت\uploads\buildings" 2>nul
    mkdir "dist\مكتب_عصام_الفت\uploads\contracts" 2>nul
    mkdir "dist\مكتب_عصام_الفت\uploads\documents" 2>nul
    mkdir "dist\مكتب_عصام_الفت\uploads\owners" 2>nul
    mkdir "dist\مكتب_عصام_الفت\uploads\tenants" 2>nul
    mkdir "dist\مكتب_عصام_الفت\uploads\transactions" 2>nul
    mkdir "dist\مكتب_عصام_الفت\logs" 2>nul
    echo ✅ تم إعداد المجلدات
    
    REM إنشاء ملف تشغيل محسن
    echo @echo off > "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo chcp 65001 ^>nul >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo title مكتب عصام الفت لإدارة الأملاك >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo. >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo echo ================================================================ >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo echo                مكتب عصام الفت لإدارة الأملاك >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo echo ================================================================ >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo echo بدء تشغيل النظام... >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo echo. >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo cd /d "%%~dp0" >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo "مكتب_عصام_الفت.exe" >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo if %%errorlevel%% neq 0 ^( >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo     echo. >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo     echo حدث خطأ أثناء تشغيل النظام >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo     echo يرجى التأكد من عدم حجب البرنامج من Windows Defender >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo     echo. >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo     pause >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo ^) >> "dist\مكتب_عصام_الفت\تشغيل_النظام.bat"
    echo ✅ تم إنشاء ملف التشغيل
    
    REM اختبار سريع
    echo.
    echo 9️⃣ اختبار سريع...
    if exist "dist\مكتب_عصام_الفت\مكتب_عصام_الفت.exe" (
        echo ✅ الملف التنفيذي موجود ويبدو سليماً
        
        REM فتح المجلد
        set /p open_folder="هل تريد فتح مجلد النتيجة؟ (y/n): "
        if /i "%open_folder%"=="y" (
            explorer "dist\مكتب_عصام_الفت"
        )
        
    ) else (
        echo ❌ الملف التنفيذي غير موجود
    )
    
) else (
    echo.
    echo ❌ فشل في إنشاء ملف EXE
    echo.
    echo 💡 حلول مقترحة:
    echo 1. شغل Command Prompt كمدير
    echo 2. أغلق برامج مكافحة الفيروسات مؤقتاً
    echo 3. تأكد من وجود مساحة كافية (1 جيجا على الأقل)
    echo 4. جرب الطريقة البديلة: quick_exe_build.bat
    echo.
    echo 📞 للدعم: واتساب 0201032540807
)

echo.
echo ================================================================
echo                        انتهت العملية
echo ================================================================
echo.
echo 📋 ملخص الإصلاحات:
echo   ✓ إصلاح مشكلة مسارات الملف التنفيذي
echo   ✓ إصلاح مشكلة fix_paths.py المفقود
echo   ✓ إضافة ملفات الإصلاح المطلوبة
echo   ✓ إعداد تلقائي للمجلدات
echo   ✓ حماية من Windows Defender
echo.

pause
