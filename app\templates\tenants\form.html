{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="mb-0">{{ title }}</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {% if form.name.errors %}
                            {{ form.name(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.name(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.id_number.label(class="form-label") }}
                        {% if form.id_number.errors %}
                            {{ form.id_number(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.id_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.id_number(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.phone.label(class="form-label") }}
                        {% if form.phone.errors %}
                            {{ form.phone(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.phone(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {% if form.email.errors %}
                            {{ form.email(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.nationality.label(class="form-label") }}
                            {% if form.nationality.errors %}
                                {{ form.nationality(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.nationality.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.nationality(class="form-control", list="nationalityOptions") }}
                                <datalist id="nationalityOptions">
                                    <option value="سعودي">
                                    <option value="مصري">
                                    <option value="سوري">
                                    <option value="أردني">
                                    <option value="يمني">
                                    <option value="فلسطيني">
                                    <option value="لبناني">
                                    <option value="سوداني">
                                    <option value="هندي">
                                    <option value="باكستاني">
                                    <option value="بنغلاديشي">
                                    <option value="فلبيني">
                                </datalist>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.birth_date.label(class="form-label") }}
                            {% if form.birth_date.errors %}
                                {{ form.birth_date(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.birth_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.birth_date(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.occupation.label(class="form-label") }}
                        {% if form.occupation.errors %}
                            {{ form.occupation(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.occupation.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.occupation(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.employer.label(class="form-label") }}
                        {% if form.employer.errors %}
                            {{ form.employer(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.employer.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.employer(class="form-control") }}
                        {% endif %}
                    </div>



                    <div class="mb-3">
                        {{ form.emergency_contact.label(class="form-label") }}
                        {% if form.emergency_contact.errors %}
                            {{ form.emergency_contact(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.emergency_contact.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.emergency_contact(class="form-control") }}
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=3) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=3) }}
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('tenants.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>رجوع
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
