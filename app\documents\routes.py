import os
from flask import render_template, redirect, url_for, flash, request, send_from_directory, current_app, jsonify, send_file, make_response
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app.documents import documents_bp
from app.forms import DocumentForm
from app.db import query_db, insert_db, update_db, delete_db
from app.decorators import data_entry_required
from app.utils import allowed_file, save_file, convert_pdf_to_images, PDF2IMAGE_AVAILABLE
from datetime import datetime

@documents_bp.route('/')
@login_required
def index():
    """قائمة المستندات مع دعم البحث والفلترة"""
    # الحصول على معلمات البحث والفلترة
    search_query = request.args.get('search', '')
    related_to_filter = request.args.get('related_to', '')
    file_type_filter = request.args.get('file_type', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # بناء استعلام SQL الأساسي
    base_query = '''
        SELECT d.*, u.name as uploaded_by_name,
               CASE
                   WHEN d.related_to = 'owner' THEN (SELECT name FROM owners WHERE id = d.related_id)
                   WHEN d.related_to = 'building' THEN (SELECT name FROM buildings WHERE id = d.related_id)
                   WHEN d.related_to = 'unit' THEN (SELECT unit_number FROM units WHERE id = d.related_id)
                   WHEN d.related_to = 'tenant' THEN (SELECT name FROM tenants WHERE id = d.related_id)
                   WHEN d.related_to = 'contract' THEN (SELECT contract_number FROM contracts WHERE id = d.related_id)
                   WHEN d.related_to = 'transaction' THEN (SELECT id FROM transactions WHERE id = d.related_id)
                   ELSE 'غير معروف'
               END as related_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE 1=1
    '''

    # إعداد قائمة المعلمات
    params = []

    # إضافة شروط البحث والفلترة
    if search_query:
        base_query += " AND (d.title LIKE ? OR d.notes LIKE ?)"
        params.extend([f'%{search_query}%', f'%{search_query}%'])

    if related_to_filter:
        base_query += " AND d.related_to = ?"
        params.append(related_to_filter)

    if file_type_filter:
        if file_type_filter == 'image':
            base_query += " AND d.file_type IN ('jpg', 'jpeg', 'png', 'gif')"
        elif file_type_filter == 'document':
            base_query += " AND d.file_type IN ('pdf', 'doc', 'docx')"
        elif file_type_filter == 'spreadsheet':
            base_query += " AND d.file_type IN ('xls', 'xlsx', 'csv')"
        else:
            base_query += " AND d.file_type = ?"
            params.append(file_type_filter)

    if date_from:
        try:
            # تحويل التاريخ إلى الصيغة المناسبة
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            base_query += " AND d.upload_date >= ?"
            params.append(date_from_obj)
        except ValueError:
            # تجاهل التاريخ غير الصالح
            pass

    if date_to:
        try:
            # تحويل التاريخ إلى الصيغة المناسبة
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # إضافة يوم واحد للحصول على نهاية اليوم
            from datetime import timedelta
            date_to_obj = date_to_obj + timedelta(days=1)
            base_query += " AND d.upload_date < ?"
            params.append(date_to_obj)
        except ValueError:
            # تجاهل التاريخ غير الصالح
            pass

    # إضافة الترتيب
    base_query += " ORDER BY d.upload_date DESC"

    # تنفيذ الاستعلام
    documents = query_db(base_query, params)

    # الحصول على قائمة أنواع العلاقات المتاحة
    related_to_options = [
        {'value': 'owner', 'label': 'مالك'},
        {'value': 'building', 'label': 'مبنى'},
        {'value': 'unit', 'label': 'وحدة'},
        {'value': 'tenant', 'label': 'مستأجر'},
        {'value': 'contract', 'label': 'عقد'},
        {'value': 'transaction', 'label': 'معاملة مالية'}
    ]

    # الحصول على قائمة أنواع الملفات المتاحة
    file_type_options = [
        {'value': 'image', 'label': 'صورة'},
        {'value': 'pdf', 'label': 'PDF'},
        {'value': 'document', 'label': 'مستند'},
        {'value': 'spreadsheet', 'label': 'جدول بيانات'}
    ]

    return render_template('documents/index.html',
                          documents=documents,
                          title='إدارة المستندات',
                          search_query=search_query,
                          related_to_filter=related_to_filter,
                          file_type_filter=file_type_filter,
                          date_from=date_from,
                          date_to=date_to,
                          related_to_options=related_to_options,
                          file_type_options=file_type_options)

@documents_bp.route('/direct/<int:document_id>')
@login_required
def direct_file(document_id):
    """عرض ملف المستند مباشرة"""
    document = query_db('SELECT * FROM documents WHERE id = ?', (document_id,), one=True)

    if not document:
        flash('المستند غير موجود.', 'danger')
        return redirect(url_for('documents.index'))

    # استخراج اسم الملف من المسار
    filename = document['file_path']

    try:
        # البحث عن الملف في مجلد التحميل
        upload_folder = current_app.config['UPLOAD_FOLDER']
        actual_filename = None

        # محاولة العثور على الملف بالاسم الدقيق
        file_path = os.path.join(upload_folder, filename)
        if os.path.exists(file_path):
            actual_filename = filename
        else:
            # محاولة العثور على الملف بدون مسافات
            filename_no_spaces = filename.replace(' ', '')
            for file in os.listdir(upload_folder):
                if file.replace(' ', '') == filename_no_spaces:
                    actual_filename = file
                    break

            # إذا لم يتم العثور على الملف، محاولة العثور عليه بالجزء الأول من الاسم
            if not actual_filename:
                filename_prefix = filename.split('_')[0]
                for file in os.listdir(upload_folder):
                    if file.startswith(filename_prefix):
                        actual_filename = file
                        break

        # إذا تم العثور على الملف، إرساله للعرض
        if actual_filename:
            # تحديد نوع MIME للملف
            mime_type = None
            file_ext = actual_filename.split('.')[-1].lower()

            if file_ext in ['jpg', 'jpeg']:
                mime_type = 'image/jpeg'
            elif file_ext == 'png':
                mime_type = 'image/png'
            elif file_ext == 'gif':
                mime_type = 'image/gif'
            elif file_ext == 'pdf':
                mime_type = 'application/pdf'
            elif file_ext in ['doc', 'docx']:
                mime_type = 'application/msword'
            elif file_ext in ['xls', 'xlsx']:
                mime_type = 'application/vnd.ms-excel'

            # إرسال الملف للعرض (بدون تنزيل)
            return send_from_directory(
                upload_folder,
                actual_filename,
                as_attachment=False,
                mimetype=mime_type
            )

        # إذا لم يتم العثور على الملف
        flash('الملف غير موجود على القرص.', 'danger')
        return redirect(url_for('documents.view', document_id=document_id))
    except Exception as e:
        flash(f'حدث خطأ أثناء عرض الملف: {str(e)}', 'danger')
        return redirect(url_for('documents.view', document_id=document_id))

@documents_bp.route('/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add():
    """إضافة مستند جديد"""
    form = DocumentForm()

    # تحميل القائمة المناسبة للاختيار بناءً على نوع العلاقة
    if request.method == 'GET':
        related_to = request.args.get('related_to')
        related_id = request.args.get('related_id', type=int)

        if related_to:
            form.related_to.data = related_to

        if related_id:
            form.related_id.data = related_id

    # تحديث قائمة الخيارات بناءً على نوع العلاقة المحدد
    if form.related_to.data == 'owner':
        owners = query_db('SELECT id, name FROM owners ORDER BY name')
        form.related_id.choices = [(owner['id'], owner['name']) for owner in owners]
    elif form.related_to.data == 'building':
        buildings = query_db('SELECT id, name FROM buildings ORDER BY name')
        form.related_id.choices = [(building['id'], building['name']) for building in buildings]
    elif form.related_to.data == 'unit':
        units = query_db('''
            SELECT u.id, u.unit_number, b.name as building_name
            FROM units u
            JOIN buildings b ON u.building_id = b.id
            ORDER BY b.name, u.unit_number
        ''')
        form.related_id.choices = [(unit['id'], f"{unit['unit_number']} - {unit['building_name']}") for unit in units]
    elif form.related_to.data == 'tenant':
        tenants = query_db('SELECT id, name FROM tenants ORDER BY name')
        form.related_id.choices = [(tenant['id'], tenant['name']) for tenant in tenants]
    elif form.related_to.data == 'contract':
        contracts = query_db('''
            SELECT c.id, c.contract_number, t.name as tenant_name, u.unit_number, b.name as building_name
            FROM contracts c
            JOIN tenants t ON c.tenant_id = t.id
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            ORDER BY c.start_date DESC
        ''')
        form.related_id.choices = [(contract['id'], f"{contract['contract_number'] or contract['id']} - {contract['tenant_name']} ({contract['unit_number']} - {contract['building_name']})") for contract in contracts]
    elif form.related_to.data == 'transaction':
        transactions = query_db('''
            SELECT id, transaction_date, amount, type, category
            FROM transactions
            ORDER BY transaction_date DESC
            LIMIT 100
        ''')
        form.related_id.choices = [(transaction['id'], f"معاملة #{transaction['id']} - {transaction['amount']} ر.س ({transaction['category']})") for transaction in transactions]

    if form.validate_on_submit():
        # حفظ الملف
        file = form.file.data
        file_path = save_file(file)

        if file_path:
            # استخراج نوع الملف
            file_type = file.filename.rsplit('.', 1)[1].lower()

            # إدراج المستند في قاعدة البيانات
            document_id = insert_db('documents', {
                'title': form.title.data,
                'file_path': os.path.basename(file_path),
                'file_type': file_type,
                'related_to': form.related_to.data,
                'related_id': form.related_id.data,
                'notes': form.notes.data,
                'upload_date': datetime.now(),
                'uploaded_by': current_user.id
            })

            flash('تم إضافة المستند بنجاح!', 'success')

            # إعادة التوجيه بناءً على نوع العلاقة
            if form.related_to.data == 'owner':
                return redirect(url_for('owners.view', owner_id=form.related_id.data))
            elif form.related_to.data == 'building':
                return redirect(url_for('properties.view_building', building_id=form.related_id.data))
            elif form.related_to.data == 'unit':
                return redirect(url_for('properties.view_unit', unit_id=form.related_id.data))
            elif form.related_to.data == 'tenant':
                return redirect(url_for('tenants.view', tenant_id=form.related_id.data))
            elif form.related_to.data == 'contract':
                return redirect(url_for('tenants.view_contract', contract_id=form.related_id.data))
            elif form.related_to.data == 'transaction':
                return redirect(url_for('finance.view_transaction', transaction_id=form.related_id.data))
            else:
                return redirect(url_for('documents.index'))
        else:
            flash('حدث خطأ أثناء حفظ الملف.', 'danger')

    return render_template('documents/form.html', form=form, title='إضافة مستند جديد')

@documents_bp.route('/view/<int:document_id>')
@login_required
def view(document_id):
    """عرض تفاصيل المستند"""
    document = query_db('''
        SELECT d.*, u.name as uploaded_by_name,
               CASE
                   WHEN d.related_to = 'owner' THEN (SELECT name FROM owners WHERE id = d.related_id)
                   WHEN d.related_to = 'building' THEN (SELECT name FROM buildings WHERE id = d.related_id)
                   WHEN d.related_to = 'unit' THEN (SELECT unit_number FROM units WHERE id = d.related_id)
                   WHEN d.related_to = 'tenant' THEN (SELECT name FROM tenants WHERE id = d.related_id)
                   WHEN d.related_to = 'contract' THEN (SELECT contract_number FROM contracts WHERE id = d.related_id)
                   WHEN d.related_to = 'transaction' THEN (SELECT id FROM transactions WHERE id = d.related_id)
                   ELSE 'غير معروف'
               END as related_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.id = ?
    ''', (document_id,), one=True)

    if not document:
        flash('المستند غير موجود.', 'danger')
        return redirect(url_for('documents.index'))

    return render_template('documents/view.html', document=document, title=f'تفاصيل المستند: {document["title"]}')

@documents_bp.route('/download/<int:document_id>')
@login_required
def download(document_id):
    """تنزيل المستند"""
    document = query_db('SELECT * FROM documents WHERE id = ?', (document_id,), one=True)

    if not document:
        flash('المستند غير موجود.', 'danger')
        return redirect(url_for('documents.index'))

    # استخراج اسم الملف من المسار
    filename = document['file_path']

    try:
        # البحث عن الملف في مجلد التحميل
        upload_folder = current_app.config['UPLOAD_FOLDER']

        # محاولة العثور على الملف بالاسم الدقيق
        file_path = os.path.join(upload_folder, filename)
        if os.path.exists(file_path):
            # إرسال الملف للتنزيل
            # استخدام Response مباشرة لتجنب مشاكل المسار
            with open(file_path, 'rb') as f:
                file_content = f.read()

            from flask import Response
            response = Response(
                file_content,
                mimetype='application/octet-stream'
            )

            # تحديد رأس Content-Disposition كـ attachment لإجبار التنزيل
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        # محاولة العثور على الملف بدون مسافات
        filename_no_spaces = filename.replace(' ', '')
        for file in os.listdir(upload_folder):
            if file.replace(' ', '') == filename_no_spaces:
                # إرسال الملف للتنزيل
                # استخدام Response مباشرة لتجنب مشاكل المسار
                file_path = os.path.join(upload_folder, file)
                with open(file_path, 'rb') as f:
                    file_content = f.read()

                from flask import Response
                response = Response(
                    file_content,
                    mimetype='application/octet-stream'
                )

                # تحديد رأس Content-Disposition كـ attachment لإجبار التنزيل
                response.headers['Content-Disposition'] = f'attachment; filename="{file}"'

                return response

        # محاولة العثور على الملف بالجزء الأول من الاسم (قبل الشرطة الأولى)
        filename_prefix = filename.split('_')[0]
        for file in os.listdir(upload_folder):
            if file.startswith(filename_prefix):
                # إرسال الملف للتنزيل
                # استخدام Response مباشرة لتجنب مشاكل المسار
                file_path = os.path.join(upload_folder, file)
                with open(file_path, 'rb') as f:
                    file_content = f.read()

                from flask import Response
                response = Response(
                    file_content,
                    mimetype='application/octet-stream'
                )

                # تحديد رأس Content-Disposition كـ attachment لإجبار التنزيل
                response.headers['Content-Disposition'] = f'attachment; filename="{file}"'

                return response

        # إذا لم يتم العثور على الملف
        flash('الملف غير موجود على القرص.', 'danger')
        return redirect(url_for('documents.view', document_id=document_id))
    except Exception as e:
        flash(f'حدث خطأ أثناء تنزيل الملف: {str(e)}', 'danger')
        return redirect(url_for('documents.view', document_id=document_id))

@documents_bp.route('/display/', defaults={'document_id': None})
@documents_bp.route('/display/<int:document_id>')
@login_required
def display_file(document_id):
    """عرض المستند في صفحة منفصلة"""
    if document_id is None:
        flash('يرجى تحديد المستند المراد عرضه.', 'warning')
        return redirect(url_for('documents.index'))

    document = query_db('SELECT * FROM documents WHERE id = ?', (document_id,), one=True)

    if not document:
        flash('المستند غير موجود.', 'danger')
        return redirect(url_for('documents.index'))

    # استخراج اسم الملف من المسار
    filename = document['file_path']
    file_ext = filename.split('.')[-1].lower()

    # تحديد نوع الملف
    file_type = None
    if file_ext in ['jpg', 'jpeg', 'png', 'gif']:
        file_type = 'image'
    elif file_ext == 'pdf':
        file_type = 'pdf'
    else:
        file_type = 'other'

    # إنشاء رابط مباشر للملف
    file_url = url_for('documents.serve_file', document_id=document_id, _external=True)

    # عرض صفحة العرض المناسبة
    if file_type == 'pdf':
        # استخدام iframe لعرض PDF مباشرة
        print(f"Using iframe to display PDF: {filename}")
        return render_template(
            'documents/pdf_iframe_viewer.html',
            document=document,
            file_url=file_url,
            title=f'عرض المستند: {document["title"]}'
        )
    else:
        return render_template(
            'documents/display.html',
            document=document,
            file_url=file_url,
            file_type=file_type,
            title=f'عرض المستند: {document["title"]}'
        )

@documents_bp.route('/serve/<int:document_id>')
@login_required
def serve_file(document_id):
    """خدمة ملف المستند مباشرة"""
    document = query_db('SELECT * FROM documents WHERE id = ?', (document_id,), one=True)

    if not document:
        flash('المستند غير موجود.', 'danger')
        return redirect(url_for('documents.index'))

    # استخراج اسم الملف من المسار
    filename = document['file_path']

    try:
        # البحث عن الملف في مجلد التحميل
        upload_folder = current_app.config['UPLOAD_FOLDER']

        # المسار الكامل للملف
        file_path = os.path.join(upload_folder, filename)

        # طباعة معلومات التصحيح
        print(f"Requested file: {filename}")
        print(f"Full path: {file_path}")
        print(f"File exists: {os.path.exists(file_path)}")

        # تحديد نوع MIME للملف
        mime_type = None
        file_ext = filename.split('.')[-1].lower()

        if file_ext in ['jpg', 'jpeg']:
            mime_type = 'image/jpeg'
        elif file_ext == 'png':
            mime_type = 'image/png'
        elif file_ext == 'gif':
            mime_type = 'image/gif'
        elif file_ext == 'pdf':
            mime_type = 'application/pdf'
        elif file_ext in ['doc', 'docx']:
            mime_type = 'application/msword'
        elif file_ext in ['xls', 'xlsx']:
            mime_type = 'application/vnd.ms-excel'

        # قراءة الملف وإرساله كاستجابة
        if os.path.exists(file_path):
            print(f"Reading file content: {filename}")
            try:
                # استخدام Response مباشرة لتجنب مشاكل المسار
                with open(file_path, 'rb') as f:
                    file_content = f.read()

                from flask import Response
                print(f"Sending file as response, size: {len(file_content)} bytes")
                response = Response(
                    file_content,
                    mimetype=mime_type
                )

                # تحديد رؤوس HTTP لضمان عرض الملف مباشرة في المتصفح
                response.headers['Content-Disposition'] = 'inline'
                response.headers['X-Content-Type-Options'] = 'nosniff'
                response.headers['Cache-Control'] = 'public, max-age=3600'

                return response
            except Exception as e:
                print(f"Error sending file with send_file: {str(e)}")
                # محاولة استخدام طريقة أخرى
                try:
                    with open(file_path, 'rb') as f:
                        file_content = f.read()

                    from flask import Response
                    print(f"Sending file as response, size: {len(file_content)} bytes")
                    return Response(
                        file_content,
                        mimetype=mime_type,
                        headers={"Content-Disposition": f"inline; filename={filename}"}
                    )
                except Exception as e2:
                    print(f"Error sending file with Response: {str(e2)}")
                    raise e2

        # إذا لم يتم العثور على الملف بالاسم الدقيق، محاولة البحث عن ملف مشابه
        print("File not found, searching for similar files...")

        # محاولة العثور على الملف بدون مسافات
        filename_no_spaces = filename.replace(' ', '')
        for file in os.listdir(upload_folder):
            if file.replace(' ', '') == filename_no_spaces:
                print(f"Found matching file (no spaces): {file}")
                file_path = os.path.join(upload_folder, file)

                # استخدام Response مباشرة لتجنب مشاكل المسار
                with open(file_path, 'rb') as f:
                    file_content = f.read()

                from flask import Response
                print(f"Sending file as response, size: {len(file_content)} bytes")
                response = Response(
                    file_content,
                    mimetype=mime_type
                )

                # تحديد رؤوس HTTP لضمان عرض الملف مباشرة في المتصفح
                response.headers['Content-Disposition'] = 'inline'
                response.headers['X-Content-Type-Options'] = 'nosniff'
                response.headers['Cache-Control'] = 'public, max-age=3600'

                return response

        # محاولة العثور على الملف بالجزء الأول من الاسم
        filename_prefix = filename.split('_')[0]
        for file in os.listdir(upload_folder):
            if file.startswith(filename_prefix):
                print(f"Found file with matching prefix: {file}")
                file_path = os.path.join(upload_folder, file)

                # استخدام Response مباشرة لتجنب مشاكل المسار
                with open(file_path, 'rb') as f:
                    file_content = f.read()

                from flask import Response
                print(f"Sending file as response, size: {len(file_content)} bytes")
                response = Response(
                    file_content,
                    mimetype=mime_type
                )

                # تحديد رؤوس HTTP لضمان عرض الملف مباشرة في المتصفح
                response.headers['Content-Disposition'] = 'inline'
                response.headers['X-Content-Type-Options'] = 'nosniff'
                response.headers['Cache-Control'] = 'public, max-age=3600'

                return response

        # إذا لم يتم العثور على الملف
        print("No matching file found")
        flash('الملف غير موجود على القرص.', 'danger')
        return redirect(url_for('documents.view', document_id=document_id))
    except Exception as e:
        print(f"Error serving file: {str(e)}")
        flash(f'حدث خطأ أثناء عرض الملف: {str(e)}', 'danger')
        return redirect(url_for('documents.view', document_id=document_id))

@documents_bp.route('/get_related_items')
@login_required
def get_related_items():
    """الحصول على العناصر المرتبطة بنوع معين"""
    related_to = request.args.get('related_to')
    result = []

    if related_to == 'owner':
        items = query_db('SELECT id, name FROM owners ORDER BY name')
        result = [{'id': item['id'], 'name': item['name']} for item in items]
    elif related_to == 'building':
        items = query_db('SELECT id, name FROM buildings ORDER BY name')
        result = [{'id': item['id'], 'name': item['name']} for item in items]
    elif related_to == 'unit':
        items = query_db('''
            SELECT u.id, u.unit_number, b.name as building_name
            FROM units u
            JOIN buildings b ON u.building_id = b.id
            ORDER BY b.name, u.unit_number
        ''')
        result = [{'id': item['id'], 'name': f"{item['unit_number']} - {item['building_name']}"} for item in items]
    elif related_to == 'tenant':
        items = query_db('SELECT id, name FROM tenants ORDER BY name')
        result = [{'id': item['id'], 'name': item['name']} for item in items]
    elif related_to == 'contract':
        items = query_db('''
            SELECT c.id, c.contract_number, t.name as tenant_name, u.unit_number, b.name as building_name
            FROM contracts c
            JOIN tenants t ON c.tenant_id = t.id
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            ORDER BY c.start_date DESC
        ''')
        result = [{'id': item['id'], 'name': f"{item['contract_number'] or item['id']} - {item['tenant_name']} ({item['unit_number']} - {item['building_name']})"} for item in items]
    elif related_to == 'transaction':
        items = query_db('''
            SELECT id, transaction_date, amount, type, category
            FROM transactions
            ORDER BY transaction_date DESC
            LIMIT 100
        ''')
        result = [{'id': item['id'], 'name': f"معاملة #{item['id']} - {item['amount']} ر.س ({item['category']})"} for item in items]

    return jsonify(result)

@documents_bp.route('/delete/<int:document_id>', methods=['POST'])
@login_required
@data_entry_required
def delete(document_id):
    """حذف مستند"""
    document = query_db('SELECT * FROM documents WHERE id = ?', (document_id,), one=True)

    if not document:
        flash('المستند غير موجود.', 'danger')
        return redirect(url_for('documents.index'))

    # حذف الملف من القرص
    try:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], document['file_path'])
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف الملف: {str(e)}', 'warning')

    # حذف المستند من قاعدة البيانات
    delete_db('documents', document_id)

    flash('تم حذف المستند بنجاح!', 'success')

    # إعادة التوجيه بناءً على نوع العلاقة
    if document['related_to'] == 'owner':
        return redirect(url_for('owners.view', owner_id=document['related_id']))
    elif document['related_to'] == 'building':
        return redirect(url_for('properties.view_building', building_id=document['related_id']))
    elif document['related_to'] == 'unit':
        return redirect(url_for('properties.view_unit', unit_id=document['related_id']))
    elif document['related_to'] == 'tenant':
        return redirect(url_for('tenants.view', tenant_id=document['related_id']))
    elif document['related_to'] == 'contract':
        return redirect(url_for('tenants.view_contract', contract_id=document['related_id']))
    elif document['related_to'] == 'transaction':
        return redirect(url_for('finance.view_transaction', transaction_id=document['related_id']))
    else:
        return redirect(url_for('documents.index'))
