<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            background-color: #f9f9f9;
        }
        .receipt {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .receipt-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .receipt-header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .receipt-header p {
            color: #666;
            margin: 5px 0;
        }
        .receipt-body {
            margin-bottom: 20px;
        }
        .receipt-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .receipt-info-item {
            flex: 1;
        }
        .receipt-info-item h3 {
            color: #333;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .receipt-info-item p {
            color: #666;
            margin: 5px 0;
        }
        .receipt-details {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .receipt-details th, .receipt-details td {
            padding: 10px;
            text-align: right;
            border-bottom: 1px solid #f0f0f0;
        }
        .receipt-details th {
            background-color: #f9f9f9;
            color: #333;
            font-weight: bold;
        }
        .receipt-total {
            text-align: left;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 2px solid #f0f0f0;
        }
        .receipt-total h3 {
            color: #333;
            margin: 0;
            font-size: 18px;
        }
        .receipt-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #f0f0f0;
            color: #666;
        }
        .badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            color: #fff;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .signature {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-item {
            flex: 1;
            text-align: center;
            padding: 0 20px;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 50px;
            padding-top: 10px;
        }
        @media print {
            body {
                background-color: #fff;
                padding: 0;
            }
            .receipt {
                box-shadow: none;
                border: none;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="receipt-header">
            <h1>إيصال معاملة مالية</h1>
            <p>نظام إدارة العقارات</p>
            <p>رقم المعاملة: {{ transaction.id }}</p>
            <p>التاريخ: {{ transaction.transaction_date }}</p>
        </div>
        
        <div class="receipt-body">
            <div class="receipt-info">
                <div class="receipt-info-item">
                    <h3>معلومات المعاملة</h3>
                    <p>
                        <strong>النوع:</strong>
                        {% if transaction.type == 'income' %}
                        <span class="badge badge-success">إيراد</span>
                        {% else %}
                        <span class="badge badge-danger">مصروف</span>
                        {% endif %}
                    </p>
                    <p>
                        <strong>الفئة:</strong>
                        {% if transaction.category == 'rent' %}
                        إيجار
                        {% elif transaction.category == 'deposit' %}
                        تأمين
                        {% elif transaction.category == 'maintenance' %}
                        صيانة
                        {% elif transaction.category == 'utilities' %}
                        مرافق
                        {% elif transaction.category == 'taxes' %}
                        ضرائب
                        {% elif transaction.category == 'insurance' %}
                        تأمين
                        {% elif transaction.category == 'salary' %}
                        رواتب
                        {% elif transaction.category == 'commission' %}
                        عمولة
                        {% else %}
                        أخرى
                        {% endif %}
                    </p>
                    <p><strong>طريقة الدفع:</strong>
                        {% if transaction.payment_method == 'cash' %}
                        نقدي
                        {% elif transaction.payment_method == 'bank_transfer' %}
                        تحويل بنكي
                        {% elif transaction.payment_method == 'check' %}
                        شيك
                        {% elif transaction.payment_method == 'credit_card' %}
                        بطاقة ائتمان
                        {% else %}
                        {{ transaction.payment_method }}
                        {% endif %}
                    </p>
                    {% if transaction.reference_number %}
                    <p><strong>رقم المرجع:</strong> {{ transaction.reference_number }}</p>
                    {% endif %}
                </div>
                
                {% if contract %}
                <div class="receipt-info-item">
                    <h3>معلومات العقد</h3>
                    <p><strong>رقم العقد:</strong> {{ contract.contract_number or contract.id }}</p>
                    <p><strong>المستأجر:</strong> {{ contract.tenant_name }}</p>
                    <p><strong>الوحدة:</strong> {{ contract.unit_number }} - {{ contract.building_name }}</p>
                    <p><strong>فترة العقد:</strong> {{ contract.start_date }} إلى {{ contract.end_date }}</p>
                </div>
                {% endif %}
            </div>
            
            <table class="receipt-details">
                <tr>
                    <th>الوصف</th>
                    <th>المبلغ</th>
                </tr>
                <tr>
                    <td>{{ transaction.description or 'معاملة مالية' }}</td>
                    <td>{{ transaction.amount }} ريال سعودي</td>
                </tr>
                {% if transaction.notes %}
                <tr>
                    <td colspan="2"><strong>ملاحظات:</strong> {{ transaction.notes }}</td>
                </tr>
                {% endif %}
            </table>
            
            <div class="receipt-total">
                <h3>المبلغ الإجمالي: {{ transaction.amount }} ريال سعودي</h3>
            </div>
            
            <div class="signature">
                <div class="signature-item">
                    <p>المستلم</p>
                    <div class="signature-line"></div>
                </div>
                <div class="signature-item">
                    <p>المحاسب</p>
                    <div class="signature-line"></div>
                </div>
            </div>
        </div>
        
        <div class="receipt-footer">
            <p>تم إنشاء هذا الإيصال بواسطة نظام إدارة العقارات</p>
            <p>{{ transaction.created_at }}</p>
        </div>
    </div>
    
    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print();" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            طباعة الإيصال
        </button>
    </div>
</body>
</html>
