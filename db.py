import sqlite3
import os
from flask import current_app, g
from datetime import datetime

def get_db():
    """الحصول على اتصال بقاعدة البيانات"""
    if 'db' not in g:
        # التأكد من وجود مجلد instance
        os.makedirs(os.path.dirname(current_app.config['DATABASE_PATH']), exist_ok=True)
        
        g.db = sqlite3.connect(
            current_app.config['DATABASE_PATH'],
            detect_types=sqlite3.PARSE_DECLTYPES
        )
        g.db.row_factory = sqlite3.Row
        
        # تكوين قاعدة البيانات لاستخدام التاريخ والوقت بشكل صحيح
        g.db.execute("PRAGMA foreign_keys = ON")
    
    return g.db

def close_db(e=None):
    """إغلاق اتصال قاعدة البيانات"""
    db = g.pop('db', None)
    
    if db is not None:
        db.close()

def init_db():
    """تهيئة قاعدة البيانات باستخدام ملف schema.sql"""
    db = get_db()
    
    with current_app.open_resource('schema.sql') as f:
        db.executescript(f.read().decode('utf8'))
    
    # إنشاء مجلد التحميلات إذا لم يكن موجودًا
    os.makedirs(current_app.config['UPLOAD_FOLDER'], exist_ok=True)

def init_app(app):
    """تسجيل وظائف قاعدة البيانات مع التطبيق"""
    app.teardown_appcontext(close_db)
    app.cli.add_command(init_db_command)

def query_db(query, args=(), one=False):
    """تنفيذ استعلام واسترجاع النتائج"""
    cur = get_db().execute(query, args)
    rv = cur.fetchall()
    cur.close()
    return (rv[0] if rv else None) if one else rv

def insert_db(table, fields=None):
    """إدراج سجل جديد في الجدول المحدد"""
    if fields is None:
        return None
    
    # إضافة حقول التاريخ تلقائيًا إذا كانت موجودة في الجدول
    if 'created_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings']:
        fields['created_at'] = datetime.now()
    if 'updated_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings']:
        fields['updated_at'] = datetime.now()
    
    placeholders = ', '.join('?' * len(fields))
    columns = ', '.join(fields.keys())
    
    query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
    
    db = get_db()
    cursor = db.execute(query, list(fields.values()))
    db.commit()
    
    return cursor.lastrowid

def update_db(table, id, fields=None):
    """تحديث سجل موجود في الجدول المحدد"""
    if fields is None:
        return False
    
    # إضافة حقل updated_at تلقائيًا إذا كان موجودًا في الجدول
    if 'updated_at' not in fields and table not in ['users', 'documents', 'notifications', 'meter_readings']:
        fields['updated_at'] = datetime.now()
    
    set_clause = ', '.join([f"{key} = ?" for key in fields.keys()])
    
    query = f"UPDATE {table} SET {set_clause} WHERE id = ?"
    
    values = list(fields.values())
    values.append(id)
    
    db = get_db()
    db.execute(query, values)
    db.commit()
    
    return True

def delete_db(table, id):
    """حذف سجل من الجدول المحدد"""
    query = f"DELETE FROM {table} WHERE id = ?"
    
    db = get_db()
    db.execute(query, (id,))
    db.commit()
    
    return True

def init_db_command():
    """أمر CLI لتهيئة قاعدة البيانات"""
    init_db()
    print('تم تهيئة قاعدة البيانات بنجاح.')
