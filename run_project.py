#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل موحد للمشروع - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def setup_environment():
    """إعداد البيئة"""
    base_dir = Path(__file__).parent
    
    # إضافة المسارات إلى sys.path
    paths_to_add = [
        str(base_dir),
        str(base_dir / 'backend'),
        str(base_dir / 'app'),
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # إنشاء المجلدات المطلوبة
    required_dirs = [
        base_dir / 'instance',
        base_dir / 'uploads',
        base_dir / 'logs',
        base_dir / 'uploads' / 'buildings',
        base_dir / 'uploads' / 'contracts',
        base_dir / 'uploads' / 'documents',
        base_dir / 'uploads' / 'owners',
        base_dir / 'uploads' / 'tenants',
        base_dir / 'uploads' / 'transactions',
    ]
    
    for directory in required_dirs:
        directory.mkdir(parents=True, exist_ok=True)

def run_gui():
    """تشغيل الواجهة الرسومية"""
    try:
        setup_environment()
        from server_manager import ServerManager, QApplication
        
        app = QApplication(sys.argv)
        window = ServerManager()
        window.show()
        
        return app.exec_()
    except ImportError as e:
        print(f"خطأ في استيراد الواجهة الرسومية: {e}")
        print("تشغيل في وضع وحدة التحكم...")
        return run_console()

def run_console():
    """تشغيل في وضع وحدة التحكم"""
    try:
        setup_environment()
        
        # محاولة استيراد من backend أولاً
        try:
            from backend.app import run_server
            print("تشغيل الخادم من backend...")
            run_server(host='127.0.0.1', port=5000, debug=False)
        except ImportError:
            # استيراد من app
            from app import create_app
            print("تشغيل الخادم من app...")
            app = create_app()
            app.run(host='127.0.0.1', port=5000, debug=False)
            
    except Exception as e:
        print(f"خطأ في تشغيل الخادم: {e}")
        return 1
    
    return 0

def run_network(host='0.0.0.0', port=5000):
    """تشغيل خادم الشبكة"""
    try:
        setup_environment()
        
        # محاولة استيراد من backend أولاً
        try:
            from backend.app import run_server
            print(f"تشغيل خادم الشبكة من backend على {host}:{port}...")
            run_server(host=host, port=port, debug=False)
        except ImportError:
            # استيراد من app
            from app import create_app
            print(f"تشغيل خادم الشبكة من app على {host}:{port}...")
            app = create_app()
            app.run(host=host, port=port, debug=False)
            
    except Exception as e:
        print(f"خطأ في تشغيل خادم الشبكة: {e}")
        return 1
    
    return 0

def build_exe():
    """إنشاء ملف EXE"""
    try:
        print("🔨 بدء إنشاء ملف EXE...")
        
        # تشغيل سكريبت البناء المحسن
        result = subprocess.run([
            sys.executable, 'build_exe_optimized.py'
        ], cwd=Path(__file__).parent)
        
        return result.returncode
        
    except Exception as e:
        print(f"خطأ في إنشاء ملف EXE: {e}")
        return 1

def setup_defender():
    """إعداد Windows Defender"""
    try:
        print("🛡️ إعداد Windows Defender...")
        
        from windows_defender_config import WindowsDefenderConfig
        config = WindowsDefenderConfig()
        config.setup_all()
        
        print("✅ تم إعداد Windows Defender بنجاح")
        return 0
        
    except Exception as e:
        print(f"خطأ في إعداد Windows Defender: {e}")
        return 1

def install_requirements():
    """تثبيت المتطلبات"""
    try:
        print("📦 تثبيت المتطلبات...")
        
        requirements_file = Path(__file__).parent / 'requirements.txt'
        if requirements_file.exists():
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
            ])
            print("✅ تم تثبيت المتطلبات بنجاح")
        else:
            print("❌ ملف requirements.txt غير موجود")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"خطأ في تثبيت المتطلبات: {e}")
        return 1

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description='مكتب عصام الفت لإدارة الأملاك',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
أمثلة الاستخدام:
  python run_project.py                    # تشغيل الواجهة الرسومية
  python run_project.py --console          # تشغيل في وضع وحدة التحكم
  python run_project.py --network          # تشغيل خادم الشبكة
  python run_project.py --build-exe        # إنشاء ملف EXE
  python run_project.py --setup-defender   # إعداد Windows Defender
  python run_project.py --install          # تثبيت المتطلبات

تم التطوير بواسطة شركة المبرمج المصري
واتساب: 0201032540807
        '''
    )
    
    parser.add_argument('--console', action='store_true',
                       help='تشغيل في وضع وحدة التحكم')
    parser.add_argument('--network', action='store_true',
                       help='تشغيل خادم الشبكة')
    parser.add_argument('--host', default='0.0.0.0',
                       help='عنوان IP للخادم (افتراضي: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000,
                       help='منفذ الخادم (افتراضي: 5000)')
    parser.add_argument('--build-exe', action='store_true',
                       help='إنشاء ملف EXE')
    parser.add_argument('--setup-defender', action='store_true',
                       help='إعداد Windows Defender')
    parser.add_argument('--install', action='store_true',
                       help='تثبيت المتطلبات')
    
    args = parser.parse_args()
    
    # طباعة معلومات المشروع
    print("=" * 60)
    print("مكتب عصام الفت لإدارة الأملاك")
    print("=" * 60)
    print("تم التطوير والبرمجة بواسطة شركة المبرمج المصري")
    print("فيسبوك: https://www.facebook.com/almbarmg")
    print("واتساب: 0201032540807")
    print("جميع الحقوق محفوظة © 2025")
    print("=" * 60)
    print()
    
    # تنفيذ الأمر المطلوب
    if args.install:
        return install_requirements()
    elif args.setup_defender:
        return setup_defender()
    elif args.build_exe:
        return build_exe()
    elif args.console:
        return run_console()
    elif args.network:
        return run_network(host=args.host, port=args.port)
    else:
        # تشغيل الواجهة الرسومية افتراضياً
        return run_gui()

if __name__ == '__main__':
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\nتم إيقاف البرنامج بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
