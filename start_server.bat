@echo off
chcp 65001 >nul
title مكتب عصام الفت لإدارة الأملاك

echo ================================================================
echo                    مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo.

REM تحديد مجلد العمل الحالي
cd /d "%~dp0"

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    echo أو استخدام النسخة المحمولة من التطبيق
    pause
    exit /b 1
)

REM التحقق من وجود المتطلبات
echo التحقق من المتطلبات...
pip install -r requirements.txt --quiet

REM تشغيل الخادم
echo بدء تشغيل الخادم...
echo.
python server_manager.py --console

pause
