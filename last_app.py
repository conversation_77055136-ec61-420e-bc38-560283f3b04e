#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
البناء النهائي الشامل - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import shutil
import subprocess
import time
import json
import zipfile
from pathlib import Path
import logging
from datetime import datetime

class LastAppBuilder:
    """البناء النهائي الشامل للتطبيق"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.build_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.app_name = "مكتب_عصام_الفت"
        self.version = "1.0.0"
        
        # إعداد التسجيل
        self.setup_logging()
        
        # مسارات البناء
        self.dist_dir = self.base_dir / 'dist'
        self.build_dir = self.base_dir / 'build'
        self.final_dir = self.base_dir / f'final_build_{self.build_time}'
        
        self.logger = logging.getLogger('LastAppBuilder')
    
    def setup_logging(self):
        """إعداد نظام التسجيل"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # إنشاء مجلد السجلات
        logs_dir = self.base_dir / 'build_logs'
        logs_dir.mkdir(exist_ok=True)
        
        # ملف السجل
        log_file = logs_dir / f'last_app_build_{self.build_time}.log'
        
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def print_header(self):
        """طباعة رأس البرنامج"""
        header = f"""
{'='*80}
                    البناء النهائي الشامل
                مكتب عصام الفت لإدارة الأملاك
{'='*80}
تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
{'='*80}
الإصدار: {self.version}
وقت البناء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
{'='*80}
"""
        print(header)
        self.logger.info("بدء البناء النهائي الشامل")
    
    def check_system_requirements(self):
        """فحص متطلبات النظام"""
        self.logger.info("🔍 فحص متطلبات النظام...")
        
        # فحص Python
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            raise Exception(f"يتطلب Python 3.8 أو أحدث. الإصدار الحالي: {python_version.major}.{python_version.minor}")
        
        self.logger.info(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # فحص المساحة المتاحة
        free_space = shutil.disk_usage(self.base_dir).free / (1024**3)  # GB
        if free_space < 2:
            self.logger.warning(f"⚠️ مساحة قليلة متاحة: {free_space:.1f} GB")
        else:
            self.logger.info(f"✅ مساحة متاحة: {free_space:.1f} GB")
        
        # فحص الملفات المطلوبة
        required_files = [
            'server_manager.py',
            'config.py',
            'schema.sql',
            'requirements.txt'
        ]
        
        required_dirs = ['app']
        
        missing_files = []
        for file in required_files:
            if not (self.base_dir / file).exists():
                missing_files.append(file)
        
        for dir_name in required_dirs:
            if not (self.base_dir / dir_name).exists():
                missing_files.append(f"{dir_name}/")
        
        if missing_files:
            raise Exception(f"ملفات مطلوبة مفقودة: {', '.join(missing_files)}")
        
        self.logger.info("✅ جميع الملفات المطلوبة موجودة")
    
    def install_dependencies(self):
        """تثبيت التبعيات"""
        self.logger.info("📦 تثبيت التبعيات...")
        
        try:
            # تحديث pip
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], check=True, capture_output=True)
            
            # تثبيت PyInstaller
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', 'pyinstaller'
            ], check=True, capture_output=True)
            
            # تثبيت المتطلبات
            requirements_file = self.base_dir / 'requirements.txt'
            if requirements_file.exists():
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
                ], check=True, capture_output=True)
            
            self.logger.info("✅ تم تثبيت جميع التبعيات")
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"❌ فشل في تثبيت التبعيات: {e}")
            raise
    
    def create_support_files(self):
        """إنشاء ملفات الدعم المطلوبة"""
        self.logger.info("📝 إنشاء ملفات الدعم...")
        
        # إنشاء exe_path_fix.py إذا لم يكن موجوداً
        exe_fix_file = self.base_dir / 'exe_path_fix.py'
        if not exe_fix_file.exists():
            exe_fix_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""إصلاح مسارات الملف التنفيذي"""

import os
import sys
import logging

def setup_exe_paths():
    """إعداد المسارات للملف التنفيذي"""
    logger = logging.getLogger('exe_path_fix')
    
    try:
        if getattr(sys, 'frozen', False):
            # في حالة التشغيل كملف تنفيذي
            if hasattr(sys, '_MEIPASS'):
                base_path = sys._MEIPASS
                application_path = os.path.dirname(sys.executable)
            else:
                application_path = os.path.dirname(sys.executable)
                base_path = application_path
        else:
            application_path = os.path.dirname(os.path.abspath(__file__))
            base_path = application_path
        
        # إنشاء المجلدات المطلوبة
        required_dirs = [
            'instance', 'uploads', 'logs',
            'uploads/buildings', 'uploads/contracts', 'uploads/documents',
            'uploads/owners', 'uploads/tenants', 'uploads/transactions'
        ]
        
        for dir_name in required_dirs:
            dir_path = os.path.join(application_path, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
        
        # إضافة المسارات إلى sys.path
        paths_to_add = [application_path, base_path]
        if getattr(sys, 'frozen', False):
            paths_to_add.extend([
                os.path.join(application_path, 'app'),
                os.path.join(application_path, 'backend'),
                os.path.join(base_path, 'app'),
                os.path.join(base_path, 'backend')
            ])
        
        for path in paths_to_add:
            if path and os.path.exists(path) and path not in sys.path:
                sys.path.insert(0, path)
        
        os.chdir(application_path)
        return True
        
    except Exception as e:
        logger.error(f"Error setting up EXE paths: {e}")
        return False

# تشغيل الإعداد تلقائياً
if __name__ == '__main__' or getattr(sys, 'frozen', False):
    setup_exe_paths()
'''
            with open(exe_fix_file, 'w', encoding='utf-8') as f:
                f.write(exe_fix_content)
            self.logger.info("✅ تم إنشاء exe_path_fix.py")
        
        # إنشاء fix_paths.py إذا لم يكن موجوداً
        fix_paths_file = self.base_dir / 'fix_paths.py'
        if not fix_paths_file.exists():
            fix_paths_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""إصلاح المسارات العام"""

import os
import sys

print("Path fix loaded successfully")

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
'''
            with open(fix_paths_file, 'w', encoding='utf-8') as f:
                f.write(fix_paths_content)
            self.logger.info("✅ تم إنشاء fix_paths.py")
        
        # إنشاء monkey_patch.py إذا لم يكن موجوداً
        monkey_patch_file = self.base_dir / 'monkey_patch.py'
        if not monkey_patch_file.exists():
            monkey_patch_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""إصلاحات المكتبات"""

import os
import sys

print("Monkey patch loaded successfully")

try:
    import werkzeug.urls
    from urllib.parse import parse_qs
    
    if not hasattr(werkzeug.urls, 'url_decode'):
        def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
            result = {}
            parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)
            for key, values in parsed.items():
                for value in values:
                    result[key] = value
            return result
        
        werkzeug.urls.url_decode = url_decode
        print("Added url_decode to werkzeug.urls")

except ImportError:
    pass
'''
            with open(monkey_patch_file, 'w', encoding='utf-8') as f:
                f.write(monkey_patch_content)
            self.logger.info("✅ تم إنشاء monkey_patch.py")
    
    def clean_previous_builds(self):
        """تنظيف البناءات السابقة"""
        self.logger.info("🧹 تنظيف البناءات السابقة...")
        
        # حذف مجلدات البناء
        for directory in [self.dist_dir, self.build_dir]:
            if directory.exists():
                shutil.rmtree(directory)
                self.logger.info(f"تم حذف: {directory}")
        
        # حذف ملفات spec
        for spec_file in self.base_dir.glob("*.spec"):
            spec_file.unlink()
            self.logger.info(f"تم حذف: {spec_file}")
        
        # حذف مجلدات __pycache__
        for pycache in self.base_dir.rglob("__pycache__"):
            if pycache.is_dir():
                shutil.rmtree(pycache)
        
        # حذف ملفات .pyc
        for pyc_file in self.base_dir.rglob("*.pyc"):
            pyc_file.unlink()
    
    def create_spec_file(self):
        """إنشاء ملف spec محسن"""
        self.logger.info("📝 إنشاء ملف spec محسن...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# المسار الأساسي
base_dir = Path(r'{self.base_dir}')

block_cipher = None

# البيانات المطلوبة
datas = []

# إضافة المجلدات والملفات
data_items = [
    ('app', 'app'),
    ('config.py', '.'),
    ('schema.sql', '.'),
    ('requirements.txt', '.'),
    ('exe_path_fix.py', '.'),
    ('fix_paths.py', '.'),
    ('monkey_patch.py', '.'),
]

# إضافة backend إذا كان موجوداً
if (base_dir / 'backend').exists():
    data_items.append(('backend', 'backend'))

# إضافة الأيقونات إذا كانت موجودة
if (base_dir / 'file.ico').exists():
    data_items.append(('file.ico', '.'))
if (base_dir / 'file.jpeg').exists():
    data_items.append(('file.jpeg', '.'))

# تحويل إلى مسارات كاملة
for src, dst in data_items:
    src_path = base_dir / src
    if src_path.exists():
        datas.append((str(src_path), dst))

a = Analysis(
    ['server_manager.py'],
    pathex=[str(base_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # Flask والمكتبات الأساسية
        'flask', 'flask.app', 'flask.blueprints', 'flask.cli', 'flask.config',
        'flask.ctx', 'flask.globals', 'flask.helpers', 'flask.json', 'flask.logging',
        'flask.sessions', 'flask.signals', 'flask.templating', 'flask.testing',
        'flask.views', 'flask.wrappers',
        
        # Flask Extensions
        'flask_login', 'flask_bcrypt', 'flask_wtf', 'flask_wtf.csrf', 'flask_mail',
        
        # Werkzeug
        'werkzeug', 'werkzeug.serving', 'werkzeug.urls', 'werkzeug.utils',
        'werkzeug.wrappers', 'werkzeug.exceptions', 'werkzeug.routing',
        'werkzeug.security', 'werkzeug.datastructures', 'werkzeug.http',
        'werkzeug.local', 'werkzeug.middleware',
        
        # Jinja2
        'jinja2', 'jinja2.environment', 'jinja2.loaders', 'jinja2.runtime',
        'jinja2.utils', 'jinja2.filters', 'jinja2.tests', 'jinja2.ext',
        
        # WTForms
        'wtforms', 'wtforms.fields', 'wtforms.form', 'wtforms.validators',
        'wtforms.widgets', 'wtforms.csrf',
        
        # PyQt5
        'PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets',
        
        # مكتبات أخرى
        'email_validator', 'itsdangerous', 'markupsafe', 'bcrypt', 'sqlite3',
        'qrcode', 'PIL', 'PIL.Image', 'PIL.ImageDraw', 'PIL.ImageFont',
        
        # مكتبات التطبيق
        'app', 'app.auth', 'app.dashboard', 'app.owners', 'app.properties',
        'app.tenants', 'app.documents', 'app.finance', 'app.reports',
        'app.db', 'app.models', 'app.forms', 'app.utils', 'app.decorators',
        
        # مكتبات النظام
        'datetime', 'os', 'sys', 'json', 'base64', 'hashlib', 'secrets',
        'uuid', 'pathlib', 'shutil', 'tempfile', 'io', 'collections',
        'functools', 'itertools', 're', 'string', 'time', 'calendar',
        'decimal', 'math', 'random', 'platform', 'signal', 'argparse',
        'configparser', 'csv', 'zipfile', 'codecs', 'locale', 'gettext',
        'unicodedata', 'encodings', 'encodings.utf_8', 'encodings.cp1256',
        'encodings.ascii', 'encodings.latin1', 'urllib', 'urllib.parse',
        'socket', 'threading', 'subprocess', 'webbrowser', 'logging',
        'traceback', 'importlib', 'importlib.util',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy', 'IPython',
        'jupyter', 'notebook', 'pytest', 'setuptools', 'distutils',
        'pip', 'wheel', 'pkg_resources', 'test', 'tests', 'unittest',
        'doctest', 'pdb', 'pydoc', 'xmlrpc', 'xml.etree', 'xml.dom',
        'xml.sax', 'html.parser', 'http.server', 'http.client',
        'urllib.robotparser', 'urllib.request', 'urllib.error',
        'ftplib', 'poplib', 'imaplib', 'nntplib', 'smtplib', 'telnetlib',
        'pickle', 'shelve', 'dbm', 'sqlite3.dump', 'multiprocessing',
        'concurrent', 'asyncio', 'queue', 'sched', 'dummy_threading',
        '_thread', 'pty', 'tty', 'pipes', 'posix', 'pwd', 'spwd',
        'grp', 'crypt', 'termios', 'resource', 'nis', 'syslog',
        'commands', 'dl', 'DLFCN', 'ossaudiodev', 'audioop', 'imageop',
        'aifc', 'sunau', 'wave', 'chunk', 'colorsys', 'imghdr',
        'sndhdr', 'turtle', 'cmd', 'shlex',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(base_dir / 'file.ico') if (base_dir / 'file.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=True,
    upx=False,
    upx_exclude=[],
    name='{self.app_name}',
)
'''
        
        spec_file = self.base_dir / f'{self.app_name}_final.spec'
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        self.logger.info(f"✅ تم إنشاء ملف spec: {spec_file}")
        return spec_file
