#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
البناء النهائي الشامل - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import shutil
import subprocess
import time
import json
import zipfile
from pathlib import Path
import logging
from datetime import datetime

class LastAppBuilder:
    """البناء النهائي الشامل للتطبيق"""

    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.build_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.app_name = "مكتب_عصام_الفت"
        self.version = "1.0.0"

        # إعداد التسجيل
        self.setup_logging()

        # مسارات البناء
        self.dist_dir = self.base_dir / 'dist'
        self.build_dir = self.base_dir / 'build'
        self.final_dir = self.base_dir / f'final_build_{self.build_time}'

        self.logger = logging.getLogger('LastAppBuilder')

    def setup_logging(self):
        """إعداد نظام التسجيل"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        # إنشاء مجلد السجلات
        logs_dir = self.base_dir / 'build_logs'
        logs_dir.mkdir(exist_ok=True)

        # ملف السجل
        log_file = logs_dir / f'last_app_build_{self.build_time}.log'

        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

    def print_header(self):
        """طباعة رأس البرنامج"""
        header = f"""
{'='*80}
                    البناء النهائي الشامل
                مكتب عصام الفت لإدارة الأملاك
{'='*80}
تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
{'='*80}
الإصدار: {self.version}
وقت البناء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
{'='*80}
"""
        print(header)
        self.logger.info("بدء البناء النهائي الشامل")

    def check_system_requirements(self):
        """فحص متطلبات النظام"""
        self.logger.info("🔍 فحص متطلبات النظام...")

        # فحص Python
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            raise Exception(f"يتطلب Python 3.8 أو أحدث. الإصدار الحالي: {python_version.major}.{python_version.minor}")

        self.logger.info(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")

        # فحص المساحة المتاحة
        free_space = shutil.disk_usage(self.base_dir).free / (1024**3)  # GB
        if free_space < 2:
            self.logger.warning(f"⚠️ مساحة قليلة متاحة: {free_space:.1f} GB")
        else:
            self.logger.info(f"✅ مساحة متاحة: {free_space:.1f} GB")

        # فحص الملفات المطلوبة
        required_files = [
            'server_manager.py',
            'config.py',
            'schema.sql',
            'requirements.txt'
        ]

        required_dirs = ['app']

        missing_files = []
        for file in required_files:
            if not (self.base_dir / file).exists():
                missing_files.append(file)

        for dir_name in required_dirs:
            if not (self.base_dir / dir_name).exists():
                missing_files.append(f"{dir_name}/")

        if missing_files:
            raise Exception(f"ملفات مطلوبة مفقودة: {', '.join(missing_files)}")

        self.logger.info("✅ جميع الملفات المطلوبة موجودة")

    def install_dependencies(self):
        """تثبيت التبعيات"""
        self.logger.info("📦 تثبيت التبعيات...")

        try:
            # تحديث pip
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], check=True, capture_output=True)

            # تثبيت PyInstaller
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', 'pyinstaller'
            ], check=True, capture_output=True)

            # تثبيت المتطلبات
            requirements_file = self.base_dir / 'requirements.txt'
            if requirements_file.exists():
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
                ], check=True, capture_output=True)

            self.logger.info("✅ تم تثبيت جميع التبعيات")

        except subprocess.CalledProcessError as e:
            self.logger.error(f"❌ فشل في تثبيت التبعيات: {e}")
            raise

    def create_support_files(self):
        """إنشاء ملفات الدعم المطلوبة"""
        self.logger.info("📝 إنشاء ملفات الدعم...")

        # إنشاء exe_path_fix.py إذا لم يكن موجوداً
        exe_fix_file = self.base_dir / 'exe_path_fix.py'
        if not exe_fix_file.exists():
            exe_fix_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""إصلاح مسارات الملف التنفيذي"""

import os
import sys
import logging

def setup_exe_paths():
    """إعداد المسارات للملف التنفيذي"""
    logger = logging.getLogger('exe_path_fix')

    try:
        if getattr(sys, 'frozen', False):
            # في حالة التشغيل كملف تنفيذي
            if hasattr(sys, '_MEIPASS'):
                base_path = sys._MEIPASS
                application_path = os.path.dirname(sys.executable)
            else:
                application_path = os.path.dirname(sys.executable)
                base_path = application_path
        else:
            application_path = os.path.dirname(os.path.abspath(__file__))
            base_path = application_path

        # إنشاء المجلدات المطلوبة
        required_dirs = [
            'instance', 'uploads', 'logs',
            'uploads/buildings', 'uploads/contracts', 'uploads/documents',
            'uploads/owners', 'uploads/tenants', 'uploads/transactions'
        ]

        for dir_name in required_dirs:
            dir_path = os.path.join(application_path, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)

        # إضافة المسارات إلى sys.path
        paths_to_add = [application_path, base_path]
        if getattr(sys, 'frozen', False):
            paths_to_add.extend([
                os.path.join(application_path, 'app'),
                os.path.join(application_path, 'backend'),
                os.path.join(base_path, 'app'),
                os.path.join(base_path, 'backend')
            ])

        for path in paths_to_add:
            if path and os.path.exists(path) and path not in sys.path:
                sys.path.insert(0, path)

        os.chdir(application_path)
        return True

    except Exception as e:
        logger.error(f"Error setting up EXE paths: {e}")
        return False

# تشغيل الإعداد تلقائياً
if __name__ == '__main__' or getattr(sys, 'frozen', False):
    setup_exe_paths()
'''
            with open(exe_fix_file, 'w', encoding='utf-8') as f:
                f.write(exe_fix_content)
            self.logger.info("✅ تم إنشاء exe_path_fix.py")

        # إنشاء fix_paths.py إذا لم يكن موجوداً
        fix_paths_file = self.base_dir / 'fix_paths.py'
        if not fix_paths_file.exists():
            fix_paths_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""إصلاح المسارات العام"""

import os
import sys

print("Path fix loaded successfully")

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
'''
            with open(fix_paths_file, 'w', encoding='utf-8') as f:
                f.write(fix_paths_content)
            self.logger.info("✅ تم إنشاء fix_paths.py")

        # إنشاء monkey_patch.py إذا لم يكن موجوداً
        monkey_patch_file = self.base_dir / 'monkey_patch.py'
        if not monkey_patch_file.exists():
            monkey_patch_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""إصلاحات المكتبات"""

import os
import sys

print("Monkey patch loaded successfully")

try:
    import werkzeug.urls
    from urllib.parse import parse_qs

    if not hasattr(werkzeug.urls, 'url_decode'):
        def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
            result = {}
            parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)
            for key, values in parsed.items():
                for value in values:
                    result[key] = value
            return result

        werkzeug.urls.url_decode = url_decode
        print("Added url_decode to werkzeug.urls")

except ImportError:
    pass
'''
            with open(monkey_patch_file, 'w', encoding='utf-8') as f:
                f.write(monkey_patch_content)
            self.logger.info("✅ تم إنشاء monkey_patch.py")

    def clean_previous_builds(self):
        """تنظيف البناءات السابقة"""
        self.logger.info("🧹 تنظيف البناءات السابقة...")

        # حذف مجلدات البناء
        for directory in [self.dist_dir, self.build_dir]:
            if directory.exists():
                shutil.rmtree(directory)
                self.logger.info(f"تم حذف: {directory}")

        # حذف ملفات spec
        for spec_file in self.base_dir.glob("*.spec"):
            spec_file.unlink()
            self.logger.info(f"تم حذف: {spec_file}")

        # حذف مجلدات __pycache__
        for pycache in self.base_dir.rglob("__pycache__"):
            if pycache.is_dir():
                shutil.rmtree(pycache)

        # حذف ملفات .pyc
        for pyc_file in self.base_dir.rglob("*.pyc"):
            pyc_file.unlink()

    def create_spec_file(self):
        """إنشاء ملف spec محسن"""
        self.logger.info("📝 إنشاء ملف spec محسن...")

        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# المسار الأساسي
base_dir = Path(r'{self.base_dir}')

block_cipher = None

# البيانات المطلوبة
datas = []

# إضافة المجلدات والملفات
data_items = [
    ('app', 'app'),
    ('config.py', '.'),
    ('schema.sql', '.'),
    ('requirements.txt', '.'),
    ('exe_path_fix.py', '.'),
    ('fix_paths.py', '.'),
    ('monkey_patch.py', '.'),
]

# إضافة backend إذا كان موجوداً
if (base_dir / 'backend').exists():
    data_items.append(('backend', 'backend'))

# إضافة الأيقونات إذا كانت موجودة
if (base_dir / 'file.ico').exists():
    data_items.append(('file.ico', '.'))
if (base_dir / 'file.jpeg').exists():
    data_items.append(('file.jpeg', '.'))

# تحويل إلى مسارات كاملة
for src, dst in data_items:
    src_path = base_dir / src
    if src_path.exists():
        datas.append((str(src_path), dst))

a = Analysis(
    ['server_manager.py'],
    pathex=[str(base_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # Flask والمكتبات الأساسية
        'flask', 'flask.app', 'flask.blueprints', 'flask.cli', 'flask.config',
        'flask.ctx', 'flask.globals', 'flask.helpers', 'flask.json', 'flask.logging',
        'flask.sessions', 'flask.signals', 'flask.templating', 'flask.testing',
        'flask.views', 'flask.wrappers',

        # Flask Extensions
        'flask_login', 'flask_bcrypt', 'flask_wtf', 'flask_wtf.csrf', 'flask_mail',

        # Werkzeug
        'werkzeug', 'werkzeug.serving', 'werkzeug.urls', 'werkzeug.utils',
        'werkzeug.wrappers', 'werkzeug.exceptions', 'werkzeug.routing',
        'werkzeug.security', 'werkzeug.datastructures', 'werkzeug.http',
        'werkzeug.local', 'werkzeug.middleware',

        # Jinja2
        'jinja2', 'jinja2.environment', 'jinja2.loaders', 'jinja2.runtime',
        'jinja2.utils', 'jinja2.filters', 'jinja2.tests', 'jinja2.ext',

        # WTForms
        'wtforms', 'wtforms.fields', 'wtforms.form', 'wtforms.validators',
        'wtforms.widgets', 'wtforms.csrf',

        # PyQt5
        'PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets',

        # مكتبات أخرى
        'email_validator', 'itsdangerous', 'markupsafe', 'bcrypt', 'sqlite3',
        'qrcode', 'PIL', 'PIL.Image', 'PIL.ImageDraw', 'PIL.ImageFont',

        # مكتبات التطبيق
        'app', 'app.auth', 'app.dashboard', 'app.owners', 'app.properties',
        'app.tenants', 'app.documents', 'app.finance', 'app.reports',
        'app.db', 'app.models', 'app.forms', 'app.utils', 'app.decorators',

        # مكتبات النظام
        'datetime', 'os', 'sys', 'json', 'base64', 'hashlib', 'secrets',
        'uuid', 'pathlib', 'shutil', 'tempfile', 'io', 'collections',
        'functools', 'itertools', 're', 'string', 'time', 'calendar',
        'decimal', 'math', 'random', 'platform', 'signal', 'argparse',
        'configparser', 'csv', 'zipfile', 'codecs', 'locale', 'gettext',
        'unicodedata', 'encodings', 'encodings.utf_8', 'encodings.cp1256',
        'encodings.ascii', 'encodings.latin1', 'urllib', 'urllib.parse',
        'socket', 'threading', 'subprocess', 'webbrowser', 'logging',
        'traceback', 'importlib', 'importlib.util',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy', 'IPython',
        'jupyter', 'notebook', 'pytest', 'setuptools', 'distutils',
        'pip', 'wheel', 'pkg_resources', 'test', 'tests', 'unittest',
        'doctest', 'pdb', 'pydoc', 'xmlrpc', 'xml.etree', 'xml.dom',
        'xml.sax', 'html.parser', 'http.server', 'http.client',
        'urllib.robotparser', 'urllib.request', 'urllib.error',
        'ftplib', 'poplib', 'imaplib', 'nntplib', 'smtplib', 'telnetlib',
        'pickle', 'shelve', 'dbm', 'sqlite3.dump', 'multiprocessing',
        'concurrent', 'asyncio', 'queue', 'sched', 'dummy_threading',
        '_thread', 'pty', 'tty', 'pipes', 'posix', 'pwd', 'spwd',
        'grp', 'crypt', 'termios', 'resource', 'nis', 'syslog',
        'commands', 'dl', 'DLFCN', 'ossaudiodev', 'audioop', 'imageop',
        'aifc', 'sunau', 'wave', 'chunk', 'colorsys', 'imghdr',
        'sndhdr', 'turtle', 'cmd', 'shlex',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(base_dir / 'file.ico') if (base_dir / 'file.ico').exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=True,
    upx=False,
    upx_exclude=[],
    name='{self.app_name}',
)
'''

        spec_file = self.base_dir / f'{self.app_name}_final.spec'
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)

        self.logger.info(f"✅ تم إنشاء ملف spec: {spec_file}")
        return spec_file

    def build_executable(self):
        """بناء الملف التنفيذي"""
        self.logger.info("🔨 بناء الملف التنفيذي...")

        # إنشاء ملف spec
        spec_file = self.create_spec_file()

        # تشغيل PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            '--log-level=WARN',
            str(spec_file)
        ]

        self.logger.info(f"تشغيل الأمر: {' '.join(cmd)}")

        start_time = time.time()
        try:
            result = subprocess.run(cmd, cwd=self.base_dir, capture_output=True, text=True, timeout=1800)  # 30 دقيقة timeout
            end_time = time.time()

            if result.returncode == 0:
                self.logger.info(f"✅ تم بناء الملف التنفيذي بنجاح في {end_time - start_time:.1f} ثانية")
                return True
            else:
                self.logger.error("❌ فشل في بناء الملف التنفيذي")
                self.logger.error(f"خطأ stdout: {result.stdout}")
                self.logger.error(f"خطأ stderr: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.error("❌ انتهت مهلة البناء (30 دقيقة)")
            return False
        except Exception as e:
            self.logger.error(f"❌ خطأ أثناء البناء: {str(e)}")
            return False

    def setup_exe_environment(self):
        """إعداد بيئة الملف التنفيذي"""
        self.logger.info("⚙️ إعداد بيئة الملف التنفيذي...")

        exe_dir = self.dist_dir / self.app_name
        if not exe_dir.exists():
            self.logger.error("❌ مجلد الملف التنفيذي غير موجود")
            return False

        # إنشاء المجلدات المطلوبة
        required_dirs = [
            'instance', 'uploads', 'logs',
            'uploads/buildings', 'uploads/contracts', 'uploads/documents',
            'uploads/owners', 'uploads/tenants', 'uploads/transactions'
        ]

        for dir_name in required_dirs:
            dir_path = exe_dir / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"✅ تم إنشاء المجلد: {dir_name}")

        # إنشاء ملف تشغيل محسن
        self.create_run_script(exe_dir)

        # إنشاء ملف README
        self.create_readme_file(exe_dir)

        # إنشاء ملف معلومات البناء
        self.create_build_info(exe_dir)

        return True

    def create_run_script(self, exe_dir):
        """إنشاء ملف تشغيل محسن"""
        run_script = exe_dir / 'تشغيل_النظام.bat'

        script_content = f'''@echo off
chcp 65001 >nul
title مكتب عصام الفت لإدارة الأملاك

echo ================================================================
echo                مكتب عصام الفت لإدارة الأملاك
echo ================================================================
echo تم التطوير والبرمجة بواسطة شركة المبرمج المصري
echo فيسبوك: https://www.facebook.com/almbarmg
echo واتساب: 0201032540807
echo جميع الحقوق محفوظة © 2025
echo ================================================================
echo الإصدار: {self.version}
echo تاريخ البناء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
echo ================================================================
echo.
echo 🚀 بدء تشغيل النظام...
echo 🌐 سيكون متاحاً على: http://localhost:5000
echo ⏹️ لإيقاف النظام: أغلق هذه النافذة أو اضغط Ctrl+C
echo.

cd /d "%~dp0"

REM التحقق من وجود الملف التنفيذي
if not exist "{self.app_name}.exe" (
    echo ❌ الملف التنفيذي غير موجود!
    echo تأكد من وجود {self.app_name}.exe في نفس المجلد
    echo.
    pause
    exit /b 1
)

REM تشغيل النظام
"{self.app_name}.exe"

REM معالجة الأخطاء
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام (رمز الخطأ: %errorlevel%)
    echo.
    echo 💡 حلول مقترحة:
    echo 1. تأكد من عدم تشغيل نسخة أخرى من النظام
    echo 2. تأكد من توفر المنفذ 5000
    echo 3. تأكد من عدم حجب البرنامج من Windows Defender
    echo 4. شغل البرنامج كمدير إذا لزم الأمر
    echo.
    echo 📞 للدعم الفني: واتساب 0201032540807
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
    echo.
)
'''

        with open(run_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        self.logger.info(f"✅ تم إنشاء ملف التشغيل: {run_script}")

    def create_readme_file(self, exe_dir):
        """إنشاء ملف README"""
        readme_file = exe_dir / 'اقرأني.txt'

        readme_content = f'''مكتب عصام الفت لإدارة الأملاك
================================

📋 معلومات التطبيق:
الاسم: مكتب عصام الفت لإدارة الأملاك
الإصدار: {self.version}
تاريخ البناء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

🚀 طريقة التشغيل:
1. شغل ملف "تشغيل_النظام.bat"
2. أو شغل "{self.app_name}.exe" مباشرة
3. افتح المتصفح واذهب إلى: http://localhost:5000

💻 متطلبات النظام:
- Windows 7 أو أحدث (64-bit)
- 4 جيجابايت رام على الأقل
- 500 ميجابايت مساحة فارغة
- اتصال بالإنترنت (اختياري)

📁 هيكل الملفات:
{self.app_name}.exe     - الملف التنفيذي الرئيسي
تشغيل_النظام.bat      - ملف التشغيل المساعد
اقرأني.txt            - هذا الملف
معلومات_البناء.json   - معلومات تقنية عن البناء

instance/              - قاعدة البيانات والإعدادات
uploads/               - الملفات المرفوعة
logs/                  - ملفات السجلات

🔧 استكشاف الأخطاء:

❌ المشكلة: لا يعمل البرنامج
✅ الحل:
1. تأكد من عدم حجب Windows Defender للبرنامج
2. شغل البرنامج كمدير
3. تأكد من عدم استخدام المنفذ 5000 من برنامج آخر

❌ المشكلة: لا يمكن الوصول للموقع
✅ الحل:
1. تأكد من تشغيل البرنامج بنجاح
2. جرب http://127.0.0.1:5000 بدلاً من localhost
3. تأكد من إعدادات Firewall

❌ المشكلة: خطأ في قاعدة البيانات
✅ الحل:
1. احذف مجلد instance واعد تشغيل البرنامج
2. تأكد من صلاحيات الكتابة في المجلد

🌐 الوصول من الشبكة:
لجعل النظام متاحاً لأجهزة أخرى على الشبكة:
1. تأكد من إعدادات Firewall
2. استخدم عنوان IP الخاص بالجهاز
3. تأكد من فتح المنفذ 5000

🔐 الأمان:
- غير كلمة المرور الافتراضية عند أول تشغيل
- قم بعمل نسخ احتياطية دورية من مجلد instance
- لا تشارك ملفات النظام مع أشخاص غير موثوقين

📞 الدعم الفني:
شركة المبرمج المصري
واتساب: 0201032540807
فيسبوك: https://www.facebook.com/almbarmg
ساعات الدعم: الأحد - الخميس (9 ص - 6 م)

عند طلب الدعم، يرجى تقديم:
1. وصف مفصل للمشكلة
2. رسائل الخطأ (إن وجدت)
3. نظام التشغيل وإصداره
4. خطوات إعادة إنتاج المشكلة

جميع الحقوق محفوظة © 2025 - شركة المبرمج المصري
'''

        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        self.logger.info(f"✅ تم إنشاء ملف README: {readme_file}")

    def create_build_info(self, exe_dir):
        """إنشاء ملف معلومات البناء"""
        build_info = {
            "app_name": self.app_name,
            "version": self.version,
            "build_time": self.build_time,
            "build_date": datetime.now().isoformat(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "platform": sys.platform,
            "architecture": "64-bit" if sys.maxsize > 2**32 else "32-bit",
            "builder": "شركة المبرمج المصري",
            "contact": {
                "whatsapp": "0201032540807",
                "facebook": "https://www.facebook.com/almbarmg"
            },
            "features": [
                "إدارة شاملة للعقارات",
                "إدارة المالكين والمستأجرين",
                "إدارة العقود والمستندات",
                "تقارير مالية مفصلة",
                "واجهة عربية بالكامل",
                "قاعدة بيانات SQLite",
                "واجهة ويب حديثة",
                "نظام مصادقة آمن"
            ],
            "technical_specs": {
                "framework": "Flask",
                "gui": "PyQt5",
                "database": "SQLite",
                "packaging": "PyInstaller",
                "encoding": "UTF-8"
            }
        }

        build_info_file = exe_dir / 'معلومات_البناء.json'
        with open(build_info_file, 'w', encoding='utf-8') as f:
            json.dump(build_info, f, ensure_ascii=False, indent=2)

        self.logger.info(f"✅ تم إنشاء ملف معلومات البناء: {build_info_file}")

    def create_final_package(self):
        """إنشاء الحزمة النهائية"""
        self.logger.info("📦 إنشاء الحزمة النهائية...")

        exe_dir = self.dist_dir / self.app_name
        if not exe_dir.exists():
            self.logger.error("❌ مجلد الملف التنفيذي غير موجود")
            return False

        # إنشاء مجلد البناء النهائي
        self.final_dir.mkdir(exist_ok=True)

        # نسخ مجلد التطبيق
        final_app_dir = self.final_dir / self.app_name
        if final_app_dir.exists():
            shutil.rmtree(final_app_dir)

        shutil.copytree(exe_dir, final_app_dir)
        self.logger.info(f"✅ تم نسخ التطبيق إلى: {final_app_dir}")

        # إنشاء ملف zip
        zip_file = self.final_dir / f"{self.app_name}_{self.build_time}.zip"
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for file_path in final_app_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(final_app_dir.parent)
                    zf.write(file_path, arcname)

        self.logger.info(f"✅ تم إنشاء ملف ZIP: {zip_file}")

        # حساب حجم الملفات
        total_size = sum(f.stat().st_size for f in final_app_dir.rglob('*') if f.is_file())
        zip_size = zip_file.stat().st_size

        self.logger.info(f"📊 حجم التطبيق: {total_size / (1024*1024):.1f} MB")
        self.logger.info(f"📊 حجم ملف ZIP: {zip_size / (1024*1024):.1f} MB")

        return True

    def run_tests(self):
        """تشغيل اختبارات سريعة"""
        self.logger.info("🧪 تشغيل اختبارات سريعة...")

        exe_file = self.dist_dir / self.app_name / f"{self.app_name}.exe"

        # فحص وجود الملف التنفيذي
        if not exe_file.exists():
            self.logger.error("❌ الملف التنفيذي غير موجود")
            return False

        # فحص حجم الملف
        file_size = exe_file.stat().st_size / (1024*1024)  # MB
        if file_size < 10:
            self.logger.warning(f"⚠️ حجم الملف التنفيذي صغير: {file_size:.1f} MB")
        else:
            self.logger.info(f"✅ حجم الملف التنفيذي: {file_size:.1f} MB")

        # فحص المجلدات المطلوبة
        required_dirs = ['instance', 'uploads', 'logs']
        exe_dir = exe_file.parent

        for dir_name in required_dirs:
            dir_path = exe_dir / dir_name
            if dir_path.exists():
                self.logger.info(f"✅ المجلد موجود: {dir_name}")
            else:
                self.logger.error(f"❌ المجلد مفقود: {dir_name}")
                return False

        # فحص ملفات الدعم
        support_files = ['تشغيل_النظام.bat', 'اقرأني.txt', 'معلومات_البناء.json']
        for file_name in support_files:
            file_path = exe_dir / file_name
            if file_path.exists():
                self.logger.info(f"✅ الملف موجود: {file_name}")
            else:
                self.logger.warning(f"⚠️ الملف مفقود: {file_name}")

        self.logger.info("✅ اكتملت الاختبارات السريعة")
        return True

    def generate_report(self):
        """إنشاء تقرير البناء"""
        self.logger.info("📋 إنشاء تقرير البناء...")

        report = {
            "build_summary": {
                "app_name": self.app_name,
                "version": self.version,
                "build_time": self.build_time,
                "build_date": datetime.now().isoformat(),
                "status": "success"
            },
            "system_info": {
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "platform": sys.platform,
                "architecture": "64-bit" if sys.maxsize > 2**32 else "32-bit"
            },
            "build_paths": {
                "base_dir": str(self.base_dir),
                "dist_dir": str(self.dist_dir),
                "final_dir": str(self.final_dir)
            },
            "output_files": {
                "executable": f"{self.app_name}.exe",
                "run_script": "تشغيل_النظام.bat",
                "readme": "اقرأني.txt",
                "build_info": "معلومات_البناء.json"
            }
        }

        # حساب أحجام الملفات
        exe_file = self.dist_dir / self.app_name / f"{self.app_name}.exe"
        if exe_file.exists():
            report["file_sizes"] = {
                "executable_mb": round(exe_file.stat().st_size / (1024*1024), 2)
            }

            # حساب حجم المجلد الكامل
            total_size = sum(f.stat().st_size for f in exe_file.parent.rglob('*') if f.is_file())
            report["file_sizes"]["total_mb"] = round(total_size / (1024*1024), 2)

        # حفظ التقرير
        report_file = self.final_dir / f"build_report_{self.build_time}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        self.logger.info(f"✅ تم إنشاء تقرير البناء: {report_file}")

        # طباعة ملخص
        self.print_summary(report)

    def print_summary(self, report):
        """طباعة ملخص البناء"""
        summary = f"""
{'='*80}
                            ملخص البناء النهائي
{'='*80}
✅ تم إنشاء التطبيق بنجاح!

📋 معلومات التطبيق:
   الاسم: {report['build_summary']['app_name']}
   الإصدار: {report['build_summary']['version']}
   تاريخ البناء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📁 المجلدات:
   المجلد النهائي: {self.final_dir}
   مجلد التطبيق: {self.final_dir / self.app_name}

📊 أحجام الملفات:"""

        if "file_sizes" in report:
            summary += f"""
   الملف التنفيذي: {report['file_sizes']['executable_mb']} MB
   المجلد الكامل: {report['file_sizes']['total_mb']} MB"""

        summary += f"""

🚀 طريقة التشغيل:
   1. اذهب إلى: {self.final_dir / self.app_name}
   2. شغل: تشغيل_النظام.bat
   3. أو شغل: {self.app_name}.exe

📦 للتوزيع:
   استخدم ملف ZIP: {self.app_name}_{self.build_time}.zip

📞 الدعم الفني:
   واتساب: 0201032540807
   فيسبوك: https://www.facebook.com/almbarmg

{'='*80}
                        تم البناء بنجاح! 🎉
{'='*80}
"""

        print(summary)
        self.logger.info("تم طباعة ملخص البناء")

    def build(self):
        """البناء الكامل"""
        try:
            # طباعة الرأس
            self.print_header()

            # فحص متطلبات النظام
            self.check_system_requirements()

            # تثبيت التبعيات
            self.install_dependencies()

            # إنشاء ملفات الدعم
            self.create_support_files()

            # تنظيف البناءات السابقة
            self.clean_previous_builds()

            # بناء الملف التنفيذي
            if not self.build_executable():
                self.logger.error("❌ فشل في بناء الملف التنفيذي")
                return False

            # إعداد بيئة الملف التنفيذي
            if not self.setup_exe_environment():
                self.logger.error("❌ فشل في إعداد بيئة الملف التنفيذي")
                return False

            # تشغيل الاختبارات
            if not self.run_tests():
                self.logger.warning("⚠️ فشلت بعض الاختبارات")

            # إنشاء الحزمة النهائية
            if not self.create_final_package():
                self.logger.error("❌ فشل في إنشاء الحزمة النهائية")
                return False

            # إنشاء التقرير
            self.generate_report()

            self.logger.info("🎉 تم إكمال البناء النهائي بنجاح!")
            return True

        except Exception as e:
            self.logger.error(f"❌ خطأ أثناء البناء: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

def main():
    """الدالة الرئيسية"""
    try:
        builder = LastAppBuilder()
        success = builder.build()

        if success:
            print("\n🎉 تم إنشاء التطبيق النهائي بنجاح!")
            print(f"📁 يمكنك العثور على التطبيق في: {builder.final_dir}")
        else:
            print("\n❌ فشل في إنشاء التطبيق النهائي")
            print("📋 راجع ملفات السجل للتفاصيل")

        input("\nاضغط Enter للخروج...")
        return 0 if success else 1

    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البناء بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
