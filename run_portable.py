#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل النسخة المحمولة لمكتب العقارات

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
import argparse
import webbrowser
from pathlib import Path

def setup_portable_environment():
    """إعداد بيئة النسخة المحمولة"""
    
    # تحديد المجلد الأساسي
    if getattr(sys, 'frozen', False):
        app_dir = Path(sys.executable).parent
    else:
        app_dir = Path(__file__).parent
    
    # إضافة مجلد التطبيق إلى مسار البحث
    if str(app_dir) not in sys.path:
        sys.path.insert(0, str(app_dir))
    
    # إعداد متغيرات البيئة
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['APP_DIR'] = str(app_dir)
    
    # تغيير مجلد العمل
    os.chdir(app_dir)
    
    return app_dir

def check_requirements():
    """التحقق من المتطلبات"""
    
    print("التحقق من المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 8):
        print("خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    # التحقق من الوحدات المطلوبة
    required_modules = [
        'flask',
        'flask_login',
        'flask_bcrypt',
        'flask_wtf',
        'sqlite3'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"الوحدات المفقودة: {', '.join(missing_modules)}")
        print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
        return False
    
    print("✓ جميع المتطلبات متوفرة")
    return True

def initialize_database(app_dir):
    """تهيئة قاعدة البيانات"""
    
    db_path = app_dir / "instance" / "realestate.db"
    
    if db_path.exists():
        print("✓ قاعدة البيانات موجودة")
        return True
    
    print("إنشاء قاعدة البيانات...")
    
    try:
        import sqlite3
        from datetime import datetime
        
        # إنشاء مجلد instance
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # قراءة ملف schema.sql
        schema_path = app_dir / "schema.sql"
        if not schema_path.exists():
            print("خطأ: ملف schema.sql غير موجود")
            return False
        
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # إنشاء قاعدة البيانات
        conn = sqlite3.connect(str(db_path))
        conn.executescript(schema_sql)
        
        # إنشاء مستخدم افتراضي
        try:
            import bcrypt
            username = "admin"
            password = "admin123"
            name = "مدير النظام"
            email = "<EMAIL>"
            role = "admin"
            
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            conn.execute('''
            INSERT INTO users (username, password, name, email, role, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, hashed_password, name, email, role, datetime.now()))
            
        except ImportError:
            # استخدام تشفير بسيط إذا لم يكن bcrypt متوفراً
            import hashlib
            username = "admin"
            password = "admin123"
            name = "مدير النظام"
            email = "<EMAIL>"
            role = "admin"
            
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            conn.execute('''
            INSERT INTO users (username, password, name, email, role, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, hashed_password, name, email, role, datetime.now()))
        
        conn.commit()
        conn.close()
        
        print("✓ تم إنشاء قاعدة البيانات بنجاح")
        print(f"المستخدم الافتراضي: {username}")
        print(f"كلمة المرور: {password}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")
        return False

def run_server(host='127.0.0.1', port=5000, debug=False, network=False):
    """تشغيل الخادم"""
    
    print("=" * 60)
    print("مكتب عصام الفت لإدارة الأملاك - النسخة المحمولة")
    print("=" * 60)
    
    # إعداد البيئة
    app_dir = setup_portable_environment()
    
    # التحقق من المتطلبات
    if not check_requirements():
        return False
    
    # تهيئة قاعدة البيانات
    if not initialize_database(app_dir):
        return False
    
    # تحديد المضيف
    if network:
        host = "0.0.0.0"
        print("تشغيل الخادم على الشبكة...")
    else:
        print("تشغيل الخادم محلياً...")
    
    print(f"المضيف: {host}")
    print(f"المنفذ: {port}")
    print(f"وضع التطوير: {'نعم' if debug else 'لا'}")
    print("-" * 60)
    
    try:
        # استيراد التطبيق
        from app import create_app
        
        # إنشاء التطبيق
        app = create_app()
        
        # عرض معلومات الوصول
        if host == "0.0.0.0":
            try:
                import socket
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                s.close()
                print(f"يمكن الوصول إلى الخادم من: http://{local_ip}:{port}")
                print(f"أو من: http://localhost:{port}")
            except:
                print(f"يمكن الوصول إلى الخادم من: http://localhost:{port}")
        else:
            print(f"يمكن الوصول إلى الخادم من: http://{host}:{port}")
        
        print("اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)
        
        # فتح المتصفح
        try:
            url = f"http://{'localhost' if host == '0.0.0.0' else host}:{port}"
            webbrowser.open(url)
        except:
            pass
        
        # تشغيل الخادم
        app.run(host=host, port=port, debug=debug, threaded=True)
        
        return True
        
    except KeyboardInterrupt:
        print("\nتم إيقاف الخادم بواسطة المستخدم.")
        return True
    except Exception as e:
        print(f"خطأ في تشغيل الخادم: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    parser = argparse.ArgumentParser(description='تشغيل النسخة المحمولة لمكتب عصام الفت')
    parser.add_argument('--host', default='127.0.0.1', help='عنوان IP للخادم')
    parser.add_argument('--port', type=int, default=5000, help='رقم المنفذ')
    parser.add_argument('--network', action='store_true', help='تشغيل على الشبكة')
    parser.add_argument('--debug', action='store_true', help='تشغيل في وضع التطوير')
    
    args = parser.parse_args()
    
    # تشغيل الخادم
    success = run_server(
        host=args.host,
        port=args.port,
        debug=args.debug,
        network=args.network
    )
    
    if not success:
        print("فشل في تشغيل الخادم")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
