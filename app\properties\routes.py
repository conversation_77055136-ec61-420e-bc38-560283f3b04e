from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from app.properties import properties_bp
from app.forms import BuildingForm, UnitForm, MeterReadingForm, MaintenanceRequestForm
from app.db import query_db, insert_db, update_db, delete_db
from app.decorators import data_entry_required
from datetime import datetime

@properties_bp.route('/buildings')
@login_required
def buildings():
    """قائمة المباني مع دعم البحث والفلترة"""
    # الحصول على معلمات البحث والفلترة
    search_query = request.args.get('search', '')
    owner_id = request.args.get('owner_id', '')
    city = request.args.get('city', '')
    sort_by = request.args.get('sort_by', 'name')
    display_mode = request.args.get('display_mode', 'table')  # إضافة معلمة طريقة العرض

    # بناء استعلام SQL الأساسي
    base_query = '''
        SELECT b.*, o.name as owner_name
        FROM buildings b
        JOIN owners o ON b.owner_id = o.id
        WHERE 1=1
    '''

    # إعداد قائمة المعلمات
    params = []

    # إضافة شروط البحث
    if search_query:
        base_query += " AND (b.name LIKE ? OR b.address LIKE ? OR b.building_code LIKE ?)"
        search_param = f'%{search_query}%'
        params.extend([search_param, search_param, search_param])

    # إضافة فلتر المالك
    if owner_id:
        base_query += " AND b.owner_id = ?"
        params.append(owner_id)

    # إضافة فلتر المدينة
    if city:
        base_query += " AND b.city = ?"
        params.append(city)

    # إضافة الترتيب
    if sort_by == 'name':
        base_query += " ORDER BY b.name"
    elif sort_by == 'building_code':
        base_query += " ORDER BY b.building_code"
    elif sort_by == 'city':
        base_query += " ORDER BY b.city, b.name"
    elif sort_by == 'owner':
        base_query += " ORDER BY o.name, b.name"
    else:
        base_query += " ORDER BY b.name"  # الترتيب الافتراضي

    # تنفيذ الاستعلام
    buildings_list = query_db(base_query, params)

    # الحصول على قائمة الملاك للفلترة
    owners_list = query_db('SELECT id, name FROM owners ORDER BY name')

    # الحصول على قائمة المدن الفريدة للفلترة
    cities = query_db('SELECT DISTINCT city FROM buildings WHERE city IS NOT NULL AND city != "" ORDER BY city')
    cities = [city['city'] for city in cities]

    return render_template('properties/buildings.html',
                          buildings=buildings_list,
                          owners_list=owners_list,
                          cities=cities,
                          search_query=search_query,
                          owner_id=owner_id,
                          city=city,
                          sort_by=sort_by,
                          display_mode=display_mode,  # إضافة معلمة طريقة العرض
                          title='إدارة المباني')

@properties_bp.route('/buildings/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add_building():
    """إضافة مبنى جديد"""
    form = BuildingForm()

    # تحميل قائمة الملاك للاختيار
    owners = query_db('SELECT id, name FROM owners ORDER BY name')
    form.owner_id.choices = [(owner['id'], owner['name']) for owner in owners]

    if form.validate_on_submit():
        # إذا لم يتم تحديد رقم العقار، قم بإنشاء رقم جديد
        building_code = form.building_code.data
        if not building_code:
            # الحصول على أعلى رقم عقار موجود
            max_code = query_db('SELECT MAX(CAST(building_code AS INTEGER)) as max_code FROM buildings', one=True)
            if max_code['max_code']:
                building_code = str(int(max_code['max_code']) + 1)
            else:
                building_code = '1001'  # رقم البداية الافتراضي

        building_id = insert_db('buildings', {
            'name': form.name.data,
            'building_code': building_code,
            'address': form.address.data,
            'district': form.district.data,
            'city': form.city.data,
            'owner_id': form.owner_id.data,
            'agent': form.agent.data,
            'type': form.type.data,
            'usage': form.usage.data,
            'deed_number': form.deed_number.data,
            'deed_date': form.deed_date.data,
            'deed_type': form.deed_type.data,
            'deed_issuer': form.deed_issuer.data,
            'building_number': form.building_number.data,
            'additional_number': form.additional_number.data,
            'postal_code': form.postal_code.data,
            'old_file': form.old_file.data,
            'build_year': form.build_year.data,
            'floors_count': form.floors_count.data,
            'notes': form.notes.data
        })

        flash('تم إضافة المبنى بنجاح!', 'success')
        return redirect(url_for('properties.view_building', building_id=building_id))

    return render_template('properties/building_form.html', form=form, title='إضافة مبنى جديد')

@properties_bp.route('/buildings/edit/<int:building_id>', methods=['GET', 'POST'])
@login_required
@data_entry_required
def edit_building(building_id):
    """تعديل مبنى"""
    building = query_db('SELECT * FROM buildings WHERE id = ?', (building_id,), one=True)

    if not building:
        flash('المبنى غير موجود.', 'danger')
        return redirect(url_for('properties.buildings'))

    form = BuildingForm()

    # تحميل قائمة الملاك للاختيار
    owners = query_db('SELECT id, name FROM owners ORDER BY name')
    form.owner_id.choices = [(owner['id'], owner['name']) for owner in owners]

    if request.method == 'GET':
        form.name.data = building['name']
        form.building_code.data = building['building_code']
        form.address.data = building['address']
        form.district.data = building['district']
        form.city.data = building['city']
        form.owner_id.data = building['owner_id']
        form.agent.data = building['agent']
        form.type.data = building['type']
        form.usage.data = building['usage']
        form.deed_number.data = building['deed_number']
        form.deed_date.data = building['deed_date']
        form.deed_type.data = building['deed_type']
        form.deed_issuer.data = building['deed_issuer']
        form.building_number.data = building['building_number']
        form.additional_number.data = building['additional_number']
        form.postal_code.data = building['postal_code']
        form.old_file.data = building['old_file']
        form.build_year.data = building['build_year']
        form.floors_count.data = building['floors_count']
        form.notes.data = building['notes']

    if form.validate_on_submit():
        update_db('buildings', building_id, {
            'name': form.name.data,
            'building_code': form.building_code.data,
            'address': form.address.data,
            'district': form.district.data,
            'city': form.city.data,
            'owner_id': form.owner_id.data,
            'agent': form.agent.data,
            'type': form.type.data,
            'usage': form.usage.data,
            'deed_number': form.deed_number.data,
            'deed_date': form.deed_date.data,
            'deed_type': form.deed_type.data,
            'deed_issuer': form.deed_issuer.data,
            'building_number': form.building_number.data,
            'additional_number': form.additional_number.data,
            'postal_code': form.postal_code.data,
            'old_file': form.old_file.data,
            'build_year': form.build_year.data,
            'floors_count': form.floors_count.data,
            'notes': form.notes.data
        })

        flash('تم تحديث بيانات المبنى بنجاح!', 'success')
        return redirect(url_for('properties.view_building', building_id=building_id))

    return render_template('properties/building_form.html', form=form, title='تعديل بيانات المبنى', building=building)

@properties_bp.route('/buildings/view/<int:building_id>')
@login_required
def view_building(building_id):
    """عرض تفاصيل المبنى"""
    building = query_db('''
        SELECT b.*, o.name as owner_name
        FROM buildings b
        JOIN owners o ON b.owner_id = o.id
        WHERE b.id = ?
    ''', (building_id,), one=True)

    if not building:
        flash('المبنى غير موجود.', 'danger')
        return redirect(url_for('properties.buildings'))

    # الحصول على الوحدات التابعة للمبنى
    units = query_db('''
        SELECT u.*,
               CASE
                   WHEN u.status = 'occupied' THEN
                       (SELECT t.name FROM contracts c JOIN tenants t ON c.tenant_id = t.id
                        WHERE c.unit_id = u.id AND c.status = 'active' LIMIT 1)
                   ELSE NULL
               END as tenant_name
        FROM units u
        WHERE u.building_id = ?
        ORDER BY u.unit_number
    ''', (building_id,))

    # الحصول على المستندات المرتبطة بالمبنى
    documents = query_db('''
        SELECT d.*, u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.related_to = 'building' AND d.related_id = ?
        ORDER BY d.upload_date DESC
    ''', (building_id,))

    # الحصول على طلبات الصيانة المرتبطة بالمبنى
    maintenance_requests = query_db('''
        SELECT m.*, u.unit_number
        FROM maintenance_requests m
        JOIN units u ON m.unit_id = u.id
        WHERE u.building_id = ?
        ORDER BY m.request_date DESC
        LIMIT 5
    ''', (building_id,))

    return render_template('properties/view_building.html',
                           building=building,
                           units=units,
                           documents=documents,
                           maintenance_requests=maintenance_requests,
                           title=f'تفاصيل المبنى: {building["name"]}')

@properties_bp.route('/buildings/delete/<int:building_id>', methods=['POST'])
@login_required
@data_entry_required
def delete_building(building_id):
    """حذف مبنى"""
    building = query_db('SELECT * FROM buildings WHERE id = ?', (building_id,), one=True)

    if not building:
        flash('المبنى غير موجود.', 'danger')
        return redirect(url_for('properties.buildings'))

    # التحقق من وجود وحدات مرتبطة بالمبنى
    units = query_db('SELECT COUNT(*) as count FROM units WHERE building_id = ?', (building_id,), one=True)

    if units['count'] > 0:
        flash('لا يمكن حذف المبنى لأنه يحتوي على وحدات. يرجى حذف الوحدات أولاً.', 'danger')
        return redirect(url_for('properties.view_building', building_id=building_id))

    # حذف المستندات المرتبطة بالمبنى
    delete_db('documents', building_id)

    # حذف المبنى
    delete_db('buildings', building_id)

    flash('تم حذف المبنى بنجاح!', 'success')
    return redirect(url_for('properties.buildings'))

@properties_bp.route('/units')
@login_required
def units():
    """قائمة الوحدات مع دعم البحث والفلترة"""
    # الحصول على معلمات البحث والفلترة
    search_query = request.args.get('search', '')
    building_id = request.args.get('building_id', '')
    type = request.args.get('type', '')
    status = request.args.get('status', '')
    min_rent = request.args.get('min_rent', '')
    max_rent = request.args.get('max_rent', '')
    min_rooms = request.args.get('min_rooms', '')
    sort_by = request.args.get('sort_by', 'building')
    display_mode = request.args.get('display_mode', 'table')  # إضافة معلمة طريقة العرض

    # بناء استعلام SQL الأساسي
    base_query = '''
        SELECT u.*, b.name as building_name,
               CASE
                   WHEN u.status = 'occupied' THEN
                       (SELECT t.name FROM contracts c JOIN tenants t ON c.tenant_id = t.id
                        WHERE c.unit_id = u.id AND c.status = 'active' LIMIT 1)
                   ELSE NULL
               END as tenant_name
        FROM units u
        JOIN buildings b ON u.building_id = b.id
        WHERE 1=1
    '''

    # إعداد قائمة المعلمات
    params = []

    # إضافة شروط البحث
    if search_query:
        base_query += " AND (u.unit_number LIKE ? OR u.site_number_electricity LIKE ? OR u.meter_number_electricity LIKE ? OR u.water_meter_number LIKE ?)"
        search_param = f'%{search_query}%'
        params.extend([search_param, search_param, search_param, search_param])

    # إضافة فلتر المبنى
    if building_id:
        base_query += " AND u.building_id = ?"
        params.append(building_id)

    # إضافة فلتر نوع الوحدة
    if type:
        base_query += " AND u.type = ?"
        params.append(type)

    # إضافة فلتر حالة الوحدة
    if status:
        base_query += " AND u.status = ?"
        params.append(status)

    # إضافة فلتر الإيجار الأدنى
    if min_rent:
        base_query += " AND u.rent_amount >= ?"
        params.append(min_rent)

    # إضافة فلتر الإيجار الأقصى
    if max_rent:
        base_query += " AND u.rent_amount <= ?"
        params.append(max_rent)

    # إضافة فلتر عدد الغرف
    if min_rooms:
        base_query += " AND u.rooms_count >= ?"
        params.append(min_rooms)

    # إضافة الترتيب
    if sort_by == 'building':
        base_query += " ORDER BY b.name, u.unit_number"
    elif sort_by == 'unit_number':
        base_query += " ORDER BY u.unit_number"
    elif sort_by == 'rent_asc':
        base_query += " ORDER BY u.rent_amount ASC"
    elif sort_by == 'rent_desc':
        base_query += " ORDER BY u.rent_amount DESC"
    elif sort_by == 'rooms':
        base_query += " ORDER BY u.rooms_count DESC, u.rent_amount DESC"
    else:
        base_query += " ORDER BY b.name, u.unit_number"  # الترتيب الافتراضي

    # تنفيذ الاستعلام
    units_list = query_db(base_query, params)

    # الحصول على قائمة المباني للفلترة
    buildings_list = query_db('SELECT id, name FROM buildings ORDER BY name')

    return render_template('properties/units.html',
                          units=units_list,
                          buildings_list=buildings_list,
                          search_query=search_query,
                          building_id=building_id,
                          type=type,
                          status=status,
                          min_rent=min_rent,
                          max_rent=max_rent,
                          min_rooms=min_rooms,
                          sort_by=sort_by,
                          display_mode=display_mode,  # إضافة معلمة طريقة العرض
                          title='إدارة الوحدات')

@properties_bp.route('/units/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add_unit():
    """إضافة وحدة جديدة"""
    form = UnitForm()

    # تحميل قائمة المباني للاختيار
    buildings = query_db('SELECT id, name FROM buildings ORDER BY name')
    form.building_id.choices = [(building['id'], building['name']) for building in buildings]

    if form.validate_on_submit():
        unit_id = insert_db('units', {
            'unit_number': form.unit_number.data,
            'building_id': form.building_id.data,
            'floor_number': form.floor_number.data,
            'type': form.type.data,
            'area': form.area.data,
            'rooms_count': form.rooms_count.data,
            'bathrooms_count': form.bathrooms_count.data,
            'rent_amount': form.rent_amount.data,
            'status': form.status.data,
            'site_number_electricity': form.site_number_electricity.data,
            'meter_number_electricity': form.meter_number_electricity.data,
            'water_meter_number': form.water_meter_number.data,
            'water_meter_account': form.water_meter_account.data,
            'electricity_meter_type': form.electricity_meter_type.data,
            'water_meter_type': form.water_meter_type.data,
            'notes': form.notes.data
        })

        flash('تم إضافة الوحدة بنجاح!', 'success')
        return redirect(url_for('properties.view_unit', unit_id=unit_id))

    return render_template('properties/unit_form.html', form=form, title='إضافة وحدة جديدة')

@properties_bp.route('/units/edit/<int:unit_id>', methods=['GET', 'POST'])
@login_required
@data_entry_required
def edit_unit(unit_id):
    """تعديل وحدة"""
    unit = query_db('SELECT * FROM units WHERE id = ?', (unit_id,), one=True)

    if not unit:
        flash('الوحدة غير موجودة.', 'danger')
        return redirect(url_for('properties.units'))

    form = UnitForm()

    # تحميل قائمة المباني للاختيار
    buildings = query_db('SELECT id, name FROM buildings ORDER BY name')
    form.building_id.choices = [(building['id'], building['name']) for building in buildings]

    if request.method == 'GET':
        form.unit_number.data = unit['unit_number']
        form.building_id.data = unit['building_id']
        form.floor_number.data = unit['floor_number']
        form.type.data = unit['type']
        form.area.data = unit['area']
        form.rooms_count.data = unit['rooms_count']
        form.bathrooms_count.data = unit['bathrooms_count']
        form.rent_amount.data = unit['rent_amount']
        form.status.data = unit['status']
        form.site_number_electricity.data = unit['site_number_electricity']
        form.meter_number_electricity.data = unit['meter_number_electricity']
        form.water_meter_number.data = unit['water_meter_number']
        form.water_meter_account.data = unit['water_meter_account']
        form.electricity_meter_type.data = unit['electricity_meter_type']
        form.water_meter_type.data = unit['water_meter_type']
        form.notes.data = unit['notes']

    if form.validate_on_submit():
        update_db('units', unit_id, {
            'unit_number': form.unit_number.data,
            'building_id': form.building_id.data,
            'floor_number': form.floor_number.data,
            'type': form.type.data,
            'area': form.area.data,
            'rooms_count': form.rooms_count.data,
            'bathrooms_count': form.bathrooms_count.data,
            'rent_amount': form.rent_amount.data,
            'status': form.status.data,
            'site_number_electricity': form.site_number_electricity.data,
            'meter_number_electricity': form.meter_number_electricity.data,
            'water_meter_number': form.water_meter_number.data,
            'water_meter_account': form.water_meter_account.data,
            'electricity_meter_type': form.electricity_meter_type.data,
            'water_meter_type': form.water_meter_type.data,
            'notes': form.notes.data
        })

        flash('تم تحديث بيانات الوحدة بنجاح!', 'success')
        return redirect(url_for('properties.view_unit', unit_id=unit_id))

    return render_template('properties/unit_form.html', form=form, title='تعديل بيانات الوحدة', unit=unit)

@properties_bp.route('/units/view/<int:unit_id>')
@login_required
def view_unit(unit_id):
    """عرض تفاصيل الوحدة"""
    unit = query_db('''
        SELECT u.*, b.name as building_name, b.address as building_address
        FROM units u
        JOIN buildings b ON u.building_id = b.id
        WHERE u.id = ?
    ''', (unit_id,), one=True)

    if not unit:
        flash('الوحدة غير موجودة.', 'danger')
        return redirect(url_for('properties.units'))

    # الحصول على العقد النشط للوحدة (إن وجد)
    active_contract = query_db('''
        SELECT c.*, t.name as tenant_name, t.phone as tenant_phone
        FROM contracts c
        JOIN tenants t ON c.tenant_id = t.id
        WHERE c.unit_id = ? AND c.status = 'active'
        LIMIT 1
    ''', (unit_id,), one=True)

    # الحصول على سجل العقود السابقة للوحدة
    contracts_history = query_db('''
        SELECT c.*, t.name as tenant_name
        FROM contracts c
        JOIN tenants t ON c.tenant_id = t.id
        WHERE c.unit_id = ? AND c.status != 'active'
        ORDER BY c.end_date DESC
    ''', (unit_id,))

    # الحصول على قراءات العدادات
    meter_readings = query_db('''
        SELECT m.*, u.name as created_by_name
        FROM meter_readings m
        LEFT JOIN users u ON m.created_by = u.id
        WHERE m.unit_id = ?
        ORDER BY m.reading_date DESC
        LIMIT 10
    ''', (unit_id,))

    # الحصول على طلبات الصيانة
    maintenance_requests = query_db('''
        SELECT m.*, t.name as tenant_name
        FROM maintenance_requests m
        LEFT JOIN tenants t ON m.tenant_id = t.id
        WHERE m.unit_id = ?
        ORDER BY m.request_date DESC
    ''', (unit_id,))

    # الحصول على المستندات المرتبطة بالوحدة
    documents = query_db('''
        SELECT d.*, u.name as uploaded_by_name
        FROM documents d
        LEFT JOIN users u ON d.uploaded_by = u.id
        WHERE d.related_to = 'unit' AND d.related_id = ?
        ORDER BY d.upload_date DESC
    ''', (unit_id,))

    return render_template('properties/view_unit.html',
                           unit=unit,
                           active_contract=active_contract,
                           contracts_history=contracts_history,
                           meter_readings=meter_readings,
                           maintenance_requests=maintenance_requests,
                           documents=documents,
                           title=f'تفاصيل الوحدة: {unit["unit_number"]} - {unit["building_name"]}')

@properties_bp.route('/units/delete/<int:unit_id>', methods=['POST'])
@login_required
@data_entry_required
def delete_unit(unit_id):
    """حذف وحدة"""
    unit = query_db('SELECT * FROM units WHERE id = ?', (unit_id,), one=True)

    if not unit:
        flash('الوحدة غير موجودة.', 'danger')
        return redirect(url_for('properties.units'))

    # التحقق من وجود عقود مرتبطة بالوحدة
    contracts = query_db('SELECT COUNT(*) as count FROM contracts WHERE unit_id = ?', (unit_id,), one=True)

    if contracts['count'] > 0:
        flash('لا يمكن حذف الوحدة لأنها مرتبطة بعقود. يرجى حذف العقود أولاً.', 'danger')
        return redirect(url_for('properties.view_unit', unit_id=unit_id))

    # حذف قراءات العدادات المرتبطة بالوحدة
    query_db('DELETE FROM meter_readings WHERE unit_id = ?', (unit_id,))

    # حذف طلبات الصيانة المرتبطة بالوحدة
    query_db('DELETE FROM maintenance_requests WHERE unit_id = ?', (unit_id,))

    # حذف المستندات المرتبطة بالوحدة
    query_db('DELETE FROM documents WHERE related_to = "unit" AND related_id = ?', (unit_id,))

    # حذف الوحدة
    delete_db('units', unit_id)

    flash('تم حذف الوحدة بنجاح!', 'success')
    return redirect(url_for('properties.units'))

@properties_bp.route('/meter-readings/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add_meter_reading():
    """إضافة قراءة عداد جديدة"""
    form = MeterReadingForm()

    # تحميل قائمة الوحدات للاختيار
    units = query_db('''
        SELECT u.id, u.unit_number, b.name as building_name
        FROM units u
        JOIN buildings b ON u.building_id = b.id
        ORDER BY b.name, u.unit_number
    ''')
    form.unit_id.choices = [(unit['id'], f"{unit['unit_number']} - {unit['building_name']}") for unit in units]

    if form.validate_on_submit():
        insert_db('meter_readings', {
            'unit_id': form.unit_id.data,
            'reading_date': form.reading_date.data,
            'electricity_reading': form.electricity_reading.data,
            'water_reading': form.water_reading.data,
            'gas_reading': form.gas_reading.data,
            'notes': form.notes.data,
            'created_by': current_user.id,
            'created_at': datetime.now()
        })

        flash('تم إضافة قراءة العداد بنجاح!', 'success')
        return redirect(url_for('properties.view_unit', unit_id=form.unit_id.data))

    # إذا تم تمرير معرف الوحدة في الـ URL
    unit_id = request.args.get('unit_id', type=int)
    if unit_id:
        form.unit_id.data = unit_id

    return render_template('properties/meter_reading_form.html', form=form, title='إضافة قراءة عداد جديدة')

@properties_bp.route('/maintenance/add', methods=['GET', 'POST'])
@login_required
@data_entry_required
def add_maintenance():
    """إضافة طلب صيانة جديد"""
    form = MaintenanceRequestForm()

    # تحميل قائمة الوحدات للاختيار
    units = query_db('''
        SELECT u.id, u.unit_number, b.name as building_name
        FROM units u
        JOIN buildings b ON u.building_id = b.id
        ORDER BY b.name, u.unit_number
    ''')
    form.unit_id.choices = [(unit['id'], f"{unit['unit_number']} - {unit['building_name']}") for unit in units]

    # تحميل قائمة المستأجرين للاختيار
    tenants = query_db('SELECT id, name FROM tenants ORDER BY name')
    form.tenant_id.choices = [(0, 'لا يوجد')] + [(tenant['id'], tenant['name']) for tenant in tenants]

    if form.validate_on_submit():
        data = {
            'unit_id': form.unit_id.data,
            'request_date': form.request_date.data,
            'description': form.description.data,
            'priority': form.priority.data,
            'status': form.status.data,
            'notes': form.notes.data
        }

        if form.tenant_id.data != 0:
            data['tenant_id'] = form.tenant_id.data

        if form.completion_date.data:
            data['completion_date'] = form.completion_date.data

        if form.cost.data:
            data['cost'] = form.cost.data

        maintenance_id = insert_db('maintenance_requests', data)

        flash('تم إضافة طلب الصيانة بنجاح!', 'success')
        return redirect(url_for('properties.view_maintenance', maintenance_id=maintenance_id))

    # إذا تم تمرير معرف الوحدة في الـ URL
    unit_id = request.args.get('unit_id', type=int)
    if unit_id:
        form.unit_id.data = unit_id

        # البحث عن المستأجر النشط للوحدة
        active_tenant = query_db('''
            SELECT t.id
            FROM contracts c
            JOIN tenants t ON c.tenant_id = t.id
            WHERE c.unit_id = ? AND c.status = 'active'
            LIMIT 1
        ''', (unit_id,), one=True)

        if active_tenant:
            form.tenant_id.data = active_tenant['id']

    return render_template('properties/maintenance_form.html', form=form, title='إضافة طلب صيانة جديد')

@properties_bp.route('/maintenance/view/<int:maintenance_id>')
@login_required
def view_maintenance(maintenance_id):
    """عرض تفاصيل طلب الصيانة"""
    maintenance = query_db('''
        SELECT m.*, u.unit_number, b.name as building_name, t.name as tenant_name, t.phone as tenant_phone
        FROM maintenance_requests m
        JOIN units u ON m.unit_id = u.id
        JOIN buildings b ON u.building_id = b.id
        LEFT JOIN tenants t ON m.tenant_id = t.id
        WHERE m.id = ?
    ''', (maintenance_id,), one=True)

    if not maintenance:
        flash('طلب الصيانة غير موجود.', 'danger')
        return redirect(url_for('properties.units'))

    return render_template('properties/view_maintenance.html',
                           maintenance=maintenance,
                           title=f'تفاصيل طلب الصيانة: {maintenance["id"]}')

@properties_bp.route('/maintenance/edit/<int:maintenance_id>', methods=['GET', 'POST'])
@login_required
@data_entry_required
def edit_maintenance(maintenance_id):
    """تعديل طلب صيانة"""
    maintenance = query_db('SELECT * FROM maintenance_requests WHERE id = ?', (maintenance_id,), one=True)

    if not maintenance:
        flash('طلب الصيانة غير موجود.', 'danger')
        return redirect(url_for('properties.units'))

    form = MaintenanceRequestForm()

    # تحميل قائمة الوحدات للاختيار
    units = query_db('''
        SELECT u.id, u.unit_number, b.name as building_name
        FROM units u
        JOIN buildings b ON u.building_id = b.id
        ORDER BY b.name, u.unit_number
    ''')
    form.unit_id.choices = [(unit['id'], f"{unit['unit_number']} - {unit['building_name']}") for unit in units]

    # تحميل قائمة المستأجرين للاختيار
    tenants = query_db('SELECT id, name FROM tenants ORDER BY name')
    form.tenant_id.choices = [(0, 'لا يوجد')] + [(tenant['id'], tenant['name']) for tenant in tenants]

    if request.method == 'GET':
        form.unit_id.data = maintenance['unit_id']
        form.tenant_id.data = maintenance['tenant_id'] if maintenance['tenant_id'] else 0
        form.request_date.data = maintenance['request_date']
        form.description.data = maintenance['description']
        form.priority.data = maintenance['priority']
        form.status.data = maintenance['status']
        form.completion_date.data = maintenance['completion_date']
        form.cost.data = maintenance['cost']
        form.notes.data = maintenance['notes']

    if form.validate_on_submit():
        data = {
            'unit_id': form.unit_id.data,
            'request_date': form.request_date.data,
            'description': form.description.data,
            'priority': form.priority.data,
            'status': form.status.data,
            'notes': form.notes.data
        }

        if form.tenant_id.data != 0:
            data['tenant_id'] = form.tenant_id.data
        else:
            data['tenant_id'] = None

        if form.completion_date.data:
            data['completion_date'] = form.completion_date.data
        else:
            data['completion_date'] = None

        if form.cost.data:
            data['cost'] = form.cost.data
        else:
            data['cost'] = None

        update_db('maintenance_requests', maintenance_id, data)

        flash('تم تحديث طلب الصيانة بنجاح!', 'success')
        return redirect(url_for('properties.view_maintenance', maintenance_id=maintenance_id))

    return render_template('properties/maintenance_form.html', form=form, title='تعديل طلب صيانة', maintenance=maintenance)

@properties_bp.route('/maintenance/delete/<int:maintenance_id>', methods=['POST'])
@login_required
@data_entry_required
def delete_maintenance(maintenance_id):
    """حذف طلب صيانة"""
    maintenance = query_db('SELECT * FROM maintenance_requests WHERE id = ?', (maintenance_id,), one=True)

    if not maintenance:
        flash('طلب الصيانة غير موجود.', 'danger')
        return redirect(url_for('properties.units'))

    # حذف طلب الصيانة
    delete_db('maintenance_requests', maintenance_id)

    flash('تم حذف طلب الصيانة بنجاح!', 'success')
    return redirect(url_for('properties.view_unit', unit_id=maintenance['unit_id']))
