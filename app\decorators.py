from functools import wraps
from flask import flash, redirect, url_for
from flask_login import current_user

def admin_required(f):
    """مصادقة المستخدم المسؤول"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('dashboard.index'))
        return f(*args, **kwargs)
    return decorated_function

def accountant_required(f):
    """مصادقة المستخدم المحاسب"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_accountant():
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('dashboard.index'))
        return f(*args, **kwargs)
    return decorated_function

def data_entry_required(f):
    """مصادقة مستخدم إدخال البيانات"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_data_entry():
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
            return redirect(url_for('dashboard.index'))
        return f(*args, **kwargs)
    return decorated_function
