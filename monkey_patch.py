"""
ملف لتطبيق إصلاحات شاملة للمكتبات المختلفة
"""

import sys
import importlib.util
import os
from urllib.parse import parse_qs, quote, quote_plus, unquote, unquote_plus

def apply_all_patches():
    """
    تطبيق جميع الإصلاحات المطلوبة
    """
    print("Applying all patches...")
    
    # تطبيق إصلاح Werkzeug
    patch_werkzeug()
    
    # تطبيق إصلاح Flask-Login
    patch_flask_login()
    
    print("All patches applied successfully")

def patch_werkzeug():
    """
    تطبيق إصلاح لمشكلة url_decode في Werkzeug
    """
    try:
        print("Patching werkzeug.urls...")
        
        # التحقق من وجود werkzeug
        if importlib.util.find_spec("werkzeug") is None:
            print("Werkzeug not found, skipping patch")
            return
        
        # استيراد werkzeug.urls
        import werkzeug.urls
        
        # التحقق مما إذا كانت url_decode غير موجودة
        if not hasattr(werkzeug.urls, 'url_decode'):
            print("Adding url_decode to werkzeug.urls")
            
            # إضافة url_decode كدالة بديلة
            def url_decode(qs, charset='utf-8', decode_keys=False, include_empty=True, errors='replace', separator='&', cls=None):
                """تنفيذ بديل لـ url_decode"""
                try:
                    from werkzeug.datastructures import MultiDict
                    result_cls = MultiDict
                except ImportError:
                    result_cls = dict
                
                if cls is not None:
                    result_cls = cls
                
                result = result_cls()
                parsed = parse_qs(qs, keep_blank_values=include_empty, encoding=charset, errors=errors)
                
                for key, values in parsed.items():
                    for value in values:
                        if hasattr(result, 'add'):
                            result.add(key, value)
                        else:
                            result[key] = value
                
                return result
            
            werkzeug.urls.url_decode = url_decode
            print("Added url_decode to werkzeug.urls")
        
        # التحقق من وجود دوال أخرى مفقودة
        missing_functions = []
        for func_name in ['url_encode', 'url_quote', 'url_quote_plus', 'url_unquote', 'url_unquote_plus']:
            if not hasattr(werkzeug.urls, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"Adding missing functions to werkzeug.urls: {', '.join(missing_functions)}")
            
            if 'url_quote' in missing_functions:
                werkzeug.urls.url_quote = quote
                print("Added url_quote")
            
            if 'url_quote_plus' in missing_functions:
                werkzeug.urls.url_quote_plus = quote_plus
                print("Added url_quote_plus")
            
            if 'url_unquote' in missing_functions:
                werkzeug.urls.url_unquote = unquote
                print("Added url_unquote")
            
            if 'url_unquote_plus' in missing_functions:
                werkzeug.urls.url_unquote_plus = unquote_plus
                print("Added url_unquote_plus")
            
            if 'url_encode' in missing_functions:
                def url_encode(obj, charset='utf-8', encode_keys=False, sort=False, key=None, separator='&'):
                    """تنفيذ بسيط لـ url_encode"""
                    pairs = []
                    
                    # تحويل obj إلى قائمة من الأزواج
                    if hasattr(obj, 'items'):
                        items = list(obj.items())
                    else:
                        items = list(obj)
                    
                    if sort:
                        items.sort(key=key)
                    
                    for key, value in items:
                        # تشفير القيم
                        try:
                            key = quote_plus(str(key), encoding=charset)
                            value = quote_plus(str(value), encoding=charset)
                            pairs.append(f"{key}={value}")
                        except Exception:
                            pass
                    
                    return separator.join(pairs)
                
                werkzeug.urls.url_encode = url_encode
                print("Added url_encode")
        
        print("Werkzeug patch applied successfully")
        return True
    
    except Exception as e:
        print(f"Error applying werkzeug patch: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def patch_flask_login():
    """
    تطبيق إصلاح لمشكلة flask_login.utils
    """
    try:
        print("Patching flask_login.utils...")
        
        # التحقق من وجود flask_login
        if importlib.util.find_spec("flask_login") is None:
            print("Flask-Login not found, skipping patch")
            return
        
        # التحقق من وجود flask_login.utils
        if importlib.util.find_spec("flask_login.utils") is None:
            print("flask_login.utils not found, skipping patch")
            return
        
        # استيراد flask_login.utils
        import flask_login.utils
        
        # التحقق من وجود _create_identifier
        if not hasattr(flask_login.utils, '_create_identifier'):
            print("_create_identifier not found in flask_login.utils, applying patch")
            
            # تطبيق إصلاح flask_login.utils
            import hashlib
            from flask import request
            
            def _create_identifier():
                user_agent = request.headers.get('User-Agent', '')
                if user_agent:
                    user_agent = user_agent.encode('utf-8')
                base = f"{request.remote_addr}|{user_agent}"
                if str is bytes:
                    base = base.encode('utf8')
                h = hashlib.md5()
                h.update(base)
                return h.hexdigest()
            
            flask_login.utils._create_identifier = _create_identifier
            print("Added _create_identifier to flask_login.utils")
        
        print("Flask-Login patch applied successfully")
        return True
    
    except Exception as e:
        print(f"Error applying Flask-Login patch: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# تطبيق الإصلاحات عند استيراد الملف
apply_all_patches()
