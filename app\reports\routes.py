from flask import render_template, redirect, url_for, flash, request, send_file, make_response
from flask_login import login_required, current_user
from app.reports import reports_bp
from app.decorators import accountant_required
from app.db import query_db
from datetime import datetime
import csv
import io
import pdfkit

@reports_bp.route('/')
@login_required
@accountant_required
def index():
    """صفحة التقارير الرئيسية"""
    return render_template('reports/index.html', title='التقارير')

@reports_bp.route('/financial_report')
@login_required
@accountant_required
def financial_report():
    """تصدير التقرير المالي"""
    # الحصول على معلمات التصفية
    period = request.args.get('period', 'month')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # تحديد نطاق التاريخ بناءً على الفترة المحددة
    today = datetime.now()

    if period == 'month':
        # الشهر الحالي
        date_from = datetime(today.year, today.month, 1).strftime('%Y-%m-%d')
        if today.month == 12:
            date_to = datetime(today.year + 1, 1, 1).strftime('%Y-%m-%d')
        else:
            date_to = datetime(today.year, today.month + 1, 1).strftime('%Y-%m-%d')
    elif period == 'quarter':
        # الربع الحالي
        quarter = (today.month - 1) // 3 + 1
        date_from = datetime(today.year, (quarter - 1) * 3 + 1, 1).strftime('%Y-%m-%d')
        if quarter == 4:
            date_to = datetime(today.year + 1, 1, 1).strftime('%Y-%m-%d')
        else:
            date_to = datetime(today.year, quarter * 3 + 1, 1).strftime('%Y-%m-%d')
    elif period == 'year':
        # السنة الحالية
        date_from = datetime(today.year, 1, 1).strftime('%Y-%m-%d')
        date_to = datetime(today.year + 1, 1, 1).strftime('%Y-%m-%d')

    # استعلام الإيرادات
    income_query = '''
        SELECT t.id, t.transaction_date, t.amount, t.category, t.description
        FROM transactions t
        WHERE t.type = 'income'
    '''

    # استعلام المصروفات
    expense_query = '''
        SELECT t.id, t.transaction_date, t.amount, t.category, t.description
        FROM transactions t
        WHERE t.type = 'expense'
    '''

    # إضافة شروط التاريخ إذا تم تحديدها
    params = []
    if date_from:
        income_query += ' AND t.transaction_date >= ?'
        expense_query += ' AND t.transaction_date >= ?'
        params.append(date_from)

    if date_to:
        income_query += ' AND t.transaction_date < ?'
        expense_query += ' AND t.transaction_date < ?'
        params.append(date_to)

    # إضافة الترتيب
    income_query += ' ORDER BY t.transaction_date DESC'
    expense_query += ' ORDER BY t.transaction_date DESC'

    # تنفيذ الاستعلامات
    income_transactions = query_db(income_query, params)
    expense_transactions = query_db(expense_query, params)

    # إنشاء ملف CSV
    output = io.StringIO()
    writer = csv.writer(output)

    # كتابة رأس التقرير
    writer.writerow(['التقرير المالي'])
    writer.writerow(['الفترة:', period])
    if date_from:
        writer.writerow(['من تاريخ:', date_from])
    if date_to:
        writer.writerow(['إلى تاريخ:', date_to])
    writer.writerow([])

    # كتابة معلومات الإيرادات
    writer.writerow(['الإيرادات'])
    writer.writerow(['رقم المعاملة', 'التاريخ', 'المبلغ', 'الفئة', 'الوصف', 'المبنى', 'الوحدة', 'المستأجر'])

    total_income = 0
    for transaction in income_transactions:
        writer.writerow([
            transaction['id'],
            transaction['transaction_date'],
            transaction['amount'],
            transaction['category'],
            transaction['description'],
            '',  # building_name
            '',  # unit_number
            ''   # tenant_name
        ])
        total_income += transaction['amount']

    writer.writerow(['إجمالي الإيرادات:', total_income])
    writer.writerow([])

    # كتابة معلومات المصروفات
    writer.writerow(['المصروفات'])
    writer.writerow(['رقم المعاملة', 'التاريخ', 'المبلغ', 'الفئة', 'الوصف', 'المبنى', 'الوحدة', 'المستأجر'])

    total_expense = 0
    for transaction in expense_transactions:
        writer.writerow([
            transaction['id'],
            transaction['transaction_date'],
            transaction['amount'],
            transaction['category'],
            transaction['description'],
            '',  # building_name
            '',  # unit_number
            ''   # tenant_name
        ])
        total_expense += transaction['amount']

    writer.writerow(['إجمالي المصروفات:', total_expense])
    writer.writerow([])

    # كتابة الملخص
    writer.writerow(['الملخص'])
    writer.writerow(['إجمالي الإيرادات:', total_income])
    writer.writerow(['إجمالي المصروفات:', total_expense])
    writer.writerow(['صافي الربح:', total_income - total_expense])

    # إعداد الاستجابة
    output.seek(0)

    # تحديد اسم الملف بناءً على الفترة
    if period == 'month':
        filename = f'financial_report_month_{today.year}_{today.month}.csv'
    elif period == 'quarter':
        quarter = (today.month - 1) // 3 + 1
        filename = f'financial_report_quarter_{today.year}_{quarter}.csv'
    elif period == 'year':
        filename = f'financial_report_year_{today.year}.csv'
    else:
        filename = f'financial_report_custom_{today.strftime("%Y%m%d")}.csv'

    # إرسال الملف
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=filename
    )

@reports_bp.route('/receipt/<int:transaction_id>')
@login_required
@accountant_required
def print_receipt(transaction_id):
    """طباعة إيصال للمعاملة المالية"""
    # الحصول على بيانات المعاملة
    transaction = query_db('''
        SELECT t.*, u.name as created_by_name,
               CASE
                   WHEN t.related_to = 'contract' THEN (
                       SELECT c.contract_number || ' - ' || te.name || ' (' || un.unit_number || ')'
                       FROM contracts c
                       JOIN tenants te ON c.tenant_id = te.id
                       JOIN units un ON c.unit_id = un.id
                       WHERE c.id = t.related_id
                   )
                   WHEN t.related_to = 'building' THEN (SELECT name FROM buildings WHERE id = t.related_id)
                   WHEN t.related_to = 'unit' THEN (
                       SELECT u.unit_number || ' - ' || b.name
                       FROM units u
                       JOIN buildings b ON u.building_id = b.id
                       WHERE u.id = t.related_id
                   )
                   ELSE NULL
               END as related_name
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        WHERE t.id = ?
    ''', (transaction_id,), one=True)

    if not transaction:
        return render_template('errors/404.html', message='المعاملة المالية غير موجودة.'), 404

    # الحصول على بيانات العقد إذا كانت المعاملة مرتبطة بعقد
    contract = None
    if transaction['related_to'] == 'contract':
        contract = query_db('''
            SELECT c.*, t.name as tenant_name, u.unit_number, b.name as building_name
            FROM contracts c
            JOIN tenants t ON c.tenant_id = t.id
            JOIN units u ON c.unit_id = u.id
            JOIN buildings b ON u.building_id = b.id
            WHERE c.id = ?
        ''', (transaction['related_id'],), one=True)

    # إنشاء قالب الإيصال
    html = render_template(
        'reports/receipt.html',
        transaction=transaction,
        contract=contract,
        title=f'إيصال المعاملة المالية #{transaction["id"]}'
    )

    try:
        # محاولة تحويل HTML إلى PDF باستخدام pdfkit
        pdf = pdfkit.from_string(html, False)

        # إنشاء استجابة مع ملف PDF
        response = make_response(pdf)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'inline; filename=receipt_{transaction_id}.pdf'

        return response
    except Exception as e:
        # في حالة فشل إنشاء PDF، عرض HTML مباشرة
        print(f"Error generating PDF: {str(e)}")
        return html
