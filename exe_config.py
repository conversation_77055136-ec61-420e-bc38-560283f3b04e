#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات إنشاء الملف التنفيذي - مكتب عصام الفت لإدارة الأملاك

تم التطوير والبرمجة بواسطة شركة المبرمج المصري
فيسبوك: https://www.facebook.com/almbarmg
واتساب: 0201032540807
جميع الحقوق محفوظة © 2025
"""

import os
import sys
from pathlib import Path

# المسار الأساسي للمشروع
BASE_DIR = Path(__file__).parent

# إعدادات PyInstaller
PYINSTALLER_CONFIG = {
    # الملف الرئيسي
    'main_script': 'server_manager.py',
    
    # اسم الملف التنفيذي
    'exe_name': 'مكتب_عصام_الفت',
    
    # نوع البناء
    'build_type': 'onedir',  # أو 'onefile'
    
    # واجهة المستخدم
    'console': False,  # False للواجهة الرسومية، True لوحدة التحكم
    'windowed': True,  # True للواجهة الرسومية
    
    # الأيقونة
    'icon': 'file.ico',
    
    # الملفات والمجلدات المطلوبة
    'add_data': [
        ('app', 'app'),
        ('backend', 'backend'),
        ('config.py', '.'),
        ('schema.sql', '.'),
        ('file.ico', '.'),
        ('file.jpeg', '.'),
        ('requirements.txt', '.'),
    ],
    
    # المكتبات المخفية
    'hidden_imports': [
        # Flask والمكتبات الأساسية
        'flask',
        'flask.app',
        'flask.blueprints',
        'flask.cli',
        'flask.config',
        'flask.ctx',
        'flask.globals',
        'flask.helpers',
        'flask.json',
        'flask.logging',
        'flask.sessions',
        'flask.signals',
        'flask.templating',
        'flask.testing',
        'flask.views',
        'flask.wrappers',
        
        # Flask Extensions
        'flask_login',
        'flask_bcrypt',
        'flask_wtf',
        'flask_wtf.csrf',
        'flask_mail',
        
        # Werkzeug
        'werkzeug',
        'werkzeug.serving',
        'werkzeug.urls',
        'werkzeug.utils',
        'werkzeug.wrappers',
        'werkzeug.exceptions',
        'werkzeug.routing',
        'werkzeug.security',
        'werkzeug.datastructures',
        'werkzeug.http',
        'werkzeug.local',
        'werkzeug.middleware',
        
        # Jinja2
        'jinja2',
        'jinja2.environment',
        'jinja2.loaders',
        'jinja2.runtime',
        'jinja2.utils',
        'jinja2.filters',
        'jinja2.tests',
        'jinja2.ext',
        
        # WTForms
        'wtforms',
        'wtforms.fields',
        'wtforms.form',
        'wtforms.validators',
        'wtforms.widgets',
        'wtforms.csrf',
        
        # PyQt5
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        
        # مكتبات أخرى
        'email_validator',
        'itsdangerous',
        'markupsafe',
        'bcrypt',
        'sqlite3',
        'qrcode',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        
        # مكتبات النظام
        'datetime',
        'os',
        'sys',
        'json',
        'base64',
        'hashlib',
        'secrets',
        'uuid',
        'pathlib',
        'shutil',
        'tempfile',
        'io',
        'collections',
        'functools',
        'itertools',
        're',
        'string',
        'time',
        'calendar',
        'decimal',
        'math',
        'random',
        'platform',
        'signal',
        'argparse',
        'configparser',
        'csv',
        'zipfile',
        'tarfile',
        'gzip',
        'bz2',
        'codecs',
        'locale',
        'gettext',
        'unicodedata',
        'encodings',
        'encodings.utf_8',
        'encodings.cp1256',
        'encodings.ascii',
        'encodings.latin1',
        'urllib',
        'urllib.parse',
        'socket',
        'threading',
        'subprocess',
        'webbrowser',
        'logging',
        'traceback',
        'importlib',
        'importlib.util',
        
        # مكتبات التطبيق
        'app',
        'app.auth',
        'app.dashboard',
        'app.owners',
        'app.properties',
        'app.tenants',
        'app.documents',
        'app.finance',
        'app.reports',
        'app.db',
        'app.models',
        'app.forms',
        'app.utils',
        'app.decorators',
        'backend',
        'backend.app',
    ],
    
    # المكتبات المستبعدة
    'excludes': [
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'pytest',
        'setuptools',
        'distutils',
        'pip',
        'wheel',
        'pkg_resources',
        'test',
        'tests',
        'unittest',
        'doctest',
        'pdb',
        'pydoc',
        'xmlrpc',
        'xml.etree',
        'xml.dom',
        'xml.sax',
        'html.parser',
        'http.server',
        'http.client',
        'urllib.robotparser',
        'urllib.request',
        'urllib.error',
        'ftplib',
        'poplib',
        'imaplib',
        'nntplib',
        'smtplib',
        'telnetlib',
        'uuid',
        'pickle',
        'shelve',
        'dbm',
        'sqlite3.dump',
        'multiprocessing',
        'concurrent',
        'asyncio',
        'queue',
        'sched',
        'threading',
        'dummy_threading',
        '_thread',
        'pty',
        'tty',
        'pipes',
        'posix',
        'pwd',
        'spwd',
        'grp',
        'crypt',
        'termios',
        'resource',
        'nis',
        'syslog',
        'commands',
        'dl',
        'DLFCN',
        'ossaudiodev',
        'audioop',
        'imageop',
        'aifc',
        'sunau',
        'wave',
        'chunk',
        'colorsys',
        'imghdr',
        'sndhdr',
        'turtle',
        'cmd',
        'shlex',
    ],
    
    # خيارات إضافية
    'options': [
        '--clean',
        '--noconfirm',
        '--log-level=WARN',
        '--strip',
        '--noupx',
    ],
    
    # خيارات Windows Defender
    'windows_defender_options': [
        '--exclude-module=tkinter',
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=pandas',
        '--exclude-module=test',
        '--exclude-module=tests',
        '--exclude-module=unittest',
        '--exclude-module=pytest',
    ]
}

# إعدادات خاصة بـ Windows
WINDOWS_CONFIG = {
    'version_info': {
        'version': '*******',
        'company_name': 'شركة المبرمج المصري',
        'file_description': 'مكتب عصام الفت لإدارة الأملاك',
        'internal_name': 'مكتب_عصام_الفت',
        'legal_copyright': 'جميع الحقوق محفوظة © 2025',
        'original_filename': 'مكتب_عصام_الفت.exe',
        'product_name': 'مكتب عصام الفت لإدارة الأملاك',
        'product_version': '*******'
    }
}

def get_pyinstaller_command():
    """إنشاء أمر PyInstaller"""
    config = PYINSTALLER_CONFIG
    
    cmd = ['python', '-m', 'PyInstaller']
    
    # نوع البناء
    if config['build_type'] == 'onefile':
        cmd.append('--onefile')
    else:
        cmd.append('--onedir')
    
    # واجهة المستخدم
    if config['windowed']:
        cmd.append('--windowed')
    elif config['console']:
        cmd.append('--console')
    
    # الاسم والأيقونة
    cmd.extend(['--name', config['exe_name']])
    if config['icon'] and os.path.exists(config['icon']):
        cmd.extend(['--icon', config['icon']])
    
    # الملفات والمجلدات
    for data_item in config['add_data']:
        if isinstance(data_item, tuple) and len(data_item) == 2:
            src, dst = data_item
            if os.path.exists(src):
                cmd.extend(['--add-data', f'{src};{dst}'])
    
    # المكتبات المخفية
    for hidden_import in config['hidden_imports']:
        cmd.extend(['--hidden-import', hidden_import])
    
    # المكتبات المستبعدة
    for exclude in config['excludes']:
        cmd.extend(['--exclude-module', exclude])
    
    # خيارات إضافية
    cmd.extend(config['options'])
    cmd.extend(config['windows_defender_options'])
    
    # الملف الرئيسي
    cmd.append(config['main_script'])
    
    return cmd

if __name__ == '__main__':
    cmd = get_pyinstaller_command()
    print("PyInstaller command:")
    print(' '.join(cmd))
