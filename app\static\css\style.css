/* الخط الأساسي */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* تنسيق الهيدر */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
}

/* تنسيق البطاقات */
.card {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    font-weight: bold;
    border-bottom: 0;
}

/* تنسيق الجداول */
.table th {
    font-weight: bold;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* تنسيق الأزرار */
.btn {
    border-radius: 5px;
    padding: 0.375rem 1rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
}

/* تنسيق النماذج */
.form-control {
    border-radius: 5px;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تنسيق الفوتر */
.footer {
    border-top: 1px solid #e9ecef;
}

/* تنسيق الصفحات */
.page-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
}

/* تنسيق الإشعارات */
.alert {
    border-radius: 10px;
}

/* تنسيق الشارات */
.badge {
    padding: 0.5em 0.8em;
    border-radius: 5px;
}

/* تنسيق الصور */
.img-thumbnail {
    border-radius: 10px;
}

/* تنسيق الملفات */
.document-card {
    transition: transform 0.2s;
}

.document-card:hover {
    transform: translateY(-5px);
}

/* تنسيق الرسوم البيانية */
.chart-container {
    position: relative;
    margin: auto;
    height: 300px;
}

/* تنسيق الأيقونات */
.icon-large {
    font-size: 2rem;
}

.icon-medium {
    font-size: 1.5rem;
}

/* تنسيق الصفحات الخاصة */
.login-page {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        background-color: white;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
