{% extends "base.html" %}

{% block title %}إدارة المالية - نظام إدارة العقارات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-0">إدارة المالية</h1>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('finance.add_transaction') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>إضافة معاملة جديدة
        </a>
        <a href="{{ url_for('finance.summary') }}" class="btn btn-info">
            <i class="fas fa-chart-pie me-1"></i>الملخص المالي
        </a>
    </div>
</div>

<!-- الملخص المالي -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>إجمالي الإيرادات ({{ current_month }})
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="text-success">{{ monthly_income|format_currency }}</h3>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>إجمالي المصروفات ({{ current_month }})
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="text-danger">{{ monthly_expense|format_currency }}</h3>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>صافي الربح ({{ current_month }})
                </h5>
            </div>
            <div class="card-body text-center">
                <h3 class="{% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ net_profit|format_currency }}
                </h3>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- آخر المعاملات المالية -->
    <div class="col-md-8 mb-3">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-check-alt me-2"></i>آخر المعاملات المالية
                </h5>
            </div>
            <div class="card-body">
                {% if latest_transactions %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الفئة</th>
                                <th>المبلغ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in latest_transactions %}
                            <tr>
                                <td>{{ transaction.transaction_date|format_date }}</td>
                                <td>
                                    {% if transaction.type == 'income' %}
                                    <span class="badge bg-success">إيراد</span>
                                    {% else %}
                                    <span class="badge bg-danger">مصروف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if transaction.category == 'rent' %}
                                    إيجار
                                    {% elif transaction.category == 'deposit' %}
                                    تأمين
                                    {% elif transaction.category == 'maintenance' %}
                                    صيانة
                                    {% elif transaction.category == 'utilities' %}
                                    مرافق
                                    {% elif transaction.category == 'taxes' %}
                                    ضرائب
                                    {% elif transaction.category == 'insurance' %}
                                    تأمين
                                    {% elif transaction.category == 'salary' %}
                                    رواتب
                                    {% elif transaction.category == 'commission' %}
                                    عمولة
                                    {% else %}
                                    أخرى
                                    {% endif %}
                                </td>
                                <td>{{ transaction.amount|format_currency }}</td>
                                <td>
                                    <a href="{{ url_for('finance.view_transaction', transaction_id=transaction.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-3 text-center">
                    <a href="{{ url_for('finance.transactions') }}" class="btn btn-outline-primary">
                        عرض كل المعاملات
                    </a>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد معاملات مالية حتى الآن. <a href="{{ url_for('finance.add_transaction') }}">إضافة معاملة جديدة</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- إحصائيات الإيرادات والمصروفات -->
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>إحصائيات الإيرادات
                </h5>
            </div>
            <div class="card-body">
                {% if income_by_category %}
                <canvas id="incomeChart" height="200"></canvas>
                {% else %}
                <div class="alert alert-info">
                    لا توجد بيانات إيرادات لهذا الشهر.
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>إحصائيات المصروفات
                </h5>
            </div>
            <div class="card-body">
                {% if expense_by_category %}
                <canvas id="expenseChart" height="200"></canvas>
                {% else %}
                <div class="alert alert-info">
                    لا توجد بيانات مصروفات لهذا الشهر.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% if income_by_category %}
        // رسم بياني للإيرادات
        var incomeCtx = document.getElementById('incomeChart').getContext('2d');
        var incomeChart = new Chart(incomeCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for item in income_by_category %}
                    '{{ item.category }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in income_by_category %}
                        {{ item.total }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 205, 86, 0.7)',
                        'rgba(201, 203, 207, 0.7)'
                    ],
                    borderColor: [
                        'rgb(75, 192, 192)',
                        'rgb(54, 162, 235)',
                        'rgb(153, 102, 255)',
                        'rgb(255, 159, 64)',
                        'rgb(255, 99, 132)',
                        'rgb(255, 205, 86)',
                        'rgb(201, 203, 207)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
        {% endif %}
        
        {% if expense_by_category %}
        // رسم بياني للمصروفات
        var expenseCtx = document.getElementById('expenseChart').getContext('2d');
        var expenseChart = new Chart(expenseCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for item in expense_by_category %}
                    '{{ item.category }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for item in expense_by_category %}
                        {{ item.total }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 205, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(201, 203, 207, 0.7)'
                    ],
                    borderColor: [
                        'rgb(255, 99, 132)',
                        'rgb(255, 159, 64)',
                        'rgb(255, 205, 86)',
                        'rgb(75, 192, 192)',
                        'rgb(54, 162, 235)',
                        'rgb(153, 102, 255)',
                        'rgb(201, 203, 207)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
        {% endif %}
    });
</script>
{% endblock %}
